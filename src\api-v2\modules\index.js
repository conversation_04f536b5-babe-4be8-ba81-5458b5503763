/**
 * API模块入口文件 - V2版本
 * 统一导出所有业务模块
 * 根据菜单配置扩展的完整业务模块
 */

// 服务项目相关模块
import service from './service'         // 服务项目管理模块（整合版：包含服务、轮播图、服务点、金刚区、服务分类）
import category from './category'       // 分类管理模块

// 师傅管理模块
import technician from './technician'   // 师傅管理模块

// 营销管理模块
import market from './market'           // 营销管理模块

// 订单管理模块
import shop from './shop'               // 订单管理模块

// 分销管理模块
import distribution from './distribution' // 分销管理模块

// 财务管理模块
import finance from './finance'         // 财务管理模块

// 用户管理模块
import user from './user'               // 用户管理模块

// 账号设置模块
import account from './account'         // 账号设置模块
import role from './role'               // 角色管理模块
import admin from './admin'             // 管理员管理模块

// 系统设置模块
import sys from './sys'                 // 系统设置模块

// 日志管理模块
import log from './log'                 // 日志管理模块

// 基础功能模块
import base from './base'               // 基础模块（登录、配置等）
import system from './system'          // 系统管理模块
import upload from './upload'          // 文件上传模块

// 统计分析模块
import statistics from './statistics'  // 统计模块

// 模块集合 - 按业务分类组织
let modules = {
  // 服务项目相关
  service,     // 服务项目管理模块（整合版：包含服务、轮播图、服务点、金刚区、服务分类）
  category,    // 分类管理模块

  // 人员管理
  technician,  // 师傅管理模块
  user,        // 用户管理模块

  // 营销管理
  market,      // 营销管理模块

  // 订单管理
  shop,        // 订单管理模块

  // 分销管理
  distribution, // 分销管理模块

  // 财务管理
  finance,     // 财务管理模块

  // 账号设置
  account,     // 账号设置模块
  role,        // 角色管理模块
  admin,       // 管理员管理模块

  // 系统设置
  sys,         // 系统设置模块

  // 日志管理
  log,         // 日志管理模块

  // 基础功能
  base,        // 基础模块（登录、配置等）
  system,      // 系统管理模块
  upload,      // 文件上传模块

  // 统计分析
  statistics   // 统计模块
}

// 统一导出所有模块
export default {
  ...modules
}
