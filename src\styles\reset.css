/**
 * 现代CSS重置样式
 *  /src/style/reset.css的现代化重构
 * 
 * 目标：
 * - 消除浏览器默认样式差异
 * - 建立一致的基础样式
 * - 优化盒模型和布局
 * - 提升可访问性
 */

/* ===== 现代CSS重置 ===== */

/* 使用border-box盒模型 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 移除默认边距和内边距 */
* {
  margin: 0;
  padding: 0;
}

/* HTML和body基础设置 */
html {
  line-height: 1.15; /* 修复iOS中的行高问题 */
  -webkit-text-size-adjust: 100%; /* 防止iOS中的字体大小调整 */
  -webkit-tap-highlight-color: transparent; /* 移除iOS中的点击高亮 */
  scroll-behavior: smooth; /* 平滑滚动 */
}

body {
  margin: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--bg-color-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden; /* 防止水平滚动条 */
}

/* 标题元素 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 500;
  line-height: 1.2;
  color: var(--color-text-primary);
}

h1 { font-size: var(--font-size-xxl); }
h2 { font-size: var(--font-size-xl); }
h3 { font-size: var(--font-size-lg); }
h4 { font-size: var(--font-size-md); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

/* 段落和文本 */
p {
  margin: 0;
  line-height: 1.6;
}

/* 链接 */
a {
  color: var(--color-primary);
  text-decoration: none;
  background-color: transparent;
  transition: var(--transition-base);
}

a:hover {
  color: var(--color-primary-dark-1);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

a:focus:not(:focus-visible) {
  outline: none;
}

/* 列表 */
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

li {
  margin: 0;
  padding: 0;
}

/* 表格 */
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  background-color: var(--bg-color-base);
}

th, td {
  padding: var(--table-cell-padding);
  text-align: left;
  vertical-align: middle;
  border-bottom: 1px solid var(--table-border-color);
}

th {
  font-weight: 500;
  color: var(--color-text-primary);
  background-color: var(--table-header-bg);
}

/* 表单元素 */
button,
input,
optgroup,
select,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
  border: none;
  background: none;
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/* 输入框样式 - 完全排除 Element Plus 组件 */
/* 使用更精确的选择器，只针对非 Element Plus 的原生表单元素 */
input[type="text"]:not([class*="el-"]),
input[type="password"]:not([class*="el-"]),
input[type="email"]:not([class*="el-"]),
input[type="number"]:not([class*="el-"]),
input[type="tel"]:not([class*="el-"]),
input[type="url"]:not([class*="el-"]),
input[type="search"]:not([class*="el-"]),
input[type="date"]:not([class*="el-"]),
input[type="time"]:not([class*="el-"]),
input[type="datetime-local"]:not([class*="el-"]),
textarea:not([class*="el-"]),
select:not([class*="el-"]) {
  border: 1px solid var(--input-border-color);
  border-radius: var(--input-border-radius);
  padding: var(--form-input-padding);
  background-color: var(--bg-color-base);
  transition: var(--transition-border);
  width: 100%;
  height: var(--input-height);
}

input[type="text"]:not([class*="el-"]):focus,
input[type="password"]:not([class*="el-"]):focus,
input[type="email"]:not([class*="el-"]):focus,
input[type="number"]:not([class*="el-"]):focus,
input[type="tel"]:not([class*="el-"]):focus,
input[type="url"]:not([class*="el-"]):focus,
input[type="search"]:not([class*="el-"]):focus,
input[type="date"]:not([class*="el-"]):focus,
input[type="time"]:not([class*="el-"]):focus,
input[type="datetime-local"]:not([class*="el-"]):focus,
textarea:not([class*="el-"]):focus,
select:not([class*="el-"]):focus {
  outline: none;
  border-color: var(--input-focus-border-color);
  box-shadow: 0 0 0 2px rgba(96, 155, 235, 0.2);
}

textarea {
  resize: vertical;
  min-height: 80px;
  height: auto;
  line-height: 1.5;
}

/* 禁用状态 */
button:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  opacity: var(--opacity-disabled);
  cursor: not-allowed;
  background-color: var(--input-disabled-bg);
}

/* 图片和媒体 */
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  max-width: 100%;
  height: auto;
}

img {
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

/* 代码 */
code,
kbd,
pre,
samp {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.875em;
}

pre {
  overflow: auto;
  padding: var(--spacing-md);
  background-color: var(--bg-color-column);
  border-radius: var(--border-radius-sm);
  line-height: 1.4;
}

code {
  padding: 2px 4px;
  background-color: var(--bg-color-column);
  border-radius: var(--border-radius-xs);
  color: var(--color-danger);
  font-size: 0.9em;
}

pre code {
  padding: 0;
  background-color: transparent;
  color: inherit;
  font-size: inherit;
}

/* 引用 */
blockquote {
  margin: 0;
  padding: var(--spacing-md);
  border-left: 4px solid var(--color-primary);
  background-color: var(--bg-color-column);
  color: var(--color-text-secondary);
  font-style: italic;
}

/* 分隔线 */
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
  border: 0;
  border-top: 1px solid var(--border-color-base);
  margin: var(--spacing-lg) 0;
}

/* 细节和摘要 */
details {
  display: block;
}

summary {
  display: list-item;
  cursor: pointer;
  font-weight: 500;
}

/* 模板 */
template {
  display: none;
}

/* 隐藏属性 */
[hidden] {
  display: none !important;
}

/* 可访问性 */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 焦点样式 */
:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* 选中文本样式 */
::selection {
  background-color: var(--color-primary);
  color: #ffffff;
}

::-moz-selection {
  background-color: var(--color-primary);
  color: #ffffff;
}
