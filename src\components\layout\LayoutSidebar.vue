<!--
  LayoutSidebar 组件
 /src/components/sidebar.vue的布局结构重构

  功能：
  - 双栏布局：主菜单(120px) + 子菜单(159px)
  - 与原系统完全一致的交互逻辑
  - 路由导航和权限控制
  - 保持原有的视觉效果和用户体验
-->

<template>
  <div class="lb-sidebar">
    <!-- 主菜单栏 -->
    <div class="menu">
      <div class="menu-scroll">
        <ul class="menu-top">
          <li
            v-for="(item, index) in routes"
            :key="index"
            :class="{ 'menu-active': currentMenu === item.path }"
            @click="handleMenuClick(item)"
          >
            <el-icon v-if="item.icon" :size="20">
              <component :is="item.icon" />
            </el-icon>
            <span class="menu-text">{{ item.name }}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- 子菜单栏 -->
    <div :class="subnav.length > 0 ? 'isopen' : ''" class="submenu">
      <div class="submenu-scroll">
        <el-collapse v-model="activeNames">
          <el-collapse-item
            v-for="(group, index) in subnav"
            :key="index"
            :title="group.name"
            :name="index.toString()"
          >
            <div class="item" v-for="(item, itemIndex) in group.url" :key="itemIndex">
              <span
                :class="{ 'el-collapse-item-active': $route.path === item.url }"
                @click="handleSubmenuClick(item)"
              >
                {{ item.name }}
              </span>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { refreshUtils } from '@/utils/eventBus'

const store = useStore()
const route = useRoute()
const router = useRouter()

// 响应式数据
const userInfo = ref({})
const routes = ref([]) // 动态菜单列表
const subnav = ref([]) // 二级菜单表
const activeNames = ref([]) // 二级菜单展开的配置
const currentMenu = ref('') // 当前选中的主菜单

// 计算属性
const mainMenuList = computed(() => store.getters['menu/mainMenuList'])
const submenuMap = computed(() => store.getters['menu/submenuMap'])
const menuLoaded = computed(() => store.getters['menu/menuLoaded'])

// 方法
const getSubmenuByPath = (path) => {
  return submenuMap.value[path] || []
}

const hasSubmenu = (path) => {
  const submenu = submenuMap.value[path]
  return submenu && submenu.length > 0
}

const getDefaultRoute = (path) => {
  const submenu = submenuMap.value[path]
  if (submenu && submenu.length > 0 && submenu[0].url && submenu[0].url.length > 0) {
    return submenu[0].url[0].url
  }
  return path
}

const getMainMenuByRoute = (currentRoute) => {
  for (const menu of mainMenuList.value) {
    if (currentRoute.startsWith(menu.path)) {
      return menu.path
    }
  }
  return mainMenuList.value.length > 0 ? mainMenuList.value[0].path : '/service'
}

/**
 * 处理主菜单点击事件
 * 基于新的菜单配置系统，点击父类自动跳转到第一个子类
 */
const handleMenuClick = (menuItem) => {
  console.log('点击菜单:', menuItem.name, menuItem.path)

  // 更新当前选中的主菜单
  currentMenu.value = menuItem.path

  // 特殊处理：用户管理模块直接跳转，无子菜单
  if (menuItem.path === '/custom') {
    router.push('/user/list')
    store.commit('ui/SET_SIDEBAR_SUBMENU_OPEN', false)
    subnav.value = []
    return
  }

  // 加载对应的子菜单
  loadSubmenu(menuItem.path)

  // 自动跳转到第一个子菜单项
  navigateToFirstSubmenu(menuItem.path)

  // 更新UI状态
  store.commit('ui/SET_SIDEBAR_SUBMENU_OPEN', subnav.value.length > 0)

  // 保存当前菜单状态到本地存储
  localStorage.setItem('currentMenu', menuItem.path)
}

/**
 * 加载子菜单数据
 * 使用新的菜单配置系统，包含性能优化
 */
const loadSubmenu = (menuPath) => {
  const startTime = performance.now()

  const submenuData = getSubmenuByPath(menuPath)
  subnav.value = submenuData

  // 默认展开所有子菜单分组
  if (submenuData.length > 0) {
    openSubnav()
  }

  // 更新Vuex状态 - 关键修复
  store.commit('ui/SET_SIDEBAR_SUBMENU_OPEN', submenuData.length > 0)

  const endTime = performance.now()
  console.log(`加载子菜单 ${menuPath}: ${submenuData.length}个分组, 耗时: ${(endTime - startTime).toFixed(2)}ms`)

  // 缓存子菜单展开状态
  if (submenuData.length > 0) {
    localStorage.setItem(`submenu_${menuPath}`, JSON.stringify(activeNames.value))
  }
}

/**
 * 展开二级菜单
 * 默认展开所有分组
 */
const openSubnav = () => {
  const arr = []
  subnav.value.forEach((_, index) => {
    arr.push(index.toString()) // Element Plus需要字符串类型
  })
  activeNames.value = arr
}

/**
 * 自动跳转到第一个子菜单项
 * 点击父类菜单时自动加载第一个子类页面
 */
const navigateToFirstSubmenu = (menuPath) => {
  const submenuData = getSubmenuByPath(menuPath)

  if (submenuData.length > 0) {
    // 获取第一个分组的第一个菜单项
    const firstGroup = submenuData[0]
    if (firstGroup && firstGroup.url && firstGroup.url.length > 0) {
      const firstMenuItem = firstGroup.url[0]

      // 检查当前路由是否已经在这个菜单的子页面中
      const isAlreadyInSubmenu = submenuData.some(group =>
        group.url.some(item => item.url === route.path)
      )

      // 如果当前不在子菜单中，则跳转到第一个子菜单项
      if (!isAlreadyInSubmenu) {
        console.log('自动跳转到第一个子菜单:', firstMenuItem.name, firstMenuItem.url)
        router.push(firstMenuItem.url)
      } else {
        console.log('当前已在子菜单中，无需跳转')
      }
    }
  } else {
    // 如果没有子菜单，尝试跳转到默认路由
    const defaultRoute = getDefaultRoute(menuPath)
    if (defaultRoute && route.path !== defaultRoute) {
      console.log('跳转到默认路由:', defaultRoute)
      router.push(defaultRoute)
    }
  }
}

/**
 * 处理子菜单项点击
 * 每次点击都刷新页面数据，确保数据是最新的
 */
const handleSubmenuClick = (item) => {
  console.log('点击子菜单项:', item.name, item.url)

  // 检查是否是当前路由
  if (route.path === item.url) {
    console.log('重复点击当前页面，刷新页面数据')
    // 触发页面数据刷新
    refreshCurrentPageData(item)
    return
  }

  // 先导航到目标页面
  router.push({
    path: item.url,
    // 添加时间戳参数，确保每次点击都能触发刷新
    query: {
      _t: Date.now()
    }
  }).then(() => {
    // 导航成功后，延迟一下再触发数据刷新，确保页面已经加载
    setTimeout(() => {
      refreshCurrentPageData(item)
    }, 200)
  }).catch(err => {
    // 忽略导航重复错误
    if (err.name !== 'NavigationDuplicated') {
      console.error('路由导航失败:', err)
    }
  })
}

/**
 * 刷新当前页面数据
 * 当用户点击子菜单时调用，确保数据是最新的
 */
const refreshCurrentPageData = (item) => {
  const path = item.url
  const refreshTimestamp = Date.now()

  console.log(`🔄 开始刷新页面数据: ${item.name} (${path}) - 时间戳: ${refreshTimestamp}`)

  // 方法1: 通过事件总线发送刷新事件给页面组件
  if (path.includes('/service')) {
    refreshUtils.refreshServiceList({ source: 'sidebar_click', path, timestamp: refreshTimestamp })
  } else if (path.includes('/technician')) {
    refreshUtils.refreshTechnicianList({ source: 'sidebar_click', path, timestamp: refreshTimestamp })
  } else if (path.includes('/market')) {
    refreshUtils.refreshMarketList({ source: 'sidebar_click', path, timestamp: refreshTimestamp })
  } else if (path.includes('/user')) {
    refreshUtils.refreshUserList({ source: 'sidebar_click', path, timestamp: refreshTimestamp })
  } else if (path.includes('/finance')) {
    refreshUtils.refreshFinanceList({ source: 'sidebar_click', path, timestamp: refreshTimestamp })
  } else if (path.includes('/shop')) {
    refreshUtils.refreshShopOrderList({ source: 'sidebar_click', path, timestamp: refreshTimestamp })
  } else if (path.includes('/distribution')) {
    refreshUtils.refreshDistributionList({ source: 'sidebar_click', path, timestamp: refreshTimestamp })
  } else {
    // 通用刷新事件
    refreshUtils.refreshAll({ source: 'sidebar_click', path, timestamp: refreshTimestamp })
  }

  // 方法2: 如果是当前页面，也通过路由参数触发刷新
  if (route.path === path) {
    router.replace({
      path: path,
      query: {
        ...route.query,
        _refresh: refreshTimestamp
      }
    }).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.error('刷新导航失败:', err)
      }
    })
  }

  console.log(`✅ 页面数据刷新事件已发送: ${item.name}`)
}

/**
 * 初始化菜单状态
 */
const initializeMenu = () => {
  // 等待菜单数据加载完成
  if (!menuLoaded.value || mainMenuList.value.length === 0) {
    console.log('⏳ 菜单数据未加载完成，等待中...')
    return
  }

  // 更新routes数据
  routes.value = mainMenuList.value

  // 从本地存储恢复菜单状态
  const savedMenu = localStorage.getItem('currentMenu')
  if (savedMenu && mainMenuList.value.some(menu => menu.path === savedMenu)) {
    currentMenu.value = savedMenu
  } else {
    // 根据当前路由确定主菜单，或使用第一个菜单
    currentMenu.value = getMainMenuByRoute(route.path)
  }

  // 加载对应的子菜单
  loadSubmenu(currentMenu.value)

  console.log('🎯 动态菜单初始化完成:', currentMenu.value, routes.value)
}

// 生命周期
onMounted(() => {
  userInfo.value = JSON.parse(window.sessionStorage.getItem('userInfo') || '{}')

  // 如果菜单已加载，直接初始化；否则等待菜单加载完成
  if (menuLoaded.value) {
    initializeMenu()
  }
})

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    // 根据新路由更新主菜单选中状态
    const mainMenu = getMainMenuByRoute(newPath)
    if (mainMenu !== currentMenu.value) {
      currentMenu.value = mainMenu
      loadSubmenu(mainMenu)
    }

    // 更新面包屑
    store.dispatch('ui/generateBreadcrumb', route)
  },
  {
    immediate: true
  }
)

// 监听菜单加载状态
watch(
  () => menuLoaded.value,
  (loaded) => {
    if (loaded) {
      console.log('🌲 菜单数据加载完成，初始化侧边栏')
      initializeMenu()
    }
  },
  { immediate: true }
)

// 监听主菜单数据变化
watch(
  () => mainMenuList.value,
  (newMenuList) => {
    if (newMenuList && newMenuList.length > 0) {
      console.log('🔄 主菜单数据更新:', newMenuList)
      routes.value = newMenuList
      // 如果当前菜单不在新菜单列表中，重置为第一个菜单
      if (!newMenuList.some(menu => menu.path === currentMenu.value)) {
        currentMenu.value = newMenuList[0].path
        loadSubmenu(currentMenu.value)
      }
    }
  },
  { immediate: true, deep: true }
)
</script>

<style scoped>

.lb-sidebar {
  position: fixed;
  top: var(--header-height);
  left: 0;
  display: flex;
  height: calc(100% - var(--header-height));
  z-index: 2;
}

.menu {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 120px;
  height: 100%;
  background: #273543;
  z-index: 3;
}

.menu-scroll {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.menu-scroll::-webkit-scrollbar {
  width: 6px;
}

.menu-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.menu-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  opacity: 0.5;
  border-radius: 3px;
}

.menu-scroll::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.menu-top {
  width: 100%;
  color: #cccccc;
  font-size: 14px;
  text-align: center;
  line-height: 50px;
}

.menu-top li {
  width: 100%;
  min-height: 50px;
  padding: 15px 8px;
  line-height: 20px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
}

.menu-top li i {
  margin-bottom: 5px;
  font-size: 18px;
}

.menu-top li img {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-bottom: 5px;
}

.menu-top li .menu-text {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

.menu-top li:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.menu-top li.menu-active {
  background: #409eff;
  color: #ffffff;
}

.menu-top li.menu-active:hover {
  background: #409eff;
  color: #ffffff;
}

.submenu {
  position: absolute;
  top: 0;
  left: 120px;
  z-index: 2;
  width: 159px;
  height: 100%;
  background: #fff;
  display: none;
  transition: all 0.2s linear;
  border-right: 1px solid var(--border-color-light);
}

.submenu-scroll {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--spacing-sm) 0;
}

.submenu-scroll::-webkit-scrollbar {
  width: 6px;
}

.submenu-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.submenu-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  opacity: 0.5;
  border-radius: 3px;
}

.submenu-scroll::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.isopen {
  width: 159px;
  display: block;
}

/* Element Plus Collapse 样式覆盖 */
:deep(.el-collapse) {
  width: 130px;
  margin: 0 auto;
  border: none;
}

:deep(.el-collapse-item) {
  border-bottom: none;
}

:deep(.el-collapse-item__header) {
  font-weight: bold;
  font-size: 13px;
  color: var(--color-text-primary);
  background-color: transparent;
  border-bottom: 1px solid var(--border-color-light);
  padding: 0 var(--spacing-md);
  height: 40px;
  line-height: 40px;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}

.item {
  width: 100%;
  cursor: pointer;
  text-align: left;
}

.item span {
  display: inline-block;
  width: 100%;
  line-height: 32px;
  padding: 0 30px;
  font-size: 12px;
  color: #303133;
  cursor: pointer;
  transition: all 0.2s ease;
}

.item span:hover {
  background: #f5f7fa;
  color: #409eff;
}

.item span.el-collapse-item-active {
  background: #f5f7fa;
  color: #409eff;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .lb-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .lb-sidebar.mobile-open {
    transform: translateX(0);
  }

  .submenu.isopen {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .menu {
    width: 100px;
  }

  .submenu {
    left: 100px;
    width: 180px;
  }

  .submenu.isopen {
    width: 180px;
  }

  .menu-top li {
    padding: 10px 5px;
    min-height: 45px;
  }

  .menu-top li .menu-text {
    font-size: 11px;
  }
}
</style>
