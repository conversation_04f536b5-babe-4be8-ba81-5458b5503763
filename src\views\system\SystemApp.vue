<!--
  APP设置页面
-->

<template>
  <div class="lb-system-app">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>APP设置</span>
          </div>
        </template>
        
        <el-form 
          :model="configForm" 
          :rules="configRules" 
          ref="configFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="APP名称" prop="app_name">
            <el-input v-model="configForm.app_name" placeholder="请输入APP名称" />
          </el-form-item>
          
          <el-form-item label="APP版本" prop="version">
            <el-input v-model="configForm.version" placeholder="请输入APP版本号" />
          </el-form-item>
          
          <el-form-item label="下载地址" prop="download_url">
            <el-input v-model="configForm.download_url" placeholder="请输入APP下载地址" />
          </el-form-item>
          
          <el-form-item label="更新说明" prop="update_desc">
            <el-input v-model="configForm.update_desc" type="textarea" :rows="4" placeholder="请输入更新说明" />
          </el-form-item>
          
          <el-form-item label="强制更新" prop="force_update">
            <el-radio-group v-model="configForm.force_update">
              <el-radio :value="1">是</el-radio>
              <el-radio :value="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="saveConfig" :loading="saveLoading">保存配置</LbButton>
            <LbButton @click="resetConfig" style="margin-left: 10px;">重置</LbButton>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const saveLoading = ref(false)
const configFormRef = ref()

const configForm = reactive({
  app_name: '',
  version: '',
  download_url: '',
  update_desc: '',
  force_update: 0
})

const configRules = {
  app_name: [{ required: true, message: '请输入APP名称', trigger: 'blur' }],
  version: [{ required: true, message: '请输入版本号', trigger: 'blur' }]
}

const getConfig = async () => {
  try {
    const response = await fetch('/api/system/app/config')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(configForm, result.data || {})
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

const saveConfig = async () => {
  try {
    await configFormRef.value.validate()
    saveLoading.value = true
    
    const response = await fetch('/api/system/app/config', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(result.meg || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const resetConfig = () => {
  Object.assign(configForm, {
    app_name: '',
    version: '',
    download_url: '',
    update_desc: '',
    force_update: 0
  })
  configFormRef.value?.clearValidate()
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-app {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}

@media (max-width: 768px) {
  .lb-system-app {
    padding: 10px;
  }
  
  .config-form {
    max-width: 100%;
  }
}
</style>
