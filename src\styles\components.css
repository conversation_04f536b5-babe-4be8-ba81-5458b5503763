/**
 * 通用组件样式
 * 定义项目中常用的组件样式，确保设计一致性
 * 
 * 包含：
 * - 按钮组件样式
 * - 表单组件样式
 * - 导航组件样式
 * - 反馈组件样式
 * - 数据展示组件样式
 */

/* ===== 按钮组件样式 ===== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--button-padding-vertical) var(--button-padding-horizontal);
  font-size: var(--button-font-size);
  font-weight: 500;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--button-border-radius);
  transition: var(--transition-base);
  height: var(--button-height-default);
  min-width: 64px;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(96, 155, 235, 0.2);
}

.btn:disabled {
  opacity: var(--opacity-disabled);
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮尺寸 */
.btn-small {
  padding: 4px 8px;
  font-size: var(--font-size-sm);
  height: var(--button-height-small);
  min-width: 48px;
}

.btn-large {
  padding: 12px 20px;
  font-size: var(--font-size-md);
  height: var(--button-height-large);
  min-width: 80px;
}

/* 按钮类型 */
.btn-primary {
  color: #ffffff;
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark-1);
  border-color: var(--color-primary-dark-1);
}

.btn-success {
  color: #ffffff;
  background-color: var(--color-success);
  border-color: var(--color-success);
}

.btn-success:hover {
  background-color: var(--color-success-dark);
  border-color: var(--color-success-dark);
}

.btn-warning {
  color: #ffffff;
  background-color: var(--color-warning);
  border-color: var(--color-warning);
}

.btn-warning:hover {
  background-color: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
}

.btn-danger {
  color: #ffffff;
  background-color: var(--color-danger);
  border-color: var(--color-danger);
}

.btn-danger:hover {
  background-color: var(--color-danger-dark);
  border-color: var(--color-danger-dark);
}

.btn-info {
  color: #ffffff;
  background-color: var(--color-info);
  border-color: var(--color-info);
}

.btn-info:hover {
  background-color: var(--color-info-dark);
  border-color: var(--color-info-dark);
}

/* 次要按钮 */
.btn-outline-primary {
  color: var(--color-primary);
  background-color: transparent;
  border-color: var(--color-primary);
}

.btn-outline-primary:hover {
  color: #ffffff;
  background-color: var(--color-primary);
}

.btn-outline-success {
  color: var(--color-success);
  background-color: transparent;
  border-color: var(--color-success);
}

.btn-outline-success:hover {
  color: #ffffff;
  background-color: var(--color-success);
}

/* 文本按钮 */
.btn-text {
  color: var(--color-primary);
  background-color: transparent;
  border-color: transparent;
  padding: 4px 8px;
}

.btn-text:hover {
  background-color: var(--bg-color-primary-light);
}

/* 链接按钮 */
.btn-link {
  color: var(--color-primary);
  background-color: transparent;
  border-color: transparent;
  text-decoration: underline;
  padding: 0;
  height: auto;
  min-width: auto;
}

.btn-link:hover {
  color: var(--color-primary-dark-1);
}

/* ===== 表单组件样式 ===== */

.form-group {
  margin-bottom: var(--form-item-margin-bottom);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--form-label-font-size);
  font-weight: 500;
  color: var(--form-label-color);
  line-height: 1.5;
}

.form-label.required::after {
  content: " *";
  color: var(--color-danger);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--form-input-padding);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--bg-color-base);
  border: 1px solid var(--input-border-color);
  border-radius: var(--input-border-radius);
  transition: var(--transition-border);
  height: var(--input-height);
}

.form-control:focus {
  outline: none;
  border-color: var(--input-focus-border-color);
  box-shadow: 0 0 0 2px rgba(96, 155, 235, 0.2);
}

.form-control:disabled {
  background-color: var(--input-disabled-bg);
  opacity: var(--opacity-disabled);
  cursor: not-allowed;
}

.form-control::placeholder {
  color: var(--input-placeholder-color);
}

.form-text {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.form-error {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-danger);
}

/* 表单布局 */
.form-inline {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.form-inline .form-group {
  margin-bottom: 0;
  flex: 1;
}

.form-horizontal .form-group {
  display: flex;
  align-items: center;
  margin-bottom: var(--form-item-margin-bottom);
}

.form-horizontal .form-label {
  flex: 0 0 var(--form-label-width);
  margin-bottom: 0;
  margin-right: var(--spacing-md);
  text-align: right;
}

.form-horizontal .form-control {
  flex: 1;
}

/* ===== 导航组件样式 ===== */

.nav {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0;
  list-style: none;
}

.nav-item {
  display: flex;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--nav-item-padding);
  color: var(--color-text-primary);
  text-decoration: none;
  transition: var(--transition-base);
  height: var(--nav-item-height);
  font-size: var(--nav-text-size);
}

.nav-link:hover {
  color: var(--color-primary);
  background-color: var(--bg-color-primary-light);
}

.nav-link.active {
  color: var(--color-primary);
  background-color: var(--bg-color-primary-light);
  font-weight: 500;
}

.nav-link.disabled {
  color: var(--color-text-disabled);
  pointer-events: none;
  cursor: not-allowed;
}

.nav-icon {
  font-size: var(--nav-icon-size);
  flex-shrink: 0;
}

/* 垂直导航 */
.nav-vertical {
  flex-direction: column;
}

.nav-vertical .nav-item {
  width: 100%;
}

/* 标签页导航 */
.nav-tabs {
  border-bottom: 1px solid var(--border-color-base);
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-bottom: none;
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  margin-bottom: -1px;
}

.nav-tabs .nav-link:hover {
  border-color: var(--border-color-light);
  background-color: var(--bg-color-column);
}

.nav-tabs .nav-link.active {
  color: var(--color-primary);
  background-color: var(--bg-color-base);
  border-color: var(--border-color-base) var(--border-color-base) var(--bg-color-base);
}

/* ===== 面包屑组件样式 ===== */

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0;
  list-style: none;
  font-size: var(--breadcrumb-font-size);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "/";
  color: var(--color-text-placeholder);
  margin: var(--breadcrumb-separator-margin);
}

.breadcrumb-item.active {
  color: var(--color-text-secondary);
}

.breadcrumb-link {
  color: var(--breadcrumb-link-color);
  text-decoration: none;
  transition: var(--transition-color);
}

.breadcrumb-link:hover {
  color: var(--color-primary-dark-1);
  text-decoration: underline;
}

/* ===== 分页组件样式 ===== */

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: 0;
  margin: 0;
  list-style: none;
}

.pagination-item {
  display: flex;
}

.pagination-link {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: var(--pagination-item-size);
  height: var(--pagination-item-size);
  padding: 0 var(--spacing-xs);
  color: var(--color-text-primary);
  text-decoration: none;
  border: 1px solid var(--border-color-base);
  border-radius: var(--pagination-border-radius);
  transition: var(--transition-base);
  font-size: var(--font-size-sm);
}

.pagination-link:hover {
  color: var(--color-primary);
  background-color: var(--pagination-hover-bg);
  border-color: var(--color-primary);
}

.pagination-link.active {
  color: #ffffff;
  background-color: var(--pagination-active-bg);
  border-color: var(--pagination-active-bg);
}

.pagination-link.disabled {
  color: var(--color-text-disabled);
  background-color: var(--bg-color-column);
  border-color: var(--border-color-light);
  cursor: not-allowed;
  pointer-events: none;
}

/* ===== 标签组件样式 ===== */

.tag {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--tag-padding);
  font-size: var(--tag-font-size);
  font-weight: 500;
  line-height: 1;
  color: var(--color-text-primary);
  background-color: var(--bg-color-column);
  border: 1px solid var(--border-color-base);
  border-radius: var(--tag-border-radius);
  height: var(--tag-height);
  margin: var(--tag-margin);
  transition: var(--transition-base);
}

.tag.closable {
  padding-right: var(--spacing-xs);
}

.tag-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  margin-left: var(--spacing-xs);
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: 50%;
  transition: var(--transition-base);
}

.tag-close:hover {
  color: var(--color-text-primary);
  background-color: var(--border-color-light);
}

/* 标签颜色变体 */
.tag-primary {
  color: var(--color-primary);
  background-color: var(--bg-color-primary-light);
  border-color: var(--color-primary-light-2);
}

.tag-success {
  color: var(--color-success-dark);
  background-color: var(--color-success-light);
  border-color: var(--color-success);
}

.tag-warning {
  color: var(--color-warning-dark);
  background-color: var(--color-warning-light);
  border-color: var(--color-warning);
}

.tag-danger {
  color: var(--color-danger-dark);
  background-color: var(--color-danger-light);
  border-color: var(--color-danger);
}

.tag-info {
  color: var(--color-info-dark);
  background-color: var(--color-info-light);
  border-color: var(--color-info);
}

/* ===== 徽章组件样式 ===== */

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: var(--font-size-xs);
  font-weight: 500;
  line-height: 1;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  background-color: var(--color-danger);
  border-radius: 10px;
}

.badge-dot {
  width: 8px;
  height: 8px;
  min-width: 8px;
  padding: 0;
  border-radius: 50%;
}

.badge-primary { background-color: var(--color-primary); }
.badge-success { background-color: var(--color-success); }
.badge-warning { background-color: var(--color-warning); }
.badge-danger { background-color: var(--color-danger); }
.badge-info { background-color: var(--color-info); }

/* ===== 进度条组件样式 ===== */

.progress {
  display: flex;
  height: var(--progress-height);
  overflow: hidden;
  background-color: var(--progress-bg);
  border-radius: var(--progress-border-radius);
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  background-color: var(--progress-color);
  transition: width var(--transition-duration-base) ease;
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  0% { background-position-x: 1rem; }
}
