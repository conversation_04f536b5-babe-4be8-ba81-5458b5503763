/**
 * 地图API动态加载器
 * 解决腾讯地图API使用document.write导致的警告问题
 */

// 腾讯地图API配置
const TENCENT_MAP_CONFIG = {
  key: 'OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77',
  version: '2.exp',
  baseUrl: 'https://map.qq.com/api/js'
}

// 加载状态管理
let isLoading = false
let isLoaded = false
let loadPromise = null

/**
 * 动态加载腾讯地图API
 * 使用Promise方式，避免document.write的问题
 */
export function loadTencentMapAPI() {
  // 如果已经加载完成，直接返回成功的Promise
  if (isLoaded && window.qq && window.qq.maps) {
    return Promise.resolve()
  }

  // 如果正在加载中，返回现有的Promise
  if (isLoading && loadPromise) {
    return loadPromise
  }

  // 开始加载
  isLoading = true
  loadPromise = new Promise((resolve, reject) => {
    try {
      // 检查是否已经存在API
      if (window.qq && window.qq.maps) {
        isLoaded = true
        isLoading = false
        resolve()
        return
      }

      console.log('🗺️ 开始动态加载腾讯地图API...')

      // 创建script标签
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.charset = 'utf-8'
      script.async = true
      script.defer = true
      
      // 构建API URL
      const apiUrl = `${TENCENT_MAP_CONFIG.baseUrl}?v=${TENCENT_MAP_CONFIG.version}&key=${TENCENT_MAP_CONFIG.key}`
      script.src = apiUrl

      // 设置加载成功回调
      script.onload = () => {
        console.log('✅ 腾讯地图API加载成功')
        
        // 验证API是否正确加载
        if (window.qq && window.qq.maps) {
          isLoaded = true
          isLoading = false
          resolve()
        } else {
          console.error('❌ 腾讯地图API加载后验证失败')
          isLoading = false
          reject(new Error('腾讯地图API加载后验证失败'))
        }
      }

      // 设置加载失败回调
      script.onerror = (error) => {
        console.error('❌ 腾讯地图API加载失败:', error)
        isLoading = false
        reject(new Error('腾讯地图API加载失败'))
      }

      // 设置超时处理
      const timeout = setTimeout(() => {
        console.error('❌ 腾讯地图API加载超时')
        isLoading = false
        reject(new Error('腾讯地图API加载超时'))
      }, 10000) // 10秒超时

      // 清除超时定时器
      script.onload = (originalOnload => {
        return function(...args) {
          clearTimeout(timeout)
          return originalOnload.apply(this, args)
        }
      })(script.onload)

      script.onerror = (originalOnerror => {
        return function(...args) {
          clearTimeout(timeout)
          return originalOnerror.apply(this, args)
        }
      })(script.onerror)

      // 添加到页面
      document.head.appendChild(script)

    } catch (error) {
      console.error('❌ 腾讯地图API加载器异常:', error)
      isLoading = false
      reject(error)
    }
  })

  return loadPromise
}

/**
 * 检查腾讯地图API是否已加载
 */
export function isTencentMapAPILoaded() {
  return isLoaded && window.qq && window.qq.maps
}

/**
 * 获取腾讯地图API加载状态
 */
export function getTencentMapAPIStatus() {
  return {
    isLoading,
    isLoaded,
    hasAPI: window.qq && window.qq.maps
  }
}

/**
 * 重置加载状态（用于重新加载）
 */
export function resetTencentMapAPILoader() {
  isLoading = false
  isLoaded = false
  loadPromise = null
  console.log('🔄 腾讯地图API加载器已重置')
}

/**
 * 预加载腾讯地图API
 * 在应用启动时调用，提前加载地图API
 */
export function preloadTencentMapAPI() {
  // 延迟加载，避免阻塞应用启动
  setTimeout(() => {
    loadTencentMapAPI()
      .then(() => {
        console.log('🎯 腾讯地图API预加载成功')
      })
      .catch(error => {
        console.warn('⚠️ 腾讯地图API预加载失败:', error)
      })
  }, 2000) // 2秒后开始预加载
}

/**
 * 创建地图实例的辅助函数
 * 确保API已加载后再创建地图
 */
export async function createTencentMap(containerId, options = {}) {
  try {
    // 确保API已加载
    await loadTencentMapAPI()

    // 获取容器
    const container = document.getElementById(containerId)
    if (!container) {
      throw new Error(`地图容器未找到: ${containerId}`)
    }

    // 默认配置
    const defaultOptions = {
      center: new window.qq.maps.LatLng(39.916527, 116.397128),
      zoom: 13
    }

    // 合并配置
    const finalOptions = { ...defaultOptions, ...options }

    // 创建地图实例
    const map = new window.qq.maps.Map(container, finalOptions)

    console.log('✅ 腾讯地图实例创建成功')
    return map

  } catch (error) {
    console.error('❌ 创建腾讯地图实例失败:', error)
    throw error
  }
}

/**
 * 地图API错误处理
 */
export function handleMapAPIError(error, fallbackCallback) {
  console.error('🗺️ 地图API错误:', error)
  
  // 可以在这里添加错误上报逻辑
  
  // 执行降级回调
  if (typeof fallbackCallback === 'function') {
    try {
      fallbackCallback(error)
    } catch (fallbackError) {
      console.error('❌ 地图API降级处理失败:', fallbackError)
    }
  }
}

// 导出默认配置
export { TENCENT_MAP_CONFIG }
