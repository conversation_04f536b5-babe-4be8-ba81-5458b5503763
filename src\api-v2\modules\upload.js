/**
 * 文件上传模块 - V2版本
 * 包含文件上传相关接口
 */

import { get, post, postUpload } from '../index'
import axios from 'axios'

/**
 * 带Cookie认证的文件上传封装
 * @param {string} url 上传地址
 * @param {FormData} data 文件数据
 * @param {Function} progressCallback 进度回调
 * @param {Object} customHeaders 自定义请求头
 * @returns {Promise} 返回Promise对象
 */
function postUploadWithHeaders(url, data = {}, progressCallback, customHeaders = {}) {
  return new Promise((resolve, reject) => {
    // 合并请求头（移除Content-Type，让浏览器自动设置multipart边界）
    const headers = {
      ...customHeaders
    }

    axios.post(url, data, {
      headers: headers,
      withCredentials: true, // 使用Cookie认证
      onUploadProgress: progressCallback
    })
    .then(response => {
      resolve(response.data)
    }, err => {
      console.log('文件上传错误:', err)
      reject(err)
    })
  })
}

export default {
  /**
   * 核心文件上传接口 - 使用Cookie认证
   * @param {FormData} formData 文件数据，包含multipartFile字段
   * @param {Function} progressCallback 进度回调函数
   * @returns {Promise} 返回上传结果
   */
  uploadFile(formData, progressCallback) {
    console.log('📤 核心文件上传API-V2请求 - /api/ admin/file/upload')

    // 使用Cookie认证，不需要手动设置token
    return postUploadWithHeaders('/api/admin/file/upload', formData, progressCallback)
      .then(response => {
        console.log('📤 文件上传成功:', response)
        return response
      })
      .catch(error => {
        console.error('📤 文件上传失败:', error)
        throw error
      })
  },

  /**
   * 文件下载接口
   * @param {Object} params 下载参数
   * @param {string} params.fileId 文件ID或文件名
   * @returns {Promise} 返回下载结果
   */
  downloadFile(params) {
    console.log('📥 文件下载API-V2请求 - /api/admin/file/downloadFile:', params)
    return get('/api/admin/file/downloadFile', params)
  },

  /**
   * 上传文件（旧版本，保留兼容性）
   * @param {FormData} formData 文件数据
   * @param {Function} progressCallback 进度回调函数
   * @returns {Promise} 返回上传结果
   */
  uploadFiles(formData, progressCallback) {
    console.log('📤 文件上传API-V2请求（旧版本兼容）')
    return postUpload('/api/admin/upload/file', formData, progressCallback)
  },

  /**
   * 上传图片（旧版本，保留兼容性）
   * @param {FormData} formData 图片数据
   * @param {Function} progressCallback 进度回调函数
   * @returns {Promise} 返回上传结果
   */
  uploadImage(formData, progressCallback) {
    console.log('🖼️ 图片上传API-V2请求（旧版本兼容）')
    return postUpload('/api/admin/upload/image', formData, progressCallback)
  },

  /**
   * 云存储上传后回调
   * @param {Object} querys 文件信息
   * @param {string} querys.file_url 文件URL
   * @param {string} querys.file_name 文件名
   * @param {number} querys.file_size 文件大小
   * @param {number} querys.group_id 分组ID
   * @returns {Promise} 返回处理结果
   */
  uploadAddFile(querys) {
    console.log('☁️ 云存储文件回调API-V2请求:', querys)
    return post('/api/admin/upload/addFile', querys)
  },

  /**
   * 获取文件列表
   * @param {Object} querys 查询参数
   * @param {number} querys.group_id 分组ID
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回文件列表
   */
  fileList(querys) {
    console.log('📁 文件列表API-V2请求参数:', querys)
    return get('/api/admin/upload/files', querys)
  },

  /**
   * 删除文件
   * @param {Object} querys 删除参数
   * @param {number} querys.id 文件ID
   * @returns {Promise} 返回删除结果
   */
  delFiles(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('文件ID不能为空'))
    }
    console.log('🗑️ 删除文件API-V2请求:', querys)
    return post('/api/admin/upload/delete', querys)
  },

  /**
   * 创建文件分组
   * @param {Object} querys 分组信息
   * @param {string} querys.name 分组名称
   * @returns {Promise} 返回创建结果
   */
  createGroup(querys) {
    console.log('📂 创建文件分组API-V2请求:', querys)
    return post('/api/admin/upload/createGroup', querys)
  },

  /**
   * 获取文件分组列表
   * @returns {Promise} 返回分组列表
   */
  groupList() {
    console.log('📂 文件分组列表API-V2请求')
    return get('/api/admin/upload/groups')
  }
}
