/**
 * API核心封装文件 - V2版本
 * 根据API封装规范文档实现统一的API管理和调用
 */

import axios from 'axios'
import router from '../router'
import apis from './modules'

// 获取全局配置（生产环境从 window.APP_CONFIG 读取）
function getGlobalConfig() {
  if (typeof window !== 'undefined' && window.APP_CONFIG) {
    return window.APP_CONFIG.api
  }
  return null
}

// 基础配置
let developmentURL = '' // 开发环境使用代理，不需要完整URL
let productionURL = `http://192.168.1.29:8889/ims` // 生产环境URL

// 生产环境优先使用全局配置
if (process.env.NODE_ENV === 'production') {
  const globalConfig = getGlobalConfig()
  if (globalConfig && globalConfig.baseURL) {
    productionURL = globalConfig.baseURL
  }
}

axios.defaults.timeout = 300000
axios.defaults.baseURL = process.env.NODE_ENV === 'development'
  ? developmentURL
  : productionURL

// 配置axios支持Cookie认证
axios.defaults.withCredentials = true // 确保所有请求都携带cookie

/**
 * API配置说明：
 * - 开发环境：使用Vite代理，baseURL为空字符串，代理配置在vite.config.js中
 * - 生产环境：直接访问API服务器
 */

// 请求拦截器
axios.interceptors.request.use(config => {
  // 确保请求携带cookie（用于autograph认证）
  config.withCredentials = true

  // 移除token header认证，改用cookie认证
  // 注释掉原有的token header设置
  // if (sessionStorage.getItem('minitk')) {
  //   config.headers['token'] = sessionStorage.getItem('minitk')
  // }

  // 请求日志
  console.log('🚀 API-V2请求:', {
    method: config.method?.toUpperCase(),
    url: config.url,
    baseURL: config.baseURL,
    withCredentials: config.withCredentials,
    params: config.params,
    data: config.data
  })

  return config
}, error => {
  console.error('❌ 请求配置错误:', error)
  return Promise.reject(error)
})

// 响应拦截器
axios.interceptors.response.use(res => {
  console.log('✅ API-V2响应:', {
    url: res.config.url,
    status: res.status,
    data: res.data
  })

  // 根据业务状态码进行不同处理
  if (res.data.code === 401) { // cookie认证失败
    console.error('❌ 未授权，请重新登录:', res.data.error)
    // 清除本地存储的认证信息
    sessionStorage.removeItem('minitk')
    sessionStorage.removeItem('ms_username')
    // 清除cookie中的autograph
    document.cookie = 'autograph=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/'
    router.push('/login')
  } else if (res.data.code === 402) {
    console.error('❌ 业务错误:', res.data.error)
  } else if (res.data.code === 400) {
    console.error('❌ 参数错误:', res.data.error)
  }

  return res
}, err => {
  console.error('❌ API-V2响应错误:', err)
  console.error('❌ 网络错误，请稍后再试:', err.message)
  return Promise.reject(err.response)
})

/**
 * GET请求封装
 * @param {string} url 请求地址
 * @param {Object} params 请求参数
 * @param {string} type 内容类型
 * @returns {Promise} 返回Promise对象
 */
export function get(url, params = {}, type = 'application/json') {
  return new Promise((resolve, reject) => {
    axios.get(url, {
      params: params,
      headers: {
        'Content-Type': type
      },
      withCredentials: true // 确保GET请求也携带cookie
    })
    .then(response => {
      resolve(response.data)
    })
    .catch(err => {
      reject(err)
    })
  })
}

/**
 * POST请求封装
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {string} type 内容类型
 * @returns {Promise} 返回Promise对象
 */
export function post(url, data = {}, type = 'application/json') {
  return new Promise((resolve, reject) => {
    axios.post(url, data, {
      headers: {
        'Content-Type': type
      },
      withCredentials: true // 确保POST请求也携带cookie
    }).then(response => {
      resolve(response.data)
    }, err => {
      console.log('POST请求错误:', err)
      reject(err)
    })
  })
}

/**
 * PUT请求封装
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {string} type 内容类型
 * @returns {Promise} 返回Promise对象
 */
export function put(url, data = {}, type = 'application/json') {
  return new Promise((resolve, reject) => {
    axios.put(url, data, {
      headers: {
        'Content-Type': type
      },
      withCredentials: true // 确保PUT请求也携带cookie
    })
      .then(response => {
        resolve(response.data)
      }, err => {
        reject(err)
      })
  })
}

/**
 * DELETE请求封装
 * @param {string} url 请求地址
 * @param {Object} params 请求参数
 * @param {string} type 内容类型
 * @returns {Promise} 返回Promise对象
 */
export function del(url, params = {}, type = 'application/json') {
  return new Promise((resolve, reject) => {
    axios.delete(url, {
      params: params,
      headers: {
        'Content-Type': type
      },
      withCredentials: true // 确保DELETE请求也携带cookie
    })
    .then(response => {
      resolve(response.data)
    })
    .catch(err => {
      reject(err)
    })
  })
}

/**
 * 文件上传封装
 * @param {string} url 上传地址
 * @param {FormData} data 文件数据
 * @param {Function} progressCallback 进度回调
 * @returns {Promise} 返回Promise对象
 */
export function postUpload(url, data = {}, progressCallback) {
  return new Promise((resolve, reject) => {
    axios.post(url, data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      withCredentials: true, // 确保文件上传请求也携带cookie
      onUploadProgress: progressCallback
    })
    .then(response => {
      resolve(response.data)
    }, err => {
      console.log('文件上传错误:', err)
      reject(err)
    })
  })
}

// 导出API实例
export const api = {
  ...apis
}

// 默认导出
export default api
