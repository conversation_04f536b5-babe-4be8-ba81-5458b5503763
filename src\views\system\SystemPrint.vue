<!--
  打印机设置页面
-->

<template>
  <div class="lb-system-print">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>打印机设置</span>
          </div>
        </template>
        
        <el-form 
          :model="configForm" 
          ref="configFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="打印机名称">
            <el-input v-model="configForm.printer_name" placeholder="请输入打印机名称" />
          </el-form-item>
          
          <el-form-item label="打印机IP">
            <el-input v-model="configForm.printer_ip" placeholder="请输入打印机IP地址" />
          </el-form-item>
          
          <el-form-item label="打印机端口">
            <el-input-number v-model="configForm.printer_port" :min="1" :max="65535" />
          </el-form-item>
          
          <el-form-item label="启用状态">
            <el-radio-group v-model="configForm.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="saveConfig" :loading="saveLoading">保存配置</LbButton>
            <LbButton @click="testPrint" style="margin-left: 10px;">测试打印</LbButton>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const saveLoading = ref(false)
const configFormRef = ref()

const configForm = reactive({
  printer_name: '',
  printer_ip: '',
  printer_port: 9100,
  status: 1
})

const getConfig = async () => {
  try {
    const response = await fetch('/api/system/print/config')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(configForm, result.data || {})
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

const saveConfig = async () => {
  try {
    saveLoading.value = true
    
    const response = await fetch('/api/system/print/config', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(result.meg || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const testPrint = async () => {
  try {
    const response = await fetch('/api/system/print/test', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('打印测试成功')
    } else {
      ElMessage.error(result.meg || '打印测试失败')
    }
  } catch (error) {
    ElMessage.error('打印测试失败')
  }
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-print {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}
</style>
