/**
 * 系统设置模块 - V2版本
 * 包含系统管理相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取系统日志列表
   * @param {Object} querys 查询参数
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回日志列表
   */
  logList(querys) {
    console.log('📋 系统日志列表API-V2请求参数:', querys)
    return get('/api/admin/system/logs', querys)
  },

  /**
   * 清空系统日志
   * @returns {Promise} 返回清空结果
   */
  clearLogs() {
    console.log('🗑️ 清空系统日志API-V2请求')
    return post('/api/admin/system/clearLogs')
  },

  /**
   * 获取系统统计信息
   * @returns {Promise} 返回统计信息
   */
  getStatistics() {
    console.log('📊 获取系统统计API-V2请求')
    return get('/api/admin/system/statistics')
  },

  /**
   * 系统备份
   * @returns {Promise} 返回备份结果
   */
  backup() {
    console.log('💾 系统备份API-V2请求')
    return post('/api/admin/system/backup')
  },

  /**
   * 获取备份列表
   * @param {Object} querys 查询参数
   * @returns {Promise} 返回备份列表
   */
  backupList(querys) {
    console.log('📦 备份列表API-V2请求参数:', querys)
    return get('/api/admin/system/backups', querys)
  },

  /**
   * 恢复备份
   * @param {Object} querys 恢复参数
   * @param {string} querys.backupId 备份ID
   * @returns {Promise} 返回恢复结果
   */
  restore(querys) {
    console.log('🔄 恢复备份API-V2请求:', querys)
    return post('/api/admin/system/restore', querys)
  }
}
