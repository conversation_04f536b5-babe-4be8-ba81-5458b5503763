<!--
  服务配置管理页面
  功能描述：管理服务项目配置，包括配置名称、输入类型、可选项等
  创建时间：2025-01-17
-->

<template>
  <div class="service-peizhi">
    <!-- 顶部导航 -->
    <TopNav title="服务配置管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
          <!-- <el-form-item label="关联项目id" prop="serviceId">
            <el-input
              size="default"
              v-model="searchForm.serviceId"
              placeholder="请输入关联项目id"
              clearable
              style="width: 150px"
            />
          </el-form-item> -->
          <el-form-item label="项目名称" prop="name">
            <el-input
              size="default"
              v-model="searchForm.name"
              placeholder="请输入项目名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="报价类型" prop="type">
            <el-select
              size="default"
              v-model="searchForm.type"
              placeholder="请选择报价类型"
              clearable
              style="width: 150px"
            >
              <el-option label="一口价" :value="1" />
              <el-option label="报价模式" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <LbButton
              size="default"
              type="primary"
              icon="Search"
              @click="handleSearch"
            >
              搜索
            </LbButton>
            <LbButton
              size="default"
              icon="RefreshLeft"
              @click="handleReset"
            >
              重置
            </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增配置
                </LbButton>
                <LbButton
                  size="default"
                  type="success"
                  icon="Download"
                  @click="handleDownloadTemplate"
                >
                  下载服务配置模板
                </LbButton>
                <LbButton
                  size="default"
                  type="warning"
                  icon="Upload"
                  @click="handleBatchImport"
                >
                  批量导入服务配置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          :cell-style="{ padding: '15px 8px' }"
          :row-style="{ height: 'auto', backgroundColor: '#ffffff' }"
          style="width: 100%"
        >
        <el-table-column prop="id" label="ID" width="80" fixed="left" />
        <el-table-column prop="serviceId" label="关联项目ID" width="120" />
        <el-table-column prop="serviceTitle" label="项目名称" width="180" show-overflow-tooltip />
        <el-table-column prop="problemDesc" label="配置名称" width="160" show-overflow-tooltip />
        <el-table-column prop="problemContent" label="配置描述" min-width="250">
          <template #default="scope">
            <div class="content-cell">
              {{ scope.row.problemContent }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="inputType" label="配置类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getInputTypeTagType(scope.row.inputType)" size="small">
              {{ getInputTypeText(scope.row.inputType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isRequired" label="是否必填" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isRequired === 1 ? 'danger' : 'info'" size="small">
              {{ scope.row.isRequired === 1 ? '必填' : '非必填' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="报价类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getPriceTypeTagType(scope.row.type)" size="small">
              {{ getPriceTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="options" label="可选项" min-width="200">
          <template #default="scope">
            <div v-if="scope.row.inputType === 3 || scope.row.inputType === 4" class="options-cell">
              {{ formatOptions(scope.row.options) }}
            </div>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
 
        
   
      
       
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <LbButton
              size="default"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </LbButton>
            <LbButton
              size="default"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </LbButton>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑配置' : '新增配置'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="关联项目ID" prop="serviceId">
          <el-input
            size="default"
            v-model="form.serviceId"
            placeholder="请输入关联项目ID"
            type="number"
          />
        </el-form-item>
        <el-form-item label="项目名称" prop="serviceTitle">
          <el-input
            size="default"
            v-model="form.serviceTitle"
            placeholder="请输入项目名称（非必填）"
          />
        </el-form-item>
        <el-form-item label="配置名称" prop="problemDesc">
          <el-input
            size="default"
            v-model="form.problemDesc"
            placeholder="请输入配置名称"
          />
        </el-form-item>
        <el-form-item label="配置描述" prop="problemContent">
          <el-input
            size="default"
            v-model="form.problemContent"
            type="textarea"
            :rows="3"
            placeholder="请输入配置描述"
          />
        </el-form-item>
        
        <el-form-item label="报价类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :value="1">一口价</el-radio>
            <el-radio :value="2">报价模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="配置类型" prop="inputType">
          <el-radio-group v-model="form.inputType" @change="handleInputTypeChange">
            <el-radio :value="1">输入框</el-radio>
            <el-radio :value="2">上传图片</el-radio>
            <el-radio :value="3">单选</el-radio>
            <el-radio :value="4">多选</el-radio>
          </el-radio-group>
        </el-form-item>
 
        <el-form-item v-if="form.inputType === 3 || form.inputType === 4" label="可选项配置" prop="options">
          <div class="options-config">
            <div
              v-for="(option, index) in optionsList"
              :key="index"
              class="option-item"
            >
              <el-input
                size="default"
                v-model="optionsList[index]"
                placeholder="请输入可选项内容"
                style="width: 300px"
              />
              <LbButton
                size="default"
                type="danger"
                icon="Delete"
                @click="removeOption(index)"
                style="margin-left: 10px"
              >
                删除
              </LbButton>
            </div>
            <LbButton
              size="default"
              type="primary"
              icon="Plus"
              @click="addOption"
              style="margin-top: 10px"
            >
              添加可选项
            </LbButton>
          </div>
        </el-form-item>
        <!-- <el-form-item label="默认值" prop="val">
          <el-input
            size="default"
            v-model="form.val"
            placeholder="请输入默认值（可选）"
          />
        </el-form-item> -->
               <el-form-item label="是否必填" prop="isRequired">
          <el-radio-group v-model="form.isRequired">
            <el-radio :value="1">必填</el-radio>
            <el-radio :value="0">非必填</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton size="default" @click="dialogVisible = false">取消</LbButton>
          <LbButton
            size="default"
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入服务配置"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="import-content">
        <div class="import-tips">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 请先下载服务配置模板文件</p>
              <p>2. 按照模板格式填写数据</p>
              <p>3. 上传填写完成的Excel文件</p>
              <p>4. 支持的文件格式：.xlsx</p>
            </template>
          </el-alert>
        </div>

        <div class="upload-area" style="margin-top: 20px;">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".xlsx,.xls"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 xlsx/xls 文件
              </div>
            </template>
          </el-upload>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <LbButton size="default" @click="importDialogVisible = false">取消</LbButton>
          <LbButton
            size="default"
            type="primary"
            :loading="importLoading"
            :disabled="!selectedFile"
            @click="handleConfirmImport"
          >
            确认导入
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 导入失败数据显示对话框 -->
    <el-dialog
      v-model="failDataDialogVisible"
      title="导入失败数据详情"
      width="50%"
      :close-on-click-modal="false"
      top="5vh"
    >
      <div class="fail-data-content">
        <!-- 统计信息 -->
        <div class="import-summary">
          <el-alert
            :title="`导入结果：成功 ${importResult.successCount || 0} 条，失败 ${importResult.failCount || 0} 条`"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>

        <!-- 失败数据表格 -->
        <div class="fail-data-table" style="margin-top: 20px;">
          <h4>失败数据列表：</h4>
          <el-table
            :data="importResult.failList || []"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            style="width: 100%; margin-top: 10px;"
            max-height="400"
          >
            <el-table-column prop="serviceId" label="服务ID" width="100" />
            <el-table-column prop="problemDesc" label="配置名称" width="150" show-overflow-tooltip />
            <el-table-column prop="problemContent" label="配置描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="type" label="报价类型" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.type === 1 ? 'success' : 'warning'" size="small">
                  {{ scope.row.type === 1 ? '一口价' : '报价模式' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isRequired" label="是否必填" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.isRequired === 1 ? 'danger' : 'info'" size="small">
                  {{ scope.row.isRequired === 1 ? '必填' : '非必填' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="inputType" label="配置类型" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getInputTypeTagType(scope.row.inputType)" size="small">
                  {{ getInputTypeText(scope.row.inputType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="options" label="可选项" min-width="150" show-overflow-tooltip />
          </el-table>
        </div>

        <!-- 失败原因列表 -->
        <div class="fail-reasons" style="margin-top: 20px;" v-if="importResult.failReasons && importResult.failReasons.length > 0">
          <h4>失败原因：</h4>
          <div class="reason-list">
            <div
              v-for="(reason, index) in importResult.failReasons"
              :key="index"
              class="reason-item"
            >
              <el-alert
                :title="`第 ${index + 1} 条数据：${reason}`"
                type="error"
                :closable="false"
                show-icon
                style="margin-bottom: 10px;"
              />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <LbButton size="default" @click="failDataDialogVisible = false">关闭</LbButton>
          <!-- <LbButton
            size="default"
            type="primary"
            @click="exportFailData"
          >
            导出失败数据
          </LbButton> -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

// 导入自定义组件
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

// 获取Vue实例
const { proxy } = getCurrentInstance()
const api = proxy.$api

// 页面状态
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const importDialogVisible = ref(false)
const importLoading = ref(false)
const selectedFile = ref(null)
const uploadRef = ref(null)
const failDataDialogVisible = ref(false)
const importResult = ref({
  successCount: 0,
  failCount: 0,
  failList: [],
  failReasons: []
})

// 数据存储
const tableData = ref([])
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  serviceId: '',
  name: '',
  type: null,
  pageNum: 1,
  pageSize: 10
})

// 表单数据
const form = reactive({
  id: '',
  type: 1,
  serviceId: '',
  serviceTitle: '',
  problemDesc: '',
  problemContent: '',
  isRequired: 1,
  inputType: 1,
  val: '',
  options: '[]'
})

// 可选项列表（用于选择框类型）
const optionsList = ref([])

// 表单验证规则
const formRules = {
  serviceId: [
    { required: true, message: '请输入关联项目id', trigger: 'blur' }
  ],
  problemDesc: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  problemContent: [
    { required: true, message: '请输入配置描述', trigger: 'blur' }
  ],
  isRequired: [
    { required: true, message: '请选择是否必填', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择报价类型', trigger: 'change' }
  ],
  inputType: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ]
}

// 表单引用
const formRef = ref(null)
const searchFormRef = ref(null)

// 获取输入类型文本
const getInputTypeText = (type) => {
  const typeMap = {
    1: '输入框',
    2: '上传图片',
    3: '单选',
    4: '多选'
  }
  return typeMap[type] || '未知'
}

// 获取输入类型标签类型
const getInputTypeTagType = (type) => {
  const typeMap = {
    1: 'primary',
    2: 'success',
    3: 'warning',
    4: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取报价类型文本
const getPriceTypeText = (type) => {
  const typeMap = {
    1: '一口价',
    2: '报价模式'
  }
  return typeMap[type] || '未知'
}

// 获取报价类型标签类型
const getPriceTypeTagType = (type) => {
  const typeMap = {
    1: 'success',
    2: 'warning'
  }
  return typeMap[type] || 'info'
}



// 格式化可选项显示
const formatOptions = (optionsStr) => {
  try {
    const options = JSON.parse(optionsStr || '[]')
    return options.join(', ')
  } catch (error) {
    return optionsStr || '-'
  }
}

// 获取列表数据
const getList = async () => {
  console.log('🚀 开始获取服务配置列表数据...')
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.serviceId) params.serviceId = searchForm.serviceId
    if (searchForm.name) params.name = searchForm.name
    if (searchForm.type !== null && searchForm.type !== '') params.type = searchForm.type

    console.log('📤 API请求参数:', params)
    console.log('🔗 API实例:', api)
    console.log('🔗 service模块:', api.service)

    // 使用API-V2调用方式
    const result = await api.service.priceSettingList(params)
    console.log('📋 服务配置列表数据 (API-V2):', result)
    console.log('📋 result类型:', typeof result)
    console.log('📋 result.data:', result?.data)
    console.log('📋 result.data类型:', typeof result?.data)

    // 处理响应数据
    if (result.code === 200 || result.code === '200') {
      const data = result.data
      console.log('📊 处理API响应数据:', data)

      // 根据实际API响应格式处理数据: { data: { totalCount: xxx, list: [...] } }
      if (data.list && Array.isArray(data.list)) {
        tableData.value = data.list
        total.value = data.totalCount || 0
      } else {
        // 兼容其他格式
        tableData.value = Array.isArray(data) ? data : []
        total.value = Array.isArray(data) ? data.length : 0
      }

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        count: tableData.value.length
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.meg || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取服务配置列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchForm.pageNum = 1
  getList()
}

// 重置搜索
const handleReset = () => {
  searchFormRef.value?.resetFields()
  searchForm.pageNum = 1
  getList()
}

// 分页大小改变
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getList()
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = async (row) => {
  isEdit.value = true

  try {
    // 获取详情数据
    const result = await api.service.priceSettingInfo({ id: row.id })
    console.log('📋 服务配置详情数据:', result)

    if (result.code === 200 || result.code === '200') {
      const data = result.data

      // 填充表单数据
      form.id = data.id || ''
      form.type = data.type || 1
      form.serviceId = data.serviceId || ''
      form.serviceTitle = data.serviceTitle || ''
      form.problemDesc = data.problemDesc || ''
      form.problemContent = data.problemContent || ''
      form.isRequired = data.isRequired !== undefined ? data.isRequired : 1
      form.inputType = data.inputType || 1
      form.val = data.val || ''
      form.options = data.options || '[]'

      // 处理可选项数据
      if (form.inputType === 3 || form.inputType === 4) {
        try {
          optionsList.value = JSON.parse(form.options || '[]')
        } catch (error) {
          optionsList.value = []
        }
      }

      dialogVisible.value = true
    } else {
      ElMessage.error(result.meg || result.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取服务配置详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除配置"${row.problemDesc}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const result = await api.service.priceSettingDelete({ id: row.id })
      console.log('🗑️ 删除服务配置结果:', result)

      if (result.code === 200 || result.code === '200') {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(result.meg || result.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除服务配置失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 输入类型改变处理
const handleInputTypeChange = (value) => {
  if (value === 3 || value === 4) {
    // 单选或多选类型，初始化可选项列表
    if (optionsList.value.length === 0) {
      optionsList.value = ['']
    }
  } else {
    // 其他类型，清空可选项
    optionsList.value = []
    form.options = '[]'
  }
}

// 添加可选项
const addOption = () => {
  optionsList.value.push('')
}

// 删除可选项
const removeOption = (index) => {
  if (optionsList.value.length > 1) {
    optionsList.value.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个可选项')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    type: 1,
    serviceId: '',
    serviceTitle: '',
    problemDesc: '',
    problemContent: '',
    isRequired: 1,
    inputType: 1,
    val: '',
    options: '[]'
  })
  optionsList.value = []
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()

    submitLoading.value = true

    // 处理可选项数据
    if (form.inputType === 3 || form.inputType === 4) {
      // 过滤空可选项
      const validOptions = optionsList.value.filter(option => option.trim())
      if (validOptions.length === 0) {
        ElMessage.error('单选/多选类型至少需要一个有效可选项')
        return
      }
      form.options = JSON.stringify(validOptions)
    } else {
      form.options = '[]'
    }

    // 构建提交数据
    const submitData = {
      id: form.id,
      type: form.type,
      serviceId: form.serviceId,
      serviceTitle: form.serviceTitle,
      problemDesc: form.problemDesc,
      problemContent: form.problemContent,
      isRequired: form.isRequired,
      inputType: form.inputType,
      val: form.val,
      options: form.options
    }

    let result
    if (isEdit.value) {
      // 编辑
      result = await api.service.priceSettingEdit(submitData)
      console.log('✏️ 编辑服务配置结果:', result)
    } else {
      // 新增
      result = await api.service.priceSettingAdd(submitData)
      console.log('➕ 新增服务配置结果:', result)
    }

    if (result.code === 200 || result.code === '200') {
      ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getList()
    } else {
      ElMessage.error(result.meg || result.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交服务配置失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 下载服务配置模板
const handleDownloadTemplate = async () => {
  try {
    console.log('📥 开始下载服务配置模板...')

    // 使用API-V2调用方式
    const result = await api.service.priceSettingTemplate()
    console.log('📥 下载服务配置模板API响应:', result)

    // 如果API返回的是文件流或下载链接
    if (result) {
      // 如果返回的是blob数据，直接下载
      if (result instanceof Blob) {
        const url = window.URL.createObjectURL(result)
        const link = document.createElement('a')
        link.href = url
        link.download = '服务价格配置导入模板.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        // 如果API返回的是下载URL或其他格式，使用原来的方式
        const downloadUrl = `${import.meta.env.VITE_API_BASE_URL || ''}/api/admin/priceSetting/template`

        // 创建一个隐藏的下载链接
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = '服务价格配置导入模板.xlsx'
        link.target = '_blank'

        // 添加token到请求头（如果需要）
        const token = sessionStorage.getItem('minitk')
        if (token) {
          // 对于文件下载，我们需要在URL中添加token参数
          const separator = downloadUrl.includes('?') ? '&' : '?'
          link.href = `${downloadUrl}${separator}token=${encodeURIComponent(token)}`
        }

        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      ElMessage.success('模板下载开始，请查看浏览器下载')
    }

  } catch (error) {
    console.error('下载服务配置模板失败:', error)
    ElMessage.error('下载模板失败')
  }
}

// 打开批量导入对话框
const handleBatchImport = () => {
  importDialogVisible.value = true
  selectedFile.value = null
}

// 文件选择变化处理
const handleFileChange = (file) => {
  console.log('📁 文件选择变化:', file)
  selectedFile.value = file
}

// 文件移除处理
const handleFileRemove = () => {
  console.log('🗑️ 文件被移除')
  selectedFile.value = null
}

// 确认导入
const handleConfirmImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请选择要导入的文件')
    return
  }

  try {
    importLoading.value = true
    console.log('📤 开始批量导入服务配置...')

    // 创建FormData
    const formData = new FormData()
    formData.append('file', selectedFile.value.raw)

    console.log('📤 导入文件信息:', {
      name: selectedFile.value.name,
      size: selectedFile.value.size,
      type: selectedFile.value.raw.type
    })

    const result = await api.service.priceSettingImport(formData)
    console.log('📤 批量导入API响应:', result)

    // 处理导入结果
    if (result.code === 200 || result.code === '200') {
      // 完全成功
      ElMessage.success('批量导入成功')
      importDialogVisible.value = false
      selectedFile.value = null
      // 刷新列表
      getList()
    } else if (result.code === '-1') {
      // 部分成功，部分失败
      const { successCount = 0, failCount = 0, failList = [], failReasons = [] } = result.data || {}

      // 保存导入结果数据
      importResult.value = {
        successCount,
        failCount,
        failList,
        failReasons
      }

      // 显示结果消息
      ElMessage.warning(result.msg || `成功${successCount}条，失败${failCount}条`)

      // 关闭导入对话框
      importDialogVisible.value = false
      selectedFile.value = null

      // 显示失败数据详情对话框
      failDataDialogVisible.value = true

      // 刷新列表（显示成功导入的数据）
      getList()
    } else {
      // 完全失败
      ElMessage.error(result.meg || result.msg || '批量导入失败')
    }
  } catch (error) {
    console.error('批量导入服务配置失败:', error)
    ElMessage.error('批量导入失败')
  } finally {
    importLoading.value = false
  }
}

// 导出失败数据
const exportFailData = () => {
  try {
    if (!importResult.value.failList || importResult.value.failList.length === 0) {
      ElMessage.warning('没有失败数据可导出')
      return
    }

    // 准备导出数据
    const exportData = importResult.value.failList.map((item, index) => ({
      '序号': index + 1,
      '服务ID': item.serviceId || '',
      '配置名称': item.problemDesc || '',
      '配置描述': item.problemContent || '',
      '报价类型': item.type === 1 ? '一口价' : '报价模式',
      '是否必填': item.isRequired === 1 ? '必填' : '非必填',
      '配置类型': getInputTypeText(item.inputType),
      '可选项': item.options || '',
      '失败原因': importResult.value.failReasons[index] || '未知错误'
    }))

    // 转换为CSV格式
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `导入失败数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('失败数据导出成功')
  } catch (error) {
    console.error('导出失败数据错误:', error)
    ElMessage.error('导出失败数据失败')
  }
}

// 页面初始化
onMounted(() => {
  console.log('🎯 页面初始化开始...')
  console.log('🔍 检查API实例:', api)
  console.log('🔍 检查service模块:', api?.service)
  console.log('🔍 检查priceSettingList方法:', api?.service?.priceSettingList)

  // 延迟一下确保所有依赖都已加载
  setTimeout(() => {
    console.log('⏰ 延迟后开始获取数据...')
    getList()
  }, 100)
})
</script>

<style scoped>
.lb-examine-goods {
  padding: 20px;
  /* background-color: #f5f5f5; */
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.page-main {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  overflow-x: auto;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
} 

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.options-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background: #fafafa;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.option-item:last-child {
  margin-bottom: 0;
}

.text-muted {
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表格容器样式 */
.table-container {
  width: 100%;
  margin: 20px 0;
  overflow-x: auto;
}

/* 表格样式优化 */
.el-table {
  width: 100% !important;
  min-width: 1200px;
  border-radius: 6px;
  overflow: hidden;
  background-color: #ffffff;
}

/* 确保所有表格行都是白色背景 */
.el-table .el-table__row {
  background-color: #ffffff !important;
}

/* 确保条纹行也是白色背景 */
.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #ffffff !important;
}

/* 鼠标悬停时的背景色 */
.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 表格头部样式 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266;
  padding: 15px 8px;
}

/* 表格单元格样式 */
.el-table .el-table__body-wrapper td {
  padding: 15px 8px;
  vertical-align: top;
  background-color: #ffffff !important;
}



/* 内容单元格样式 */
.content-cell {
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  max-width: 300px;
}

.options-cell {
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  max-width: 250px;
}

/* 固定列样式优化 */
.el-table .el-table__fixed-right {
  right: 0 !important;
}

/* 按钮间距 */  
.el-button + .el-button {
  margin-left: 10px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .lb-examine-goods {
    padding: 10px;
  }

  .page-main {
    padding: 15px;
    overflow-x: auto;
  }

  .search-form .el-form-item {
    margin-right: 10px;
    margin-bottom: 15px;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .el-table {
    min-width: 1000px;
  }
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式覆盖 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .el-table {
    min-width: auto;
  }

  .table-container {
    overflow-x: visible;
  }
}

/* 导入对话框样式 */
.import-content {
  padding: 10px 0;
}

.import-tips {
  margin-bottom: 20px;
}

.import-tips .el-alert__content p {
  margin: 5px 0;
  line-height: 1.5;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.el-upload-dragger {
  background-color: #fafafa;
  border: none;
  border-radius: 6px;
  width: 100%;
  height: 180px;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.el-upload-dragger:hover {
  background-color: #f0f9ff;
}

.el-upload-dragger .el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 50px;
}

.el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.el-upload__text em {
  color: #409eff;
  font-style: normal;
}

.el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}

/* 失败数据对话框样式 */
.fail-data-content {
  max-height: 70vh;
  overflow-y: auto;
}

.import-summary {
  margin-bottom: 20px;
}

.fail-data-table h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.fail-reasons h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.reason-list {
  max-height: 300px;
  overflow-y: auto;
}

.reason-item {
  margin-bottom: 8px;
}

.reason-item:last-child {
  margin-bottom: 0;
}

/* 失败数据表格样式优化 */
.fail-data-table .el-table {
  border-radius: 6px;
  overflow: hidden;
}

.fail-data-table .el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-weight: 600 !important;
}

.fail-data-table .el-table .el-table__body-wrapper td {
  padding: 12px 8px !important;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .fail-data-content {
    max-height: 60vh;
  }

  .fail-data-table .el-table {
    font-size: 12px;
  }
}
</style>