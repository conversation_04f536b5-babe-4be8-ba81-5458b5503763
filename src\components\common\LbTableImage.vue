<!--
  LbTableImage 表格专用图片组件
  
  专门用于表格中的图片显示，解决预览时的层级和事件冲突问题
-->

<template>
  <div class="lb-table-image" :style="containerStyle">
    <img 
      :src="imageSrc" 
      :alt="alt"
      :style="imageStyle"
      @click="handlePreview"
      @load="handleLoad"
      @error="handleError"
      class="table-image"
    />
    
    <!-- 预览遮罩 -->
    <div class="preview-overlay" @click="handlePreview">
      <el-icon class="preview-icon"><ZoomIn /></el-icon>
    </div>
  </div>

  <!-- 图片预览对话框 -->
  <el-dialog 
    v-model="previewVisible" 
    title="图片预览"
    width="80%"
    :close-on-click-modal="true"
    :z-index="3000"
    class="image-preview-dialog"
    append-to-body
  >
    <div class="preview-container" @click.stop>
      <img 
        :src="imageSrc" 
        :alt="alt"
        class="preview-image"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { computed, ref } from 'vue'
import { ZoomIn } from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: '图片'
  },
  width: {
    type: [String, Number],
    default: 80
  },
  height: {
    type: [String, Number],
    default: 50
  },
  fit: {
    type: String,
    default: 'cover',
    validator: (value) => ['fill', 'contain', 'cover', 'none', 'scale-down'].includes(value)
  },
  defaultSrc: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['load', 'error', 'preview'])

// 响应式数据
const previewVisible = ref(false)
const loading = ref(false)
const error = ref(false)

// 计算属性
const imageSrc = computed(() => {
  if (props.src) {
    return props.src
  }
  if (props.defaultSrc) {
    return props.defaultSrc
  }
  return ''
})

const containerStyle = computed(() => {
  const style = {}
  
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }
  
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  }
  
  return style
})

const imageStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    objectFit: props.fit
  }
})

// 方法
const handlePreview = (event) => {
  event.stopPropagation() // 阻止事件冒泡
  if (!imageSrc.value) return
  
  previewVisible.value = true
  emit('preview', imageSrc.value)
}

const handleLoad = (event) => {
  loading.value = false
  error.value = false
  emit('load', event)
}

const handleError = (event) => {
  loading.value = false
  error.value = true
  emit('error', event)
}
</script>

<style scoped>
.lb-table-image {
  position: relative;
  display: inline-block;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.lb-table-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.table-image {
  display: block;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
}

.lb-table-image:hover .preview-overlay {
  opacity: 1;
}

.preview-icon {
  font-size: 20px;
}

/* 预览对话框样式 */
.image-preview-dialog :deep(.el-dialog) {
  margin: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.image-preview-dialog :deep(.el-dialog__body) {
  padding: 0;
  text-align: center;
  background: #f5f5f5;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 确保预览对话框在最顶层 */
:deep(.el-overlay) {
  z-index: 3000 !important;
}

:deep(.el-dialog) {
  z-index: 3001 !important;
}
</style>
