<!--
  仪表盘视图
  系统首页，展示关键数据和统计信息
-->

<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>仪表盘</h1>
      <p class="page-subtitle">欢迎使用 今师傅后台管理系统</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="stat-icon" :style="{ color: stat.color }">
          <el-icon :size="48">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-change" :class="stat.trend">
            <el-icon>
              <TrendCharts v-if="stat.trend === 'up'" />
              <Bottom v-else />
            </el-icon>
            {{ stat.change }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 访问量趋势 -->
        <div class="chart-card">
          <div class="card-header">
            <h3>访问量趋势</h3>
            <el-button-group size="small">
              <el-button :type="chartPeriod === 'week' ? 'primary' : ''" @click="chartPeriod = 'week'">
                最近一周
              </el-button>
              <el-button :type="chartPeriod === 'month' ? 'primary' : ''" @click="chartPeriod = 'month'">
                最近一月
              </el-button>
            </el-button-group>
          </div>
          <div class="chart-content">
            <div class="mock-chart">
              <div class="chart-placeholder">
                <el-icon :size="64"><TrendCharts /></el-icon>
                <p>访问量趋势图</p>
                <p class="chart-desc">这里将显示网站访问量的时间趋势</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户分布 -->
        <div class="chart-card">
          <div class="card-header">
            <h3>用户分布</h3>
          </div>
          <div class="chart-content">
            <div class="mock-chart">
              <div class="chart-placeholder">
                <el-icon :size="64"><PieChart /></el-icon>
                <p>用户分布图</p>
                <p class="chart-desc">这里将显示用户的地域分布情况</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="card">
        <div class="card-header">
          <h3>快速操作</h3>
        </div>
        <div class="card-body">
          <div class="action-grid">
            <div class="action-item" v-for="action in quickActions" :key="action.key" @click="handleQuickAction(action)">
              <div class="action-icon">
                <el-icon :size="32">
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-content">
                <div class="action-title">{{ action.title }}</div>
                <div class="action-desc">{{ action.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <div class="card">
        <div class="card-header">
          <h3>最近活动</h3>
          <el-button size="small">查看全部</el-button>
        </div>
        <div class="card-body">
          <div class="activity-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-avatar">
                <el-avatar :size="32" :src="activity.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
              </div>
              <div class="activity-content">
                <div class="activity-text">
                  <strong>{{ activity.user }}</strong> {{ activity.action }}
                </div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
              <div class="activity-status">
                <el-tag :type="activity.status" size="small">{{ activity.statusText }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import {
  User,
  Document,
  DataAnalysis,
  Setting,
  TrendCharts,
  PieChart,
  Bottom,
  Plus,
  Edit,
  View,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const store = useStore()
const router = useRouter()

// 响应式数据
const chartPeriod = ref('week')

// 统计数据
const stats = ref([
  {
    key: 'users',
    label: '总用户数',
    value: '12,345',
    change: '+12.5%',
    trend: 'up',
    color: '#409eff',
    icon: 'User'
  },
  {
    key: 'orders',
    label: '订单总数',
    value: '8,765',
    change: '+8.2%',
    trend: 'up',
    color: '#67c23a',
    icon: 'Document'
  },
  {
    key: 'revenue',
    label: '总收入',
    value: '¥234,567',
    change: '-2.1%',
    trend: 'down',
    color: '#e6a23c',
    icon: 'DataAnalysis'
  },
  {
    key: 'conversion',
    label: '转化率',
    value: '3.45%',
    change: '+0.8%',
    trend: 'up',
    color: '#f56c6c',
    icon: 'TrendCharts'
  }
])

// 快速操作
const quickActions = ref([
  {
    key: 'add-user',
    title: '添加用户',
    description: '创建新的系统用户',
    icon: 'Plus',
    route: '/system/user'
  },
  {
    key: 'add-content',
    title: '发布内容',
    description: '创建新的文章内容',
    icon: 'Edit',
    route: '/content/article'
  },
  {
    key: 'view-stats',
    title: '查看统计',
    description: '查看详细数据统计',
    icon: 'View',
    route: '/statistics/overview'
  },
  {
    key: 'system-settings',
    title: '系统设置',
    description: '配置系统参数',
    icon: 'Setting',
    route: '/system/config'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    user: '张三',
    action: '创建了新用户',
    time: '2分钟前',
    avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
    status: 'success',
    statusText: '成功'
  },
  {
    id: 2,
    user: '李四',
    action: '更新了文章内容',
    time: '5分钟前',
    avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
    status: 'warning',
    statusText: '待审核'
  },
  {
    id: 3,
    user: '王五',
    action: '删除了过期数据',
    time: '10分钟前',
    avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
    status: 'info',
    statusText: '已完成'
  },
  {
    id: 4,
    user: '赵六',
    action: '修改了系统配置',
    time: '15分钟前',
    avatar: 'https://avatars.githubusercontent.com/u/4?v=4',
    status: 'danger',
    statusText: '需要确认'
  }
])

// 方法
const handleQuickAction = (action) => {
  if (action.route) {
    router.push(action.route)
  } else {
    ElMessage.info(`${action.title} 功能开发中...`)
  }
}

// 生命周期
onMounted(() => {
  // 记录页面访问
  store.dispatch('user/recordPageVisit', {
    path: '/dashboard',
    meta: { title: '仪表盘' }
  })
})
</script>

<style scoped>
.dashboard-container {
  padding: var(--spacing-lg);
}

.page-title {
  margin-bottom: var(--spacing-xl);
}

.page-title h1 {
  font-size: var(--font-size-xxl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: var(--transition-box-shadow);
}

.stat-card:hover {
  box-shadow: var(--box-shadow-base);
}

.stat-icon {
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-change {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.stat-change.up {
  color: var(--color-success);
}

.stat-change.down {
  color: var(--color-danger);
}

.charts-section {
  margin-bottom: var(--spacing-xl);
}

.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
}

.chart-card {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
}

.card-header h3 {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  margin: 0;
}

.chart-content {
  padding: var(--spacing-lg);
}

.mock-chart {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: var(--color-text-secondary);
}

.chart-placeholder p {
  margin: var(--spacing-sm) 0;
}

.chart-desc {
  font-size: var(--font-size-sm);
  color: var(--color-text-placeholder);
}

.quick-actions,
.recent-activities {
  margin-bottom: var(--spacing-xl);
}

.card {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.card-body {
  padding: var(--spacing-lg);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.action-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: var(--transition-base);
}

.action-item:hover {
  border-color: var(--color-primary);
  background-color: var(--bg-color-primary-light);
}

.action-icon {
  color: var(--color-primary);
}

.action-title {
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.action-desc {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
}

.activity-item:hover {
  background-color: var(--bg-color-column);
}

.activity-content {
  flex: 1;
}

.activity-text {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.activity-time {
  font-size: var(--font-size-sm);
  color: var(--color-text-placeholder);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
  }
}
</style>
