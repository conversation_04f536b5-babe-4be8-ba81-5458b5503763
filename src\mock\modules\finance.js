/**
 * 财务管理模块Mock数据
 */

import Mock from 'mockjs'
import { successResponse, createCrudMock } from '../utils.js'

const financeList = Mock.mock({
  'list|30-80': [{
    'id|+1': 1,
    'type|1': [1, 2, 3],
    'amount|100-5000.2': 1,
    'status|1': [0, 1, 2],
    'createTime': '@datetime'
  }]
}).list

createCrudMock('/api/finance', financeList)

console.log('财务管理模块Mock数据已加载')
export { financeList }
