  <!--
  合伙人邀请列表页面
  参考MarketList.vue的实现模式，按照快速开发指南重构
  对应API: /api/admin/partner/inviteList
-->

<template>
  <div class="market-partner-invite">
    <!-- 顶部导航 -->
    <TopNav title="合伙人邀请列表" />

    <div class="content-container">
      <!-- 返回按钮 -->
      <div class="back-button-container">
        <LbButton
          size="default"
          icon="ArrowLeft"
          @click="handleBack"
        >
          返回合伙人管理
        </LbButton>
      </div>
      <!-- 统计卡片 -->
      <div class="stats-container">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon user-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statsData.userNum || 0 }}</div>
                <div class="stat-label">用户数量</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon shifu-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statsData.shiFuNum || 0 }}</div>
                <div class="stat-label">师傅数量</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon vip-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statsData.vip ? '是' : '否' }}</div>
                <div class="stat-label">VIP状态</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户ID" prop="userId">
                <el-input
                  size="default"
                  v-model="searchForm.userId"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="类型" prop="type">
                <el-select
                  size="default"
                  v-model="searchForm.type"
                  placeholder="请选择类型"
                  clearable
                  style="width: 140px"
                >
                  <el-option label="全部" :value="0" />
                  <el-option label="用户" :value="1" />
                  <el-option label="师傅" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton type="primary" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton @click="handleReset">
                  重置
                </LbButton>
                <LbButton type="success" @click="handleExport" :loading="exportLoading">
                  导出
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="id" label="用户ID" width="100" align="center" />
          <el-table-column prop="nickName" label="昵称" width="150" align="center" />
          <el-table-column prop="phone" label="手机号" width="130" align="center" />
          <el-table-column prop="avatarUrl" label="头像" width="80" align="center">
            <template #default="scope">
              <el-avatar
                :size="40"
                :src="scope.row.avatarUrl"
                fit="cover"
              >
                <el-icon><User /></el-icon>
              </el-avatar>
            </template>
          </el-table-column>
          <el-table-column prop="shifu" label="类型" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.shifu === 1 ? 'warning' : 'primary'">
                {{ scope.row.shifu === 1 ? '师傅' : '用户' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="cnum" label="邀请数量" width="100" align="center" />
          <el-table-column prop="children" label="下级用户" min-width="200" align="center">
            <template #default="scope">
              <div v-if="scope.row.children && scope.row.children.length > 0">
                <el-tag
                  v-for="(child, index) in scope.row.children.slice(0, 3)"
                  :key="index"
                  size="small"
                  style="margin: 2px;"
                >
                  {{ child.nickName || child.phone }}
                </el-tag>
                <span v-if="scope.row.children.length > 3" style="color: #999; font-size: 12px;">
                  等{{ scope.row.children.length }}人
                </span>
              </div>
              <span v-else style="color: #999;">暂无</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template #default="scope">
              <LbButton size="small" type="primary" @click="handleView(scope.row)">
                查看详情
              </LbButton>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      title="邀请详情"
      v-model="detailDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentDetail.nickName }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentDetail.phone }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="currentDetail.shifu === 1 ? 'warning' : 'primary'">
              {{ currentDetail.shifu === 1 ? '师傅' : '用户' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="邀请数量">{{ currentDetail.cnum }}</el-descriptions-item>
          <el-descriptions-item label="头像">
            <el-avatar :size="50" :src="currentDetail.avatarUrl" fit="cover">
              <el-icon><User /></el-icon>
            </el-avatar>
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="currentDetail.children && currentDetail.children.length > 0" style="margin-top: 20px;">
          <h4>下级用户列表</h4>
          <el-table :data="currentDetail.children" border style="width: 100%; margin-top: 10px;">
            <el-table-column prop="nickName" label="昵称" align="center" />
            <el-table-column prop="phone" label="手机号" align="center" />
            <el-table-column prop="avatarUrl" label="头像" width="80" align="center">
              <template #default="scope">
                <el-avatar :size="30" :src="scope.row.avatarUrl" fit="cover">
                  <el-icon><User /></el-icon>
                </el-avatar>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="detailDialogVisible = false">关闭</LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Tools, Star } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()

// 响应式数据
const searchFormRef = ref()
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const detailDialogVisible = ref(false)
const currentDetail = ref(null)

// 统计数据
const statsData = ref({
  userNum: 0,
  shiFuNum: 0,
  vip: false
})

// 搜索表单
const searchForm = reactive({
  userId: '',
  type: '',
  pageNum: 1,
  pageSize: 10
})

// 获取合伙人邀请列表
const getPartnerInviteList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      userId: searchForm.userId || undefined,
      type: searchForm.type !== '' ? searchForm.type : undefined
    }

    const response = await proxy.$api.market.partnerInviteList(params)
    if (response.code === '200') {
      tableData.value = response.data.pageInfo.list || []
      total.value = response.data.pageInfo.totalCount || 0

      // 更新统计数据
      statsData.value = {
        userNum: response.data.userNum || 0,
        shiFuNum: response.data.shiFuNum || 0,
        vip: response.data.vip || false
      }
    } else {
      ElMessage.error(response.msg || '获取邀请列表失败')
    }
  } catch (error) {
    console.error('获取邀请列表失败:', error)
    ElMessage.error('获取邀请列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchForm.pageNum = 1
  getPartnerInviteList()
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    userId: '',
    type: '',
    pageNum: 1,
    pageSize: 10
  })
  getPartnerInviteList()
}

// 查看详情
const handleView = (row) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

// 导出功能
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出合伙人邀请列表')

    // 构建导出参数（排除分页参数）
    const exportParams = {}
    if (searchForm.userId) exportParams.userId = searchForm.userId
    if (searchForm.type !== '') exportParams.type = searchForm.type

    // 获取当前时间作为文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '')
    const filename = `合伙人邀请列表_${timestamp}.xlsx`

    // 构建下载URL - 使用环境变量
    const downloadUrl = `${import.meta.env.VITE_API_BASE_URL || ''}/api/admin/partner/inviteList/export`

    // 创建下载链接
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    link.target = '_blank'

    // 添加token到请求头（如果需要）
    const token = sessionStorage.getItem('minitk')
    if (token) {
      // 对于文件下载，我们需要在URL中添加token参数
      const params = new URLSearchParams(exportParams)
      params.append('token', encodeURIComponent(token))
      link.href = `${downloadUrl}?${params.toString()}`
    } else if (Object.keys(exportParams).length > 0) {
      // 如果没有token但有其他参数
      const params = new URLSearchParams(exportParams)
      link.href = `${downloadUrl}?${params.toString()}`
    }

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('导出成功')
    console.log('✅ 合伙人邀请列表导出成功')
  } catch (error) {
    console.error('❌ 导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 返回合伙人管理
const handleBack = () => {
  router.push('/market/partner')
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getPartnerInviteList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getPartnerInviteList()
}

// 页面初始化
onMounted(() => {
  // 从URL参数中获取userId
  const userId = route.query.userId
  if (userId) {
    searchForm.userId = userId
    console.log('🔗 从URL参数获取userId:', userId)
  }
  getPartnerInviteList()
})
</script>

<style scoped>
.market-partner-invite {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button-container {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.stats-container {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;

  border-radius: 8px;
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
}

.user-icon {
  background: rgba(255, 255, 255, 0.2);
}

.shifu-icon {
  background: rgba(255, 193, 7, 0.3);
}

.vip-icon {
  background: rgba(255, 215, 0, 0.3);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.search-form-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
