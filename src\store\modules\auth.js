/**
 * 认证状态管理模块

 * 
 * 功能：
 * - 用户登录/登出
 * - Token管理
 * - 用户信息获取
 * - 权限验证
 */

import { api } from '@/api-v2'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/utils/router'

const state = {
  // 访问令牌
  token: getToken(),
  // 刷新令牌
  refreshToken: '',
  // 用户信息
  userInfo: null,
  // 用户角色
  roles: [],
  // 用户权限
  permissions: [],
  // 登录状态
  isLoggedIn: false
}

const mutations = {
  // 设置Token
  SET_TOKEN(state, token) {
    state.token = token
    state.isLoggedIn = !!token
  },
  
  // 设置刷新Token
  SET_REFRESH_TOKEN(state, refreshToken) {
    state.refreshToken = refreshToken
  },
  
  // 设置用户信息
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
  },
  
  // 设置用户角色
  SET_ROLES(state, roles) {
    state.roles = Array.isArray(roles) ? roles : []
  },
  
  // 设置用户权限
  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.token = ''
    state.refreshToken = ''
    state.userInfo = null
    state.roles = []
    state.permissions = []
    state.isLoggedIn = false
  }
}

const actions = {
  // 用户登录 - 适配Cookie认证方式
  async login({ commit, dispatch }, userInfo) {
    const { username, password, rememberMe } = userInfo

    try {
      const response = await api.base.login({
        username: username.trim(),
        password,
        rememberMe
      })

      console.log('🔐 Store登录响应:', response)

      // 检查登录是否成功
      if (response.code === '200' || response.code === 200) {
        console.log('🔐 Store: 登录成功，开始设置状态')

        // 立即设置登录状态
        commit('SET_TOKEN', 'cookie-authenticated')
        localStorage.setItem('admin-token', 'cookie-authenticated')

        console.log('✅ Store: 登录状态已设置')

        // 异步检查cookie状态（不阻塞登录流程）
        setTimeout(() => {
          const autographCookie = document.cookie
            .split('; ')
            .find(row => row.startsWith('autograph='))

          if (autographCookie) {
            const cookieValue = autographCookie.split('=')[1]
            console.log('✅ Store: 检测到后端设置的autograph cookie:', cookieValue)
            // 更新为真实的cookie值
            commit('SET_TOKEN', cookieValue)
          } else {
            console.warn('⚠️ Store: 未检测到autograph cookie，但保持登录状态')
          }
        }, 100)

        // 设置默认用户信息
        const defaultUserInfo = {
          id: 1,
          username: userInfo.username,
          name: '管理员',
          avatar: '',
          email: '<EMAIL>',
          phone: '13800138000',
          status: 1,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        }

        // 如果响应中有用户信息则使用，否则使用默认信息
        const finalUserInfo = (response.data && response.data.userInfo)
          ? response.data.userInfo
          : defaultUserInfo

        commit('SET_USER_INFO', finalUserInfo)
        commit('SET_ROLES', finalUserInfo.roles || ['admin'])
        commit('SET_PERMISSIONS', finalUserInfo.permissions || ['*:*:*'])

        console.log('✅ Store: 用户信息已设置:', finalUserInfo)

        // 登录成功后，调用角色菜单接口获取动态菜单数据
        console.log('🌲 登录成功，开始获取用户角色菜单...')
        try {
          const roleMenuResponse = await api.base.getUserRoleMenu()
          console.log('🌲 角色菜单接口响应:', roleMenuResponse)

          if (roleMenuResponse.code === '200' && roleMenuResponse.data && roleMenuResponse.data.menuList) {
            // 使用角色菜单接口返回的菜单数据
            console.log('✅ 使用角色菜单接口数据设置菜单:', roleMenuResponse.data.menuList)
            await dispatch('menu/setMenuData', roleMenuResponse.data.menuList, { root: true })

            // 缓存菜单数据到本地存储，用于页面刷新恢复
            localStorage.setItem('user_menu_data', JSON.stringify(roleMenuResponse.data.menuList))
            localStorage.setItem('user_menu_timestamp', Date.now().toString())
            localStorage.setItem('user_name', roleMenuResponse.data.userName || '')

            // 更新用户信息，添加从角色菜单接口获取的用户名
            if (roleMenuResponse.data.userName) {
              const updatedUserInfo = {
                ...finalUserInfo,
                displayName: roleMenuResponse.data.userName, // 添加显示用的用户名
                roleUserName: roleMenuResponse.data.userName // 保存角色接口返回的用户名
              }
              commit('SET_USER_INFO', updatedUserInfo)
              console.log('👤 已更新用户信息，添加角色用户名:', roleMenuResponse.data.userName)
            }

            console.log('🌲 角色菜单数据设置成功并已缓存')
          } else {
            throw new Error('角色菜单接口返回数据格式错误')
          }
        } catch (error) {
          console.warn('⚠️ 获取角色菜单失败，使用降级菜单:', error)
          try {
            await dispatch('menu/useFallbackMenus', null, { root: true })
            console.log('🌲 降级菜单设置成功')
          } catch (fallbackError) {
            console.warn('⚠️ 降级菜单设置失败:', fallbackError)
          }
        }

        console.log('🎯 登录流程完成')
        return response
      } else {
        throw new Error(response.msg || '登录失败')
      }
    } catch (error) {
      console.error('🔐 Store登录失败:', error)
      throw error
    }
  },
  
  // 获取用户信息
  getUserInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      api.base.getUserInfo().then(response => {
        const { data } = response
        
        if (!data) {
          reject('验证失败，请重新登录')
        }
        
        const { roles, permissions, ...userInfo } = data
        
        // 验证角色数组
        if (!roles || roles.length <= 0) {
          reject('用户角色不能为空')
        }
        
        // 设置用户信息
        commit('SET_USER_INFO', userInfo)
        commit('SET_ROLES', roles)
        commit('SET_PERMISSIONS', permissions || [])
        
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 设置用户信息
  setUserInfo({ commit }, userInfo) {
    commit('SET_USER_INFO', userInfo)
    console.log('👤 用户信息已更新:', userInfo)
  },

  // 用户登出 - 适配Cookie认证方式
  logout({ commit, dispatch }) {
    return new Promise((resolve, reject) => {
      api.base.logout().then(() => {
        // 清除本地状态
        commit('RESET_STATE')
        removeToken() // 这会清除localStorage和cookie中的认证信息

        // 重置路由
        resetRouter()

        // 重置其他模块状态
        dispatch('routes/resetState', null, { root: true })
        dispatch('ui/resetState', null, { root: true })

        // 清除菜单缓存
        dispatch('menu/resetMenus', null, { root: true })

        console.log('🚪 登出成功，已清除所有认证信息和菜单缓存')
        resolve()
      }).catch(error => {
        console.error('🚪 登出接口调用失败，但仍清除本地状态:', error)
        // 即使登出接口失败，也要清除本地状态
        commit('RESET_STATE')
        removeToken()
        resetRouter()

        // 清除菜单缓存
        dispatch('menu/resetMenus', null, { root: true })

        resolve() // 改为resolve，因为本地清理成功就算登出成功
      })
    })
  },
  
  // 重置Token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('RESET_STATE')
      removeToken()
      resolve()
    })
  },
  
  // 刷新Token
  refreshToken({ commit, state }) {
    return new Promise((resolve, reject) => {
      if (!state.refreshToken) {
        reject('没有刷新令牌')
        return
      }
      
      // 这里调用刷新token的API
      // refreshTokenApi(state.refreshToken).then(response => {
      //   const { data } = response
      //   commit('SET_TOKEN', data.token)
      //   setToken(data.token)
      //   resolve(data.token)
      // }).catch(error => {
      //   reject(error)
      // })
      
      // 暂时模拟
      setTimeout(() => {
        resolve(state.token)
      }, 1000)
    })
  },
  
  // 重置状态
  resetState({ commit }) {
    commit('RESET_STATE')
  }
}

const getters = {
  // 获取Token（Cookie认证模式下主要用于状态判断）
  token: state => state.token,

  // 获取刷新Token
  refreshToken: state => state.refreshToken,

  // 获取用户信息
  userInfo: state => state.userInfo,

  // 获取用户角色
  roles: state => state.roles || [],

  // 获取用户权限
  permissions: state => state.permissions || [],

  // 获取登录状态 - 优先检查Cookie中的autograph
  isLoggedIn: state => {
    // 首先检查Cookie中是否有autograph
    const cookieAuth = document.cookie
      .split('; ')
      .find(row => row.startsWith('autograph='))

    if (cookieAuth) {
      const autographValue = cookieAuth.split('=')[1]
      return !!autographValue && autographValue !== 'undefined'
    }

    // 如果Cookie中没有，则检查state中的状态
    return state.isLoggedIn
  },
  
  // 获取用户名
  username: state => state.userInfo?.username || '',
  
  // 获取用户昵称
  nickname: state => state.userInfo?.nickname || state.userInfo?.username || '',
  
  // 获取用户头像
  avatar: state => state.userInfo?.avatar || '',
  
  // 检查是否有指定角色
  hasRole: state => role => {
    return (state.roles || []).includes(role)
  },

  // 检查是否有指定权限
  hasPermission: state => permission => {
    return (state.permissions || []).includes(permission)
  },

  // 检查是否有任一角色
  hasAnyRole: state => roles => {
    const userRoles = state.roles || []
    return Array.isArray(roles) ? roles.some(role => userRoles.includes(role)) : false
  },

  // 检查是否有任一权限
  hasAnyPermission: state => permissions => {
    const userPermissions = state.permissions || []
    return Array.isArray(permissions) ? permissions.some(permission => userPermissions.includes(permission)) : false
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
