<!--
  保证金日志页面
  根据API接口文档开发的保证金日志查询和管理功能
  支持师傅名称、状态筛选和分页查询
-->

<template>
  <div class="margin-log">
    <!-- 顶部导航 -->
    <TopNav title="保证金日志" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" class="search-form">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="师傅姓名" prop="coachName">
                <el-input 
                  size="default" 
                  v-model="searchForm.coachName" 
                  placeholder="请输入师傅姓名" 
                  clearable
                  style="width: 200px" 
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select 
                  size="default" 
                  v-model="searchForm.status" 
                  placeholder="请选择状态" 
                  clearable
                  style="width: 200px"
                >
                  <el-option label="用户未支付" :value="0" />
                  <el-option label="支付成功" :value="1" />
                  <el-option label="用户已申请退款" :value="-1" />
                </el-select>
              </el-form-item>
              <el-form-item label="选择城市" prop="cityId">
                <el-cascader 
                  size="default" 
                  v-model="searchForm.cityId" 
                  :options="cityOptions" 
                  :props="cascaderProps"
                  placeholder="请选择城市" 
                  clearable 
                  style="width: 200px" 
                  @change="handleCityChange" 
                />
              </el-form-item>
              <el-form-item>
                <LbButton size="default" type="primary" icon="Search" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton size="default" icon="RefreshLeft" @click="handleReset">
                  重置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table 
          v-loading="loading" 
          :data="tableData" 
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontSize: '16px',
            fontWeight: '600'
          }" 
          :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }" 
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="marginCode" label="保证金订单号" width="200" show-overflow-tooltip />
          <el-table-column prop="userId" label="用户ID" width="100" align="center" />
          <el-table-column prop="coachId" label="师傅ID" width="100" align="center" />
          <el-table-column prop="coachName" label="师傅姓名" width="120" />
          <el-table-column prop="mobile" label="手机号" width="150" />
          
          <el-table-column prop="status" label="支付状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="default">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="price" label="金额" width="100" align="center">
            <template #default="scope">
              <span>￥{{ scope.row.price || 0 }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="refundStatus" label="退款状态" width="120" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.refundStatus !== null" :type="getRefundStatusType(scope.row.refundStatus)" size="default">
                {{ getRefundStatusText(scope.row.refundStatus) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="wxTransactionId" label="微信订单号" width="200" show-overflow-tooltip />
          <el-table-column prop="createTime" label="缴纳时间" width="180" />
          <el-table-column prop="wxRefCode" label="退款订单号" width="200" show-overflow-tooltip />
          <el-table-column prop="refTime" label="申请退款时间" width="180" />
          <el-table-column prop="arriveTime" label="退款到账时间" width="180" />
          
          <el-table-column prop="refPrice" label="退款金额" width="100" align="center">
            <template #default="scope">
              <span v-if="scope.row.refPrice !== null">￥{{ scope.row.refPrice || 0 }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage 
        :page="searchForm.pageNum" 
        :page-size="searchForm.pageSize" 
        :total="total"
        @handleSizeChange="handleSizeChange" 
        @handleCurrentChange="handleCurrentChange" 
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const searchFormRef = ref()
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 城市选择相关
const cityOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true,
  checkStrictly: false,
  expandTrigger: 'hover'
}

// 搜索表单
const searchForm = reactive({
  coachName: '',
  status: '',
  cityId: [],
  pageNum: 1,
  pageSize: 10
})

// 状态类型映射
const getStatusType = (status) => {
  const statusMap = {
    0: 'warning',   // 用户未支付
    1: 'success',   // 支付成功
    '-1': 'danger'  // 用户已申请退款
  }
  return statusMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status) => {
  const statusMap = {
    0: '用户未支付',
    1: '支付成功',
    '-1': '用户已申请退款'
  }
  return statusMap[status] || '未知'
}

// 退款状态类型映射
const getRefundStatusType = (refundStatus) => {
  const statusMap = {
    1: 'warning',   // 退款中
    2: 'success'    // 退款成功
  }
  return statusMap[refundStatus] || 'info'
}

// 退款状态文本映射
const getRefundStatusText = (refundStatus) => {
  const statusMap = {
    1: '退款中',
    2: '退款成功'
  }
  return statusMap[refundStatus] || '未知'
}

// 搜索处理
const handleSearch = () => {
  searchForm.pageNum = 1
  getMarginLogList()
}

// 重置处理
const handleReset = () => {
  searchFormRef.value?.resetFields()
  searchForm.cityId = []
  searchForm.pageNum = 1
  getMarginLogList()
}

// 城市选择处理
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getMarginLogList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getMarginLogList()
}

// 获取城市数据
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await proxy.$api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

// 获取保证金日志列表
const getMarginLogList = async () => {
  try {
    loading.value = true

    // 构建请求参数
    const requestParams = { ...searchForm }
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      requestParams.cityId = searchForm.cityId.join(',')
    }

    console.log('📋 获取保证金日志列表，参数:', requestParams)
    const result = await proxy.$api.technician.marginLog(requestParams)

    if (result.code === '200') {
      tableData.value = result.data.list || []
      total.value = result.data.totalCount || 0
      console.log('✅ 保证金日志列表获取成功')
    } else {
      ElMessage.error(result.msg || '获取保证金日志失败')
    }
  } catch (error) {
    console.error('❌ 获取保证金日志失败:', error)
    ElMessage.error('获取保证金日志失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getMarginLogList()
  getCityData()
})
</script>

<style scoped>
.margin-log {
  padding: 0;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-table__body-wrapper) {
  border: none;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa !important;
}

:deep(.el-table th) {
  border-bottom: 2px solid #e4e7ed;
}

/* 按钮样式 */
.el-button+.el-button {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    margin: 10px;
    padding: 15px;
  }

  .search-form-container {
    padding: 15px;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }
}

@media (max-width: 480px) {
  .content-container {
    margin: 5px;
    padding: 10px;
  }

  .search-form-container {
    padding: 10px;
  }
}
</style>
