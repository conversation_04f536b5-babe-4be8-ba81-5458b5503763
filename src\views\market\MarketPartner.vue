<!--
  合伙人管理页面
  基于快速开发指南实现，包含合伙人列表、新增、编辑、状态管理等功能
  对应API: /api/admin/partner/*
-->

<template>
  <div class="market-partner">
    <!-- 顶部导航 -->
    <TopNav title="合伙人管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户ID" prop="userId">
                <el-input
                  size="default"
                  v-model="searchForm.userId"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="等级" prop="level">
                <el-select
                  size="default"
                  v-model="searchForm.level"
                  placeholder="请选择等级"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="等级1" :value="1" />
                  <el-option label="等级2" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="可用" :value="1" />
                  <el-option label="禁用" :value="0" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增合伙人
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontSize: '16px',
            fontWeight: '600'
          }"
          :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="userId" label="用户ID" width="100" align="center" />
          <el-table-column prop="level" label="等级" width="80" align="center" />
          <el-table-column prop="commissionRate1" label="一级分佣比例(%)" width="140" align="center">
            <template #default="scope">
              {{ scope.row.commissionRate1 }}%
            </template>
          </el-table-column>
          <el-table-column prop="commissionRate2" label="二级分佣比例(%)" width="140" align="center">
            <template #default="scope">
              {{ scope.row.commissionRate2 }}%
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" align="center" />
          <el-table-column prop="updateTime" label="更新时间" width="180" align="center" />
          <el-table-column label="操作" min-width="400" align="center" fixed="right">
            <template #default="scope">
              <LbButton
                size="large"
                type="primary"
                @click="handleEdit(scope.row)"
              >
                编辑
              </LbButton>
              <LbButton
                size="large"
                type="success"
                @click="handleInviteList(scope.row)"
              >
                邀请列表
              </LbButton>
              <LbButton
                size="large"
                type="warning"
                @click="handleCommission(scope.row)"
              >
                佣金统计
              </LbButton>
              <LbButton
                size="large"
                type="info"
                @click="handleOrders(scope.row)"
              >
                推广订单
              </LbButton>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="用户ID" prop="userId">
          <el-input
            v-model="formData.userId"
            placeholder="请输入用户ID"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-select
            v-model="formData.level"
            placeholder="请选择等级"
            style="width: 100%"
          >
            <el-option label="等级1" :value="1" />
            <el-option label="等级2" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="一级分佣比例" prop="commissionRate1">
          <el-input-number
            v-model="formData.commissionRate1"
            :min="0"
            :max="100"
            :precision="2"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #999;">%</span>
        </el-form-item>
        <el-form-item label="二级分佣比例" prop="commissionRate2">
          <el-input-number
            v-model="formData.commissionRate2"
            :min="0"
            :max="100"
            :precision="2"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #999;">%</span>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">可用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit">确定</LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()
const router = useRouter()

// 响应式数据
const searchFormRef = ref()
const formRef = ref()
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  userId: '',
  level: null,
  status: null
})

// 表单数据
const formData = reactive({
  id: null,
  userId: '',
  level: 1,
  commissionRate1: 0,
  commissionRate2: 0,
  status: 1
})

// 表单验证规则
const formRules = {
  userId: [
    { required: true, message: '请输入用户ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '用户ID必须为数字', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择等级', trigger: 'change' }
  ],
  commissionRate1: [
    { required: true, message: '请输入一级分佣比例', trigger: 'blur' }
  ],
  commissionRate2: [
    { required: true, message: '请输入二级分佣比例', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

/**
 * 获取合伙人列表
 */
const getList = async () => {
  try {
    loading.value = true
    console.log('📋 获取合伙人列表:', searchForm)

    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加搜索条件
    if (searchForm.userId) params.userId = parseInt(searchForm.userId)
    if (searchForm.level !== null && searchForm.level !== '') params.level = searchForm.level
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status

    // 调用API
    const result = await proxy.$api.market.partnerList(params)

    if (result.code === '200' || result.code === 200) {
      const data = result.data
      tableData.value = data.list || []
      total.value = data.totalCount || 0
      console.log('✅ 合伙人列表获取成功:', data)
    } else {
      ElMessage.error(result.msg || '获取列表失败')
    }
  } catch (error) {
    console.error('❌ 获取合伙人列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  console.log('🔍 执行搜索')
  searchForm.pageNum = 1
  getList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  console.log('🔄 重置搜索条件')
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    pageNum: 1,
    pageSize: 10,
    userId: '',
    level: null,
    status: null
  })
  getList()
}

/**
 * 新增合伙人
 */
const handleAdd = () => {
  console.log('➕ 新增合伙人')
  dialogTitle.value = '新增合伙人'
  isEdit.value = false
  dialogVisible.value = true

  // 重置表单数据
  Object.assign(formData, {
    id: null,
    userId: '',
    level: 1,
    commissionRate1: 0,
    commissionRate2: 0,
    status: 1
  })

  // 清除表单验证
  setTimeout(() => {
    formRef.value?.clearValidate()
  }, 100)
}

/**
 * 编辑合伙人
 */
const handleEdit = (row) => {
  console.log('✏️ 编辑合伙人:', row)
  dialogTitle.value = '编辑合伙人'
  isEdit.value = true
  dialogVisible.value = true

  // 填充表单数据
  Object.assign(formData, {
    id: row.id,
    userId: row.userId,
    level: row.level,
    commissionRate1: row.commissionRate1,
    commissionRate2: row.commissionRate2,
    status: row.status
  })

  // 清除表单验证
  setTimeout(() => {
    formRef.value?.clearValidate()
  }, 100)
}

/**
 * 状态切换
 */
const handleStatusChange = async (row) => {
  try {
    console.log('🔄 切换合伙人状态:', row)

    const result = await proxy.$api.market.partnerStatus({ id: row.id })

    if (result.code === '200' || result.code === 200) {
      ElMessage.success('状态修改成功')
      console.log('✅ 状态修改成功')
    } else {
      // 恢复原状态
      row.status = row.status === 1 ? 0 : 1
      ElMessage.error(result.msg || '状态修改失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
    console.error('❌ 状态修改失败:', error)
    ElMessage.error('状态修改失败')
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()

    console.log('💾 提交表单数据:', formData)

    let result
    if (isEdit.value) {
      // 编辑模式 - 使用等级/分佣比例调整接口
      result = await proxy.$api.market.partnerUpdateLevelAndCommission({
        id: formData.id,
        level: formData.level,
        commissionRate1: formData.commissionRate1,
        commissionRate2: formData.commissionRate2
      })
    } else {
      // 新增模式
      result = await proxy.$api.market.partnerAdd({
        userId: parseInt(formData.userId),
        level: formData.level,
        commissionRate1: formData.commissionRate1,
        commissionRate2: formData.commissionRate2,
        status: formData.status
      })
    }

    if (result.code === '200' || result.code === 200) {
      ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getList()
      console.log('✅ 操作成功')
    } else {
      ElMessage.error(result.msg || '操作失败')
    }
  } catch (error) {
    if (error.message && error.message.includes('validation')) {
      console.log('⚠️ 表单验证失败')
      return
    }
    console.error('❌ 操作失败:', error)
    ElMessage.error('操作失败')
  }
}

/**
 * 邀请列表
 */
const handleInviteList = (row) => {
  console.log('📋 查看邀请列表:', row)
  router.push({
    path: '/market/partner/invite',
    query: { userId: row.userId }
  })
}

/**
 * 佣金统计
 */
const handleCommission = (row) => {
  console.log('💰 查看佣金统计:', row)
  router.push({
    path: '/market/partner/commission',
    query: { userId: row.userId }
  })
}

/**
 * 推广订单
 */
const handleOrders = (row) => {
  console.log('📦 查看推广订单:', row)
  router.push({
    path: '/market/partner/orders',
    query: { userId: row.userId }
  })
}

/**
 * 分页大小改变
 */
const handleSizeChange = (size) => {
  console.log('📄 分页大小改变:', size)
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getList()
}

/**
 * 当前页改变
 */
const handleCurrentChange = (page) => {
  console.log('📄 当前页改变:', page)
  searchForm.pageNum = page
  getList()
}

// 页面加载时获取数据
onMounted(() => {
  console.log('🚀 合伙人管理页面初始化')
  getList()
})
</script>

<style scoped>
/* ===== 页面容器样式 ===== */
.market-partner {
  padding: 0px;
}

.content-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: calc(100vh - 60px);
}

/* ===== 搜索表单样式 ===== */
.search-form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form :deep(.el-input__inner) {
  font-size: 14px;
}

.search-form :deep(.el-select .el-input__inner) {
  font-size: 14px;
}

/* ===== 表格容器样式 ===== */
.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

/* ===== 表格样式优化 ===== */
.table-container :deep(.el-table) {
  border: none;
}

.table-container :deep(.el-table__header-wrapper) {
  border-bottom: 1px solid #ebeef5;
}

.table-container :deep(.el-table th) {
  background-color: #f5f7fa !important;
  border-bottom: 1px solid #ebeef5;
  padding: 15px 8px;
}

.table-container :deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 8px;
}

.table-container :deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

.table-container :deep(.el-table__body-wrapper) {
  border: none;
}

/* ===== 按钮样式 ===== */
.search-form :deep(.lb-button) {
  font-size: 14px;
  padding: 8px 16px;
  margin-right: 10px;
}

.table-container :deep(.lb-button) {
  font-size: 12px;
  padding: 6px 12px;
  margin-right: 8px;
}

/* ===== 对话框样式 ===== */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}

/* ===== 表单样式 ===== */
:deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

:deep(.el-input__inner) {
  font-size: 14px;
}

:deep(.el-select .el-input__inner) {
  font-size: 14px;
}

:deep(.el-input-number .el-input__inner) {
  font-size: 14px;
}

/* ===== 状态开关样式 ===== */
:deep(.el-switch__label) {
  font-size: 12px;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 15px;
  }

  .search-form :deep(.el-form-item) {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .table-container :deep(.el-table) {
    font-size: 12px;
  }
}
</style>