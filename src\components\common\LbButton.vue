<!--
  <PERSON><PERSON><PERSON><PERSON>on 通用按钮组件
 
-->

<template>
  <el-button
    :size="size"
    :type="type"
    :plain="plain"
    :round="round"
    :circle="circle"
    :loading="loading"
    :disabled="disabled"
    :icon="icon"
    :autofocus="autofocus"
    :native-type="nativeType"
    @click="handleClick"
    :class="customClass"
  >
    <slot></slot>
  </el-button>
</template>

<script setup>
import { computed } from 'vue'


const props = defineProps({
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['large', 'default', 'small', 'mini'].includes(value)
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['primary', 'success', 'warning', 'danger', 'info', 'text', 'default'].includes(value)
  },
  plain: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  },
  circle: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: <PERSON>olean,
    default: false
  },
  icon: {
    type: String,
    default: ''
  },
  autofocus: {
    type: Boolean,
    default: false
  },
  nativeType: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'submit', 'reset'].includes(value)
  }
})

// Emits定义
const emit = defineEmits(['click'])

// 计算属性
const customClass = computed(() => {
  return {
    'lb-button': true,
    [`lb-button--${props.size}`]: props.size !== 'default',
    [`lb-button--${props.type}`]: props.type !== 'default'
  }
})

// 方法
const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>

.lb-button {
  margin-right: 8px;
}

.lb-button:last-child {
  margin-right: 0;
}

/* 按钮尺寸样式 */
.lb-button--mini {
  padding: 7px 15px;
  font-size: 12px;
  border-radius: 3px;
}

.lb-button--small {
  padding: 9px 15px;
  font-size: 12px;
  border-radius: 3px;
}

/* 按钮类型样式增强 */
.lb-button--primary {
  background-color: #409eff;
  border-color: #409eff;
}

.lb-button--primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.lb-button--success {
  background-color: #67c23a;
  border-color: #67c23a;
}

.lb-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
}

.lb-button--danger {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

/* 操作按钮组样式 */
.table-operate .lb-button {
  margin-right: 5px;
}

.table-operate .lb-button:last-child {
  margin-right: 0;
}
</style>
