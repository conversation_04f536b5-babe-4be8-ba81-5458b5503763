<!--
  用户操作日志页面 - UserLog.vue
  基于接口文档实现用户操作日志查询功能
  包含：日志列表查询、筛选等功能
-->

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <TopNav title="用户操作日志" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 搜索条件 -->
              <el-form-item label="用户昵称" prop="nickName">
                <el-input
                  size="default"
                  v-model="searchForm.nickName"
                  placeholder="请输入用户昵称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="操作状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择操作状态"
                  clearable
                  style="width: 180px"
                >
                  <el-option label="查全部" :value="0" />
                  <el-option label="删除" :value="-1" />
                  <el-option label="解除黑名单" :value="1" />
                  <el-option label="拉入黑名单" :value="2" />
                  <el-option label="修改操作" :value="3" />
                </el-select>
              </el-form-item>

              <el-form-item label="选择城市" prop="cityId">
                <el-cascader
                  size="default"
                  v-model="searchForm.cityId"
                  :options="cityOptions"
                  :props="cascaderProps"
                  placeholder="请选择城市"
                  clearable
                  style="width: 200px"
                  @change="handleCityChange"
                />
              </el-form-item>
              
              <!-- 操作按钮 -->
              <el-form-item>
                <LbButton 
                  size="default" 
                  type="primary" 
                  icon="Search"
                  @click="handleSearch"
                  :loading="loading"
                >
                  搜索
                </LbButton>
                <LbButton 
                  size="default" 
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ 
            background: '#f5f7fa', 
            color: '#606266', 
            fontSize: '16px', 
            fontWeight: '600' 
          }"
          :cell-style="{ 
            fontSize: '14px', 
            padding: '12px 8px' 
          }"
          style="width: 100%"
        >
          <el-table-column prop="id" label="日志ID" width="100" align="center" />
          <el-table-column prop="userId" label="用户ID" width="100" align="center" />
          <el-table-column prop="nickName" label="用户昵称" width="150" />
          <el-table-column prop="status" label="操作状态" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="adminId" label="管理员ID" width="100" align="center">
            <template #default="{ row }">
              {{ row.adminId || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="ipv4" label="IP地址" width="130">
            <template #default="{ row }">
              {{ row.ipv4 || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="text" label="操作描述" min-width="200">
            <template #default="{ row }">
              <el-tooltip :content="row.text" placement="top" :disabled="!row.text || row.text.length <= 30">
                <span>{{ row.text || '-' }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="操作时间" width="160" />
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 城市选择相关
const cityOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true,
  checkStrictly: false,
  expandTrigger: 'hover'
}

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  nickName: '',
  status: '',
  cityId: []
})

// 搜索表单引用
const searchFormRef = ref(null)

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  const statusMap = {
    '0': '查全部',
    '-1': '删除',
    '1': '解除黑名单',
    '2': '拉入黑名单',
    '3': '修改操作'
  }
  return statusMap[status] || '未知'
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status) => {
  const typeMap = {
    '0': 'info',
    '-1': 'danger',
    '1': 'success',
    '2': 'danger',
    '3': 'warning'
  }
  return typeMap[status] || 'info'
}

/**
 * 城市变更处理
 */
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)
  if (value && value.length > 0) {
    const selectedCityId = value[value.length - 1]
    console.log('🏙️ 选中的城市ID:', selectedCityId)
  } else {
    console.log('🏙️ 清空城市选择')
  }
}

/**
 * 获取城市数据
 */
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await proxy.$api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

/**
 * 加载日志列表
 */
const loadLogList = async () => {
  try {
    loading.value = true
    console.log('🔍 开始加载用户操作日志列表，参数:', searchForm)
    
    // 构建请求参数
    const requestParams = { ...searchForm }
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      requestParams.cityId = searchForm.cityId.join(',')
    }
    
    const response = await proxy.$api.user.userLog(requestParams)
    console.log('📋 用户操作日志列表响应:', response)
    
    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0
      
      console.log(`✅ 用户操作日志列表加载成功，共 ${total.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取用户操作日志列表失败')
    }
  } catch (error) {
    console.error('❌ 加载用户操作日志列表失败:', error)
    ElMessage.error('获取用户操作日志列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  searchForm.pageNum = 1
  loadLogList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    pageNum: 1,
    pageSize: 10,
    nickName: '',
    status: '',
    cityId: []
  })
  loadLogList()
}

/**
 * 分页大小变化
 */
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  loadLogList()
}

/**
 * 当前页变化
 */
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  loadLogList()
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 用户操作日志页面初始化')
  getCityData()
  loadLogList()
})
</script>

<style scoped>
/* ===== 核心样式实现 ===== */

/* 1. 页面容器 - 基础布局 */
.page-container {
  padding: 0px;
}

/* 2. 内容容器 - 白色背景 + 圆角 + 阴影 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 3. 搜索表单 - 灰色背景区域 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 4. 表格容器 - 关键的阴影和圆角效果 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 5. 表格样式 - 深度选择器覆盖Element Plus默认样式 */
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

/* 表头样式 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

/* 表格内容样式 */
.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

/* 行悬停效果 */
.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 6. 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 7. 标签样式优化 */
.el-tag {
  font-size: 12px;
}

/* 8. 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 12px;
  }

  .search-form .el-form-item {
    margin-right: 10px;
  }
}

/* 9. 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 8px;
}
</style>
