/* CSS自定义属性系统 -  /src/style/theme.scss的现代化重构 */

:root {
  /* ===== 主色调系统 ===== */
  /* 基于原theme.scss中的$themeColor: #609beb */
  --color-primary: #609beb;
  --color-primary-light-1: #73a9ee;
  --color-primary-light-2: #86b7f1;
  --color-primary-light-3: #99c5f4;
  --color-primary-dark-1: #5689d3;
  --color-primary-dark-2: #4c77bb;

  /* 辅助色系 */
  --color-success: #67c23a;
  --color-success-light: #85ce61;
  --color-success-dark: #529b2e;
  
  --color-warning: #e6a23c;
  --color-warning-light: #ebb563;
  --color-warning-dark: #b88230;
  
  /* 基于原theme.scss中的$dangerColor: #fe6c6f */
  --color-danger: #fe6c6f;
  --color-danger-light: #fe8a8d;
  --color-danger-dark: #cb5659;
  
  --color-info: #909399;
  --color-info-light: #a6a9ad;
  --color-info-dark: #73767a;

  /* ===== 文字颜色系统 ===== */
  /* 基于原theme.scss中的$fontColor: #424753 */
  --color-text-primary: #424753;
  --color-text-regular: #606266;
  --color-text-secondary: #909399;
  /* 基于原theme.scss中的$tipsColor: #aaafbb */
  --color-text-placeholder: #aaafbb;
  --color-text-disabled: #c0c4cc;

  /* ===== 背景色系统 ===== */
  /* 基于原theme.scss中的$bgThemeColor: #f5f7fa */
  --bg-color-page: #f5f7fa;
  --bg-color-base: #ffffff;
  --bg-color-overlay: #ffffff;
  
  /* 基于原theme.scss中的$columnBgColor: #f5f7fa */
  --bg-color-column: #f5f7fa;
  
  /* 基于原theme.scss中的$primaryBgColor: #E8F1FB */
  --bg-color-primary-light: #e8f1fb;
  
  /* 基于原theme.scss中的$dangerBgColor: #fff6f7 */
  --bg-color-danger-light: #fff6f7;

  /* ===== 边框色系统 ===== */
  /* 基于原theme.scss中的$lineColor: #eff2f6 */
  --border-color-base: #eff2f6;
  --border-color-light: #e4e7ed;
  --border-color-lighter: #ebeef5;
  --border-color-extra-light: #f2f6fc;
  --border-color-dark: #d3d4d6;
  --border-color-darker: #cdd0d6;

  /* ===== 布局尺寸系统 ===== */
  /*  /src/components/sidebar.vue的双栏布局尺寸 */
  --header-height: 50px; /*   */
  --sidebar-main-width: 120px; /* 主菜单宽度 */
  --sidebar-submenu-width: 159px; /* 子菜单宽度 */
  --sidebar-width: 280px; /* 总宽度：120px + 159px + 1px边框 */
  --sidebar-collapsed-width: 120px; /* 仅主菜单宽度 */
  --footer-height: 40px;
  --ad-panel-width: 220px;

  /* ===== 间距系统 ===== */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* ===== 圆角系统 ===== */
  --border-radius-xs: 2px;
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;

  /* ===== 阴影系统 ===== */
  --box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);

  /* ===== 字体系统 ===== */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;

  /* ===== 过渡动画系统 ===== */
  --transition-duration-fast: 0.2s;
  --transition-duration-base: 0.3s;
  --transition-duration-slow: 0.5s;
  --transition-function-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-base: all var(--transition-duration-base) var(--transition-function-ease-in-out);
  --transition-fade: opacity var(--transition-duration-base) var(--transition-function-ease-in-out);

  /* ===== Z-index层级系统 ===== */
  --z-index-normal: 1;
  --z-index-top: 1000;
  --z-index-popper: 2000;
  --z-index-dialog: 3000;
  --z-index-message: 4000;
  --z-index-loading: 5000;

  /* ===== 表格系统 ===== */
  --table-border-color: var(--border-color-lighter);
  --table-header-bg: var(--bg-color-column);
  --table-row-hover-bg: var(--bg-color-primary-light);
  --table-stripe-bg: #fafafa;
  --table-cell-padding: var(--spacing-md);
  --table-header-height: 48px;
  --table-row-height: 40px;

  /* ===== 表单系统 ===== */
  --form-item-margin-bottom: 18px;
  --form-label-font-size: var(--font-size-base);
  --form-label-color: var(--color-text-regular);
  --form-label-width: 120px;
  --form-input-height: 32px;
  --form-input-padding: 8px 12px;

  /* ===== 按钮系统 ===== */
  --button-font-size: var(--font-size-base);
  --button-border-radius: var(--border-radius-sm);
  --button-padding-vertical: 8px;
  --button-padding-horizontal: 15px;
  --button-height-small: 24px;
  --button-height-default: 32px;
  --button-height-large: 40px;

  /* ===== 输入框系统 ===== */
  --input-height: 32px;
  --input-border-radius: var(--border-radius-sm);
  --input-border-color: var(--border-color-base);
  --input-focus-border-color: var(--color-primary);
  --input-disabled-bg: var(--bg-color-column);
  --input-placeholder-color: var(--color-text-placeholder);

  /* ===== 卡片系统 ===== */
  --card-border-radius: var(--border-radius-sm);
  --card-padding: 20px;
  --card-shadow: var(--box-shadow-light);
  --card-header-padding: 16px 20px;
  --card-body-padding: 20px;
  --card-footer-padding: 16px 20px;

  /* ===== 导航系统 ===== */
  --nav-item-height: 56px;
  --nav-item-padding: 0 20px;
  --nav-sub-item-height: 40px;
  --nav-sub-item-padding: 0 40px;
  --nav-icon-size: 18px;
  --nav-text-size: var(--font-size-base);

  /* ===== 面包屑系统 ===== */
  --breadcrumb-font-size: var(--font-size-sm);
  --breadcrumb-separator-margin: 0 8px;
  --breadcrumb-link-color: var(--color-primary);
  --breadcrumb-text-color: var(--color-text-secondary);

  /* ===== 分页系统 ===== */
  --pagination-item-size: 32px;
  --pagination-item-margin: 0 4px;
  --pagination-border-radius: var(--border-radius-sm);
  --pagination-active-bg: var(--color-primary);
  --pagination-hover-bg: var(--bg-color-primary-light);

  /* ===== 标签系统 ===== */
  --tag-height: 24px;
  --tag-padding: 0 8px;
  --tag-font-size: var(--font-size-xs);
  --tag-border-radius: var(--border-radius-xs);
  --tag-margin: 0 4px 4px 0;

  /* ===== 消息提示系统 ===== */
  --message-padding: 12px 16px;
  --message-border-radius: var(--border-radius-sm);
  --message-font-size: var(--font-size-base);
  --message-icon-size: 16px;
  --message-close-size: 14px;

  /* ===== 对话框系统 ===== */
  --dialog-border-radius: var(--border-radius-lg);
  --dialog-header-padding: 20px 24px 16px;
  --dialog-body-padding: 0 24px;
  --dialog-footer-padding: 16px 24px 20px;
  --dialog-title-font-size: var(--font-size-lg);

  /* ===== 抽屉系统 ===== */
  --drawer-header-height: 60px;
  --drawer-header-padding: 0 24px;
  --drawer-body-padding: 24px;
  --drawer-footer-height: 60px;
  --drawer-footer-padding: 0 24px;

  /* ===== 工具提示系统 ===== */
  --tooltip-bg: rgba(0, 0, 0, 0.8);
  --tooltip-color: #ffffff;
  --tooltip-padding: 8px 12px;
  --tooltip-border-radius: var(--border-radius-sm);
  --tooltip-font-size: var(--font-size-sm);
  --tooltip-arrow-size: 6px;

  /* ===== 加载系统 ===== */
  --loading-spinner-size: 32px;
  --loading-spinner-border: 3px;
  --loading-text-margin: 12px;
  --loading-overlay-bg: rgba(255, 255, 255, 0.9);

  /* ===== 进度条系统 ===== */
  --progress-height: 8px;
  --progress-border-radius: var(--border-radius-sm);
  --progress-bg: var(--bg-color-column);
  --progress-color: var(--color-primary);

  /* ===== 步骤条系统 ===== */
  --steps-icon-size: 32px;
  --steps-line-height: 2px;
  --steps-title-font-size: var(--font-size-base);
  --steps-description-font-size: var(--font-size-sm);
  --steps-spacing: 24px;

  /* ===== 时间轴系统 ===== */
  --timeline-item-padding: 16px 0;
  --timeline-dot-size: 12px;
  --timeline-line-width: 2px;
  --timeline-content-margin: 0 0 0 24px;

  /* ===== 统计卡片系统 ===== */
  --stat-card-padding: 24px;
  --stat-value-font-size: 32px;
  --stat-label-font-size: var(--font-size-base);
  --stat-change-font-size: var(--font-size-sm);
  --stat-icon-size: 48px;

  /* ===== 响应式断点系统 ===== */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1920px;

  /* ===== 容器最大宽度 ===== */
  --container-xs: 100%;
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;

  /* ===== 网格系统 ===== */
  --grid-columns: 24;
  --grid-gutter: 16px;
  --grid-gutter-sm: 8px;
  --grid-gutter-lg: 24px;

  /* ===== 动画持续时间 ===== */
  --animation-duration-fast: 0.15s;
  --animation-duration-base: 0.3s;
  --animation-duration-slow: 0.5s;
  --animation-duration-slower: 1s;

  /* ===== 缓动函数 ===== */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-in-back: cubic-bezier(0.36, 0, 0.66, -0.56);

  /* ===== 滤镜效果 ===== */
  --blur-sm: blur(4px);
  --blur-base: blur(8px);
  --blur-lg: blur(16px);
  --backdrop-blur: blur(8px);

  /* ===== 透明度 ===== */
  --opacity-disabled: 0.4;
  --opacity-loading: 0.6;
  --opacity-hover: 0.8;
  --opacity-mask: 0.5;
}

/* ===== 暗色主题 ===== */
[data-theme="dark"] {
  /* 重新定义暗色主题的颜色变量 */
  --color-text-primary: #e4e7ed;
  --color-text-regular: #cfcfcf;
  --color-text-secondary: #a8abb2;
  --color-text-placeholder: #6c6e72;
  --color-text-disabled: #5c5e66;

  --bg-color-page: #0a0a0a;
  --bg-color-base: #141414;
  --bg-color-overlay: #1d1e1f;
  --bg-color-column: #262727;
  --bg-color-primary-light: rgba(96, 155, 235, 0.1);
  --bg-color-danger-light: rgba(254, 108, 111, 0.1);

  --border-color-base: #4c4d4f;
  --border-color-light: #414243;
  --border-color-lighter: #363637;
  --border-color-extra-light: #2b2b2c;
  --border-color-dark: #5a5b5d;
  --border-color-darker: #6c6d6f;

  /* 表格暗色主题 */
  --table-header-bg: #262727;
  --table-stripe-bg: #1a1a1a;
  --table-row-hover-bg: rgba(96, 155, 235, 0.1);

  /* 输入框暗色主题 */
  --input-disabled-bg: #262727;

  /* 加载遮罩暗色主题 */
  --loading-overlay-bg: rgba(0, 0, 0, 0.8);

  /* 工具提示暗色主题 */
  --tooltip-bg: rgba(255, 255, 255, 0.9);
  --tooltip-color: #000000;

  /* 进度条暗色主题 */
  --progress-bg: #262727;
}

/* ===== 响应式断点媒体查询 ===== */

/* 超小屏幕 (手机竖屏) */
@media (max-width: 479px) {
  :root {
    /* 调整布局尺寸 */
    --sidebar-width: 0px;
    --sidebar-collapsed-width: 0px;
    --ad-panel-width: 0px;
    --header-height: 50px;

    /* 调整间距 */
    --spacing-xs: 2px;
    --spacing-sm: 4px;
    --spacing-md: 8px;
    --spacing-lg: 12px;
    --spacing-xl: 16px;
    --spacing-xxl: 24px;

    /* 调整字体大小 */
    --font-size-xs: 10px;
    --font-size-sm: 12px;
    --font-size-base: 13px;
    --font-size-md: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 20px;

    /* 调整组件尺寸 */
    --button-height-small: 20px;
    --button-height-default: 28px;
    --button-height-large: 36px;
    --input-height: 28px;
    --nav-item-height: 44px;
    --card-padding: 12px;
    --form-label-width: 80px;
  }
}

/* 小屏幕 (手机横屏/平板竖屏) */
@media (min-width: 480px) and (max-width: 767px) {
  :root {
    --sidebar-width: 0px;
    --sidebar-collapsed-width: 0px;
    --ad-panel-width: 0px;
    --header-height: 55px;

    --spacing-xl: 20px;
    --spacing-xxl: 32px;

    --card-padding: 16px;
    --form-label-width: 100px;
  }
}

/* 中等屏幕 (平板横屏/小桌面) */
@media (min-width: 768px) and (max-width: 1023px) {
  :root {
    --sidebar-width: 240px;
    --sidebar-collapsed-width: 80px;
    --ad-panel-width: 0px;

    --nav-item-padding: 0 16px;
    --nav-sub-item-padding: 0 32px;
  }
}

/* 大屏幕 (桌面) */
@media (min-width: 1024px) and (max-width: 1199px) {
  :root {
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 100px;
    --ad-panel-width: 0px;
  }
}

/* 超大屏幕 (大桌面) */
@media (min-width: 1200px) {
  :root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 120px;
    --ad-panel-width: 220px;

    --spacing-xxl: 64px;
    --card-padding: 24px;
  }
}

/* ===== 高分辨率屏幕适配 ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  :root {
    /* 高分辨率屏幕下的细线处理 */
    --border-width-thin: 0.5px;
  }
}

/* ===== 打印样式变量 ===== */
@media print {
  :root {
    --color-text-primary: #000000;
    --color-text-regular: #333333;
    --color-text-secondary: #666666;
    --bg-color-page: #ffffff;
    --bg-color-base: #ffffff;
    --border-color-base: #cccccc;
    --box-shadow-light: none;
    --box-shadow-base: none;
    --box-shadow-dark: none;
  }
}
