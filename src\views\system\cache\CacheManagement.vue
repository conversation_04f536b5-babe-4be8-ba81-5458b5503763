<template>
  <div class="cache-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>缓存管理</span>
        </div>
      </template>

      <div class="cache-section">
        <h3>菜单缓存</h3>
        <div class="cache-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="缓存状态">
              <el-tag :type="menuCacheStatus.loaded ? 'success' : 'danger'">
                {{ menuCacheStatus.loaded ? '已缓存' : '未缓存' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="菜单数量">
              {{ menuCacheStatus.menuCount }}
            </el-descriptions-item>
            <el-descriptions-item label="本地存储">
              <el-tag :type="menuCacheStatus.localStorage ? 'success' : 'info'">
                {{ menuCacheStatus.localStorage ? '已存储' : '未存储' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="缓存时间">
              {{ menuCacheStatus.cacheTime || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="cache-actions">
          <el-button type="primary" @click="refreshMenuCache" :loading="refreshing">
            刷新菜单缓存
          </el-button>
          <el-button type="danger" @click="clearMenuCache">
            清除菜单缓存
          </el-button>
          <el-button @click="checkCacheStatus">
            检查缓存状态
          </el-button>
        </div>
      </div>

      <div class="cache-section">
        <h3>其他缓存</h3>
        <div class="cache-actions">
          <el-button type="warning" @click="clearAllCache">
            清除所有缓存
          </el-button>
          <el-button @click="showCacheDetails">
            查看缓存详情
          </el-button>
        </div>
      </div>

      <!-- 缓存详情对话框 -->
      <el-dialog v-model="detailsVisible" title="缓存详情" width="60%">
        <div class="cache-details">
          <h4>本地存储内容</h4>
          <el-table :data="localStorageData" border>
            <el-table-column prop="key" label="键名" width="200" />
            <el-table-column prop="size" label="大小" width="100" />
            <el-table-column prop="value" label="值" show-overflow-tooltip />
          </el-table>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'

const store = useStore()

// 响应式数据
const refreshing = ref(false)
const detailsVisible = ref(false)
const localStorageData = ref([])

// 菜单缓存状态
const menuCacheStatus = reactive({
  loaded: false,
  menuCount: 0,
  localStorage: false,
  cacheTime: null
})

// 检查缓存状态
const checkCacheStatus = () => {
  // 检查Vuex状态
  menuCacheStatus.loaded = store.getters['menu/menuLoaded']
  menuCacheStatus.menuCount = store.getters['menu/mainMenuList'].length

  // 检查本地存储
  const cachedData = localStorage.getItem('user_menu_data')
  const cacheTimestamp = localStorage.getItem('user_menu_timestamp')
  
  menuCacheStatus.localStorage = !!cachedData
  
  if (cacheTimestamp) {
    const time = new Date(parseInt(cacheTimestamp))
    menuCacheStatus.cacheTime = time.toLocaleString()
  } else {
    menuCacheStatus.cacheTime = null
  }

  console.log('📊 缓存状态检查完成:', menuCacheStatus)
}

// 刷新菜单缓存
const refreshMenuCache = async () => {
  try {
    refreshing.value = true
    console.log('🔄 手动刷新菜单缓存...')
    
    await store.dispatch('menu/refreshUserMenus')
    
    ElMessage.success('菜单缓存刷新成功')
    checkCacheStatus()
  } catch (error) {
    console.error('❌ 刷新菜单缓存失败:', error)
    ElMessage.error('刷新菜单缓存失败')
  } finally {
    refreshing.value = false
  }
}

// 清除菜单缓存
const clearMenuCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除菜单缓存吗？清除后下次访问将重新加载菜单。',
      '清除缓存确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await store.dispatch('menu/clearMenuCache')
    
    ElMessage.success('菜单缓存清除成功')
    checkCacheStatus()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 清除菜单缓存失败:', error)
      ElMessage.error('清除菜单缓存失败')
    }
  }
}

// 清除所有缓存
const clearAllCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有缓存吗？这将清除所有本地存储的数据。',
      '清除所有缓存确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 清除所有localStorage
    localStorage.clear()
    
    // 重置Vuex状态
    await store.dispatch('menu/clearMenuCache')
    
    ElMessage.success('所有缓存清除成功')
    checkCacheStatus()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 清除所有缓存失败:', error)
      ElMessage.error('清除所有缓存失败')
    }
  }
}

// 显示缓存详情
const showCacheDetails = () => {
  localStorageData.value = []
  
  // 遍历localStorage
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    const value = localStorage.getItem(key)
    
    localStorageData.value.push({
      key,
      size: `${Math.round(value.length / 1024 * 100) / 100} KB`,
      value: value.length > 100 ? value.substring(0, 100) + '...' : value
    })
  }
  
  detailsVisible.value = true
}

// 组件挂载时检查缓存状态
onMounted(() => {
  checkCacheStatus()
})
</script>

<style scoped>
.cache-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cache-section {
  margin-bottom: 30px;
}

.cache-section h3 {
  margin-bottom: 15px;
  color: #303133;
}

.cache-info {
  margin-bottom: 20px;
}

.cache-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.cache-details h4 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
