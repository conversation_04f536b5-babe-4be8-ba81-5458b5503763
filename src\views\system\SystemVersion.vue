<!--
  版本管理页面
  系统版本升级和版本记录管理
-->

<template>
  <div class="lb-system-version">
    <TopNav />
    <div class="page-main">
      <!-- 当前版本信息 -->
      <el-card class="current-version-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>当前版本信息</span>
            <LbButton type="primary" @click="checkUpdate">检查更新</LbButton>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="系统版本">{{ currentVersion.version }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ currentVersion.release_time }}</el-descriptions-item>
          <el-descriptions-item label="版本类型">
            <el-tag :type="getVersionTypeColor(currentVersion.type)" size="small">
              {{ getVersionTypeText(currentVersion.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="更新状态">
            <el-tag :type="currentVersion.has_update ? 'warning' : 'success'" size="small">
              {{ currentVersion.has_update ? '有新版本' : '已是最新' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="版本描述" span="2">{{ currentVersion.description }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 版本升级 -->
      <el-card class="upgrade-card" shadow="never" v-if="currentVersion.has_update">
        <template #header>
          <div class="card-header">
            <span>版本升级</span>
          </div>
        </template>
        
        <el-alert
          title="发现新版本"
          type="warning"
          :description="`新版本 ${latestVersion.version} 已发布，建议及时升级以获得最新功能和安全修复。`"
          show-icon
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-descriptions :column="2" border style="margin-bottom: 20px;">
          <el-descriptions-item label="最新版本">{{ latestVersion.version }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ latestVersion.release_time }}</el-descriptions-item>
          <el-descriptions-item label="版本类型">
            <el-tag :type="getVersionTypeColor(latestVersion.type)" size="small">
              {{ getVersionTypeText(latestVersion.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="升级大小">{{ latestVersion.size }}</el-descriptions-item>
          <el-descriptions-item label="更新内容" span="2">{{ latestVersion.description }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="upgrade-actions">
          <LbButton type="primary" @click="startUpgrade" :loading="upgradeLoading">
            立即升级
          </LbButton>
          <LbButton @click="downloadUpdate">
            下载升级包
          </LbButton>
        </div>
      </el-card>
      
      <!-- 版本历史 -->
      <el-card class="history-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>版本历史</span>
            <LbButton type="primary" @click="getVersionHistory">刷新</LbButton>
          </div>
        </template>
        
        <el-table 
          v-loading="historyLoading" 
          :data="historyData" 
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
        >
          <el-table-column prop="version" label="版本号" width="120" />
          <el-table-column prop="type" label="版本类型" width="120">
            <template #default="scope">
              <el-tag :type="getVersionTypeColor(scope.row.type)" size="small">
                {{ getVersionTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="100" />
          <el-table-column prop="description" label="更新内容" min-width="300" />
          <el-table-column prop="release_time" label="发布时间" width="170">
            <template #default="scope">
              <div>{{ formatDate(scope.row.release_time, 1) }}</div>
              <div>{{ formatDate(scope.row.release_time, 2) }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="is_current" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.is_current ? 'success' : 'info'" size="small">
                {{ scope.row.is_current ? '当前版本' : '历史版本' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="mini"
                  type="primary"
                  @click="viewChangelog(scope.row)"
                >
                  查看详情
                </LbButton>
                <LbButton
                  v-if="!scope.row.is_current"
                  size="mini"
                  type="warning"
                  @click="rollbackVersion(scope.row)"
                >
                  回滚
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    
    <!-- 版本详情对话框 -->
    <el-dialog v-model="detailVisible" title="版本详情" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="版本号">{{ versionDetail.version }}</el-descriptions-item>
        <el-descriptions-item label="版本类型">
          <el-tag :type="getVersionTypeColor(versionDetail.type)" size="small">
            {{ getVersionTypeText(versionDetail.type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="发布时间">{{ versionDetail.release_time }}</el-descriptions-item>
        <el-descriptions-item label="大小">{{ versionDetail.size }}</el-descriptions-item>
        <el-descriptions-item label="更新内容" span="2">
          <div v-html="versionDetail.changelog"></div>
        </el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <LbButton @click="detailVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
    
    <!-- 升级进度对话框 -->
    <el-dialog v-model="upgradeVisible" title="系统升级" width="50%" :close-on-click-modal="false">
      <div class="upgrade-progress">
        <el-steps :active="upgradeStep" finish-status="success">
          <el-step title="下载升级包" />
          <el-step title="备份数据" />
          <el-step title="安装更新" />
          <el-step title="重启系统" />
        </el-steps>
        
        <div class="progress-content">
          <el-progress 
            :percentage="upgradeProgress" 
            :status="upgradeStatus"
            :stroke-width="20"
          />
          <p class="progress-text">{{ upgradeText }}</p>
        </div>
      </div>
      
      <template #footer>
        <LbButton @click="cancelUpgrade" :disabled="upgradeStep > 0">取消升级</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const historyLoading = ref(false)
const upgradeLoading = ref(false)
const upgradeVisible = ref(false)
const detailVisible = ref(false)
const historyData = ref([])
const upgradeStep = ref(0)
const upgradeProgress = ref(0)
const upgradeStatus = ref('')
const upgradeText = ref('')

// 当前版本信息
const currentVersion = reactive({
  version: 'v1.0.0',
  release_time: '2024-01-01',
  type: 'stable',
  description: '系统初始版本，包含基础功能模块',
  has_update: false
})

// 最新版本信息
const latestVersion = reactive({
  version: 'v1.1.0',
  release_time: '2024-01-15',
  type: 'stable',
  size: '25.6MB',
  description: '新增用户管理功能，修复已知问题，优化系统性能'
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 版本详情
const versionDetail = reactive({
  version: '',
  type: '',
  release_time: '',
  size: '',
  changelog: ''
})

// 方法
const getVersionHistory = async (page = 1) => {
  historyLoading.value = true
  pagination.page = page
  
  try {
    const params = new URLSearchParams({
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    
    const response = await fetch(`/api/system/version/history?${params}`)
    const result = await response.json()

    if (result.code === 200) {
      historyData.value = result.data.list || []
      pagination.total = result.data.total || 0
    } else {
      ElMessage.error(result.meg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取版本历史失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    historyLoading.value = false
  }
}

const checkUpdate = async () => {
  try {
    const response = await fetch('/api/system/version/check-update')
    const result = await response.json()
    
    if (result.code === 200) {
      if (result.data.has_update) {
        currentVersion.has_update = true
        Object.assign(latestVersion, result.data.latest_version)
        ElMessage.success('发现新版本')
      } else {
        ElMessage.info('当前已是最新版本')
      }
    } else {
      ElMessage.error(result.meg || '检查更新失败')
    }
  } catch (error) {
    console.error('检查更新失败:', error)
    ElMessage.error('检查更新失败')
  }
}

const startUpgrade = async () => {
  try {
    await ElMessageBox.confirm(
      '升级过程中系统将暂时不可用，确定要开始升级吗？',
      '升级确认',
      {
        confirmButtonText: '开始升级',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    upgradeVisible.value = true
    upgradeStep.value = 0
    upgradeProgress.value = 0
    upgradeStatus.value = ''
    upgradeText.value = '准备下载升级包...'
    
    // 模拟升级过程
    await simulateUpgrade()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('升级失败:', error)
      ElMessage.error('升级失败')
    }
  }
}

const simulateUpgrade = async () => {
  // 步骤1：下载升级包
  upgradeStep.value = 1
  upgradeText.value = '正在下载升级包...'
  for (let i = 0; i <= 100; i += 10) {
    upgradeProgress.value = i
    await new Promise(resolve => setTimeout(resolve, 200))
  }
  
  // 步骤2：备份数据
  upgradeStep.value = 2
  upgradeText.value = '正在备份数据...'
  upgradeProgress.value = 0
  for (let i = 0; i <= 100; i += 20) {
    upgradeProgress.value = i
    await new Promise(resolve => setTimeout(resolve, 300))
  }
  
  // 步骤3：安装更新
  upgradeStep.value = 3
  upgradeText.value = '正在安装更新...'
  upgradeProgress.value = 0
  for (let i = 0; i <= 100; i += 15) {
    upgradeProgress.value = i
    await new Promise(resolve => setTimeout(resolve, 250))
  }
  
  // 步骤4：重启系统
  upgradeStep.value = 4
  upgradeText.value = '升级完成，正在重启系统...'
  upgradeProgress.value = 100
  upgradeStatus.value = 'success'
  
  setTimeout(() => {
    upgradeVisible.value = false
    ElMessage.success('系统升级成功')
    currentVersion.version = latestVersion.version
    currentVersion.has_update = false
  }, 2000)
}

const downloadUpdate = () => {
  ElMessage.info('开始下载升级包...')
  // 这里可以添加下载逻辑
}

const cancelUpgrade = () => {
  upgradeVisible.value = false
}

const getVersionTypeColor = (type) => {
  const typeMap = {
    'stable': 'success',
    'beta': 'warning',
    'alpha': 'danger'
  }
  return typeMap[type] || 'info'
}

const getVersionTypeText = (type) => {
  const typeMap = {
    'stable': '稳定版',
    'beta': '测试版',
    'alpha': '内测版'
  }
  return typeMap[type] || '未知'
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const viewChangelog = async (row) => {
  try {
    const response = await fetch(`/api/system/version/detail/${row.id}`)
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(versionDetail, result.data)
      detailVisible.value = true
    } else {
      ElMessage.error(result.meg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取版本详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const rollbackVersion = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚到版本 "${row.version}" 吗？此操作可能导致数据丢失。`,
      '版本回滚确认',
      {
        confirmButtonText: '确定回滚',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/system/version/rollback/${row.id}`, {
      method: 'PUT'
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('版本回滚成功')
      getVersionHistory()
    } else {
      ElMessage.error(result.meg || '回滚失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('版本回滚失败:', error)
      ElMessage.error('回滚失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getVersionHistory(1)
}

const handleCurrentChange = (page) => {
  getVersionHistory(page)
}

// 生命周期
onMounted(() => {
  getVersionHistory()
  checkUpdate()
})
</script>

<style scoped>
.lb-system-version {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.current-version-card,
.upgrade-card,
.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upgrade-actions {
  display: flex;
  gap: 10px;
}

.upgrade-progress {
  padding: 20px 0;
}

.progress-content {
  margin-top: 30px;
  text-align: center;
}

.progress-text {
  margin-top: 15px;
  color: #606266;
  font-size: 14px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .lb-system-version {
    padding: 10px;
  }
  
  .upgrade-actions {
    flex-direction: column;
  }
  
  .table-operate {
    flex-direction: column;
  }
  
  .pagination-section {
    text-align: center;
  }
}
</style>
