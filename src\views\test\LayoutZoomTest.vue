<!--
  布局缩放测试页面
  用于验证布局容器缩放优化效果
-->

<template>
  <div class="layout-zoom-test">
    <div class="test-header">
      <h1>布局缩放优化测试</h1>
      <p>这个页面用于测试布局容器的缩放优化效果</p>
      <p class="instruction">
        <strong>测试说明：</strong>点击右上角的缩放按钮，观察整个布局是否协调缩放，不会出现组件挤压的问题。
      </p>
    </div>

    <div class="test-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="test-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>侧边栏测试</span>
              </div>
            </template>
            <p>观察左侧侧边栏在缩放时是否保持正确的位置和大小比例。</p>
            <ul>
              <li>菜单项是否清晰可见</li>
              <li>图标和文字是否协调</li>
              <li>展开/折叠功能是否正常</li>
            </ul>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="test-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>顶部导航测试</span>
              </div>
            </template>
            <p>观察顶部导航栏在缩放时的表现。</p>
            <ul>
              <li>导航栏高度是否合适</li>
              <li>按钮和文字是否清晰</li>
              <li>用户信息显示是否正常</li>
            </ul>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="test-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>主内容区测试</span>
              </div>
            </template>
            <p>观察主内容区域的缩放效果。</p>
            <ul>
              <li>内容是否按比例缩放</li>
              <li>滚动条是否正常</li>
              <li>布局是否保持完整</li>
            </ul>
          </el-card>
        </el-col>
      </el-row>

      <div class="test-section">
        <h2>缩放级别测试</h2>
        <el-table :data="zoomLevels" style="width: 100%" border>
          <el-table-column prop="level" label="缩放级别" width="120" />
          <el-table-column prop="scale" label="缩放比例" width="120" />
          <el-table-column prop="description" label="适用场景" />
          <el-table-column prop="status" label="测试状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.status === '通过' ? 'success' : 'info'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="test-section">
        <h2>功能验证清单</h2>
        <el-checkbox-group v-model="checkedFeatures">
          <div class="feature-item" v-for="feature in features" :key="feature.id">
            <el-checkbox :label="feature.id">{{ feature.name }}</el-checkbox>
            <span class="feature-desc">{{ feature.description }}</span>
          </div>
        </el-checkbox-group>
      </div>

      <div class="test-section">
        <h2>测试结果</h2>
        <el-alert
          :title="testResult.title"
          :type="testResult.type"
          :description="testResult.description"
          show-icon
          :closable="false"
        />
      </div>

      <div class="test-actions">
        <el-button type="primary" @click="runAutoTest">自动测试</el-button>
        <el-button @click="resetTest">重置测试</el-button>
        <el-button type="success" @click="exportReport">导出报告</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 缩放级别数据
const zoomLevels = ref([
  {
    level: '缩小',
    scale: '80%',
    description: '适合内容较多的页面，提高信息密度',
    status: '待测试'
  },
  {
    level: '正常',
    scale: '100%',
    description: '默认大小，标准显示效果',
    status: '待测试'
  },
  {
    level: '放大',
    scale: '120%',
    description: '适合需要放大查看的场景',
    status: '待测试'
  },
  {
    level: '超大',
    scale: '140%',
    description: '最大缩放级别，适合视力不佳的用户',
    status: '待测试'
  }
])

// 功能验证清单
const features = ref([
  {
    id: 'sidebar-position',
    name: '侧边栏位置正确',
    description: '侧边栏在缩放时保持正确的位置，不会挤压或重叠'
  },
  {
    id: 'header-layout',
    name: '顶部导航布局正常',
    description: '顶部导航栏的高度和内容布局在缩放时保持协调'
  },
  {
    id: 'content-scaling',
    name: '主内容区缩放正确',
    description: '主内容区域按比例缩放，保持可读性'
  },
  {
    id: 'scroll-behavior',
    name: '滚动行为正常',
    description: '缩放后滚动条表现正常，无异常滚动'
  },
  {
    id: 'button-position',
    name: '缩放按钮位置固定',
    description: '缩放控制按钮始终显示在正确位置'
  },
  {
    id: 'animation-smooth',
    name: '缩放动画流畅',
    description: '缩放切换时动画平滑，无卡顿现象'
  },
  {
    id: 'state-persistence',
    name: '状态持久化',
    description: '刷新页面后缩放设置得到保持'
  },
  {
    id: 'responsive-design',
    name: '响应式适配',
    description: '在不同屏幕尺寸下缩放效果正常'
  }
])

const checkedFeatures = ref([])

// 测试结果
const testResult = computed(() => {
  const totalFeatures = features.value.length
  const checkedCount = checkedFeatures.value.length
  const percentage = Math.round((checkedCount / totalFeatures) * 100)

  if (checkedCount === 0) {
    return {
      title: '测试未开始',
      type: 'info',
      description: '请开始测试各项功能，勾选已验证的功能项。'
    }
  } else if (percentage < 50) {
    return {
      title: `测试进行中 (${percentage}%)`,
      type: 'warning',
      description: `已验证 ${checkedCount}/${totalFeatures} 项功能，请继续测试剩余功能。`
    }
  } else if (percentage < 100) {
    return {
      title: `测试接近完成 (${percentage}%)`,
      type: 'primary',
      description: `已验证 ${checkedCount}/${totalFeatures} 项功能，即将完成全部测试。`
    }
  } else {
    return {
      title: '测试完成 ✅',
      type: 'success',
      description: `恭喜！已验证全部 ${totalFeatures} 项功能，布局缩放优化效果良好。`
    }
  }
})

// 监听功能验证状态变化
watch(checkedFeatures, (newVal) => {
  // 更新缩放级别测试状态
  if (newVal.includes('sidebar-position') && newVal.includes('header-layout')) {
    zoomLevels.value.forEach(level => {
      if (level.status === '待测试') {
        level.status = '通过'
      }
    })
  }
})

// 方法
const runAutoTest = () => {
  ElMessage.info('自动测试功能开发中，请手动验证各项功能')
}

const resetTest = () => {
  checkedFeatures.value = []
  zoomLevels.value.forEach(level => {
    level.status = '待测试'
  })
  ElMessage.success('测试已重置')
}

const exportReport = () => {
  const report = {
    testTime: new Date().toLocaleString(),
    totalFeatures: features.value.length,
    checkedFeatures: checkedFeatures.value.length,
    features: features.value.map(f => ({
      name: f.name,
      checked: checkedFeatures.value.includes(f.id)
    })),
    zoomLevels: zoomLevels.value
  }
  
  console.log('测试报告:', report)
  ElMessage.success('测试报告已导出到控制台')
}
</script>

<style scoped>
.layout-zoom-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.test-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
}

.test-header p {
  margin: 5px 0;
  opacity: 0.9;
}

.instruction {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 4px;
  margin-top: 15px;
}

.test-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.test-card {
  height: 200px;
  margin-bottom: 20px;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.test-section {
  margin: 30px 0;
}

.test-section h2 {
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.feature-item:hover {
  background-color: #f5f7fa;
}

.feature-desc {
  margin-left: 10px;
  color: #606266;
  font-size: 14px;
}

.test-actions {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.test-actions .el-button {
  margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-zoom-test {
    padding: 10px;
  }
  
  .test-header {
    padding: 15px;
  }
  
  .test-header h1 {
    font-size: 24px;
  }
  
  .test-content {
    padding: 15px;
  }
  
  .test-actions .el-button {
    margin: 5px;
    width: 100%;
  }
}
</style>
