/**
 * 构建配置脚本
 * 用于在构建后生成或更新 config.js 文件
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 项目根目录
const rootDir = path.resolve(__dirname, '..')
const distDir = path.join(rootDir, 'dist')
const configPath = path.join(distDir, 'config.js')

// 默认配置模板
const defaultConfig = `/**
 * 全局配置文件
 * 此文件可以在生产环境中直接修改来调整配置
 */
window.APP_CONFIG = {
  // API配置
  api: {
    // 生产环境API基础地址
    baseURL: 'http://************:8889/ims',
    
    // 请求超时时间（毫秒）
    timeout: 10000,
    
    // 是否启用请求日志
    enableLog: false
  },
  
  // 应用配置
  app: {
    // 应用名称
    name: '今师傅后台管理系统',
    
    // 应用版本
    version: '3.0.0',
    
    // 应用描述
    description: '基于Vue3的现代化后台管理系统',
    
    // 版权信息
    copyright: '© 2025 今师傅. All rights reserved.'
  },
  
  // 系统配置
  system: {
    // 上传文件大小限制（MB）
    uploadSizeLimit: 10,
    
    // 支持的文件类型
    supportedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
    
    // 分页配置
    pagination: {
      pageSizes: [10, 20, 50, 100],
      defaultPageSize: 20
    }
  },
  
  // 功能开关
  features: {
    // 是否启用多语言
    enableI18n: true,
    
    // 是否启用主题切换
    enableThemeSwitch: true,
    
    // 是否启用全屏
    enableFullscreen: true,
    
    // 是否启用搜索
    enableSearch: true,
    
    // 是否启用通知
    enableNotification: true
  }
}`

function ensureConfigFile() {
  try {
    // 检查 dist 目录是否存在
    if (!fs.existsSync(distDir)) {
      console.log('❌ dist 目录不存在，请先运行 npm run build')
      return
    }

    // 检查 config.js 是否存在
    if (!fs.existsSync(configPath)) {
      console.log('📝 创建 config.js 文件...')
      fs.writeFileSync(configPath, defaultConfig, 'utf8')
      console.log('✅ config.js 文件创建成功')
    } else {
      console.log('✅ config.js 文件已存在')
    }

    console.log(`📍 配置文件位置: ${configPath}`)
    console.log('💡 您可以直接编辑此文件来修改生产环境配置')
    
  } catch (error) {
    console.error('❌ 创建配置文件失败:', error)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  ensureConfigFile()
}

export { ensureConfigFile }
