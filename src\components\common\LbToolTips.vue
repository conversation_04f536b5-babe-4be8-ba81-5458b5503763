<!--
  提示信息组件

-->

<template>
  <div class="lb-tool-tips">
    <el-icon class="tips-icon"><InfoFilled /></el-icon>
    <span class="tips-text">
      <slot></slot>
    </span>
  </div>
</template>

<script setup>
import { InfoFilled } from '@element-plus/icons-vue'
</script>

<style scoped>
.lb-tool-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 12px;
  color: #1e40af;
}

.tips-icon {
  font-size: 14px;
  color: #3b82f6;
  flex-shrink: 0;
}

.tips-text {
  line-height: 1.4;
}

/* 不同类型的提示样式 */
.lb-tool-tips.warning {
  background-color: #fffbeb;
  border-color: #fed7aa;
  color: #92400e;
}

.lb-tool-tips.warning .tips-icon {
  color: #f59e0b;
}

.lb-tool-tips.error {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.lb-tool-tips.error .tips-icon {
  color: #ef4444;
}

.lb-tool-tips.success {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
}

.lb-tool-tips.success .tips-icon {
  color: #22c55e;
}
</style>
