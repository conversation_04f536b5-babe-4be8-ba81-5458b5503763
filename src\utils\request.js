/**
 * HTTP请求工具
 * 基于axios封装，支持Mock和真实API切换
 */

import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import config, { getBaseURL } from '@/config/api.js'
import { getToken } from '@/utils/auth'

// 创建axios实例
const request = axios.create({
  baseURL: getBaseURL(),
  timeout: config.TIMEOUT,
  withCredentials: true, // 启用Cookie支持
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求加载实例
let loadingInstance = null

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 显示加载动画
    if (config.showLoading !== false) {
      loadingInstance = ElLoading.service({
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }
    
    // 添加认证token（Cookie会自动发送，但也可以手动添加到请求头）
    const token = getToken()
    if (token) {
      // 使用Cookie认证，不需要Authorization头
      // Cookie会自动包含在请求中
      console.log('🍪 使用Cookie认证，token已存在')
    }
    
    // 请求日志
    if (config.ENABLE_LOG) {
      console.log('🚀 API请求:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL,
        params: config.params,
        data: config.data
      })
    }
    
    return config
  },
  (error) => {
    // 关闭加载动画
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
    
    console.error('❌ 请求错误:', error)
    ElMessage.error('请求配置错误')
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 关闭加载动画
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }

    const { data } = response

    // 响应日志
    if (config.ENABLE_LOG) {
      console.log('✅ API响应:', {
        url: response.config.url,
        status: response.status,
        data: data
      })
    }

    // 简化响应处理逻辑
    console.log('🔍 原始响应数据:', data)

    // 处理双层嵌套的响应格式
    if (data.success && data.data) {
      const innerData = data.data

      // 检查内层的code
      if (innerData.code === '200' || innerData.code === 200) {
        // 返回统一格式，将内层data提取出来
        const result = {
          code: 200,
          message: innerData.msg || 'success',
          data: innerData.data
        }
        console.log('✅ 双层嵌套处理结果:', result)
        return result
      } else {
        // 内层业务错误
        const message = innerData.msg || innerData.message || '请求失败'
        ElMessage.error(message)
        return Promise.reject(new Error(message))
      }
    } else if (data.code !== undefined) {
      // 单层响应格式（兼容处理）
      if (data.code === 200 || data.code === 0 || data.code === '200') {
        // 直接返回原始数据
        console.log('✅ 单层响应处理结果:', data)
        return data
      } else {
        const message = data.message || data.msg || '请求失败'
        ElMessage.error(message)
        return Promise.reject(new Error(message))
      }
    } else {
      // 直接返回数据的响应格式
      const result = {
        code: 200,
        data: data,
        message: 'success'
      }
      console.log('✅ 直接数据处理结果:', result)
      return result
    }
  },
  (error) => {
    // 关闭加载动画
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
    
    console.error('❌ 响应错误:', error)
    
    let message = '网络错误'
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data?.message || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 清除token并跳转到登录页
          const { removeToken } = require('@/utils/auth')
          removeToken()
          // 使用路由导航而不是window.location.href，避免页面刷新
          import('@/router').then(({ default: router }) => {
            router.push('/login')
          })
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      message = '网络连接失败，请检查网络'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 导出请求方法
export default request

// 便捷方法
export const get = (url, params = {}, config = {}) => {
  return request({
    method: 'GET',
    url,
    params,
    ...config
  })
}

export const post = (url, data = {}, config = {}) => {
  return request({
    method: 'POST',
    url,
    data,
    ...config
  })
}

export const put = (url, data = {}, config = {}) => {
  return request({
    method: 'PUT',
    url,
    data,
    ...config
  })
}

export const del = (url, config = {}) => {
  return request({
    method: 'DELETE',
    url,
    ...config
  })
}

export const patch = (url, data = {}, config = {}) => {
  return request({
    method: 'PATCH',
    url,
    data,
    ...config
  })
}
