import { createApp } from 'vue'
import App from './App.vue'

// 路由和状态管理
import router from './router'
import store from './store'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// Element Plus 中文语言包
import zhCn from 'element-plus/es/locale/lang/zh-cn'
// Day.js 中文语言包 (用于日期时间组件)
import 'dayjs/locale/zh-cn'

// 样式系统
import './styles/index.css'

// API系统 - V2版本
import { api } from './api-v2'

// 地图API预加载
import { preloadTencentMapAPI } from './utils/mapLoader'

// Mock数据系统现在由Vite插件处理
console.log('🔧 开发环境，Mock数据由Vite插件自动处理')

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局属性
app.config.globalProperties.$ELEMENT = { size: 'default' }

// 挂载API系统 - V2版本
app.config.globalProperties.$api = api
console.log('🔧 API-V2系统已挂载到Vue实例，可通过this.$api调用')

// 使用插件
app.use(ElementPlus, {
  locale: zhCn, // 设置Element Plus为中文
})
app.use(store)
app.use(router)

// 初始化应用
store.dispatch('initApp').then(() => {
  app.mount('#app')

  // 预加载地图API（非阻塞）
  preloadTencentMapAPI()
}).catch(error => {
  console.error('应用初始化失败:', error)
  app.mount('#app')

  // 即使应用初始化失败，也尝试预加载地图API
  preloadTencentMapAPI()
})
