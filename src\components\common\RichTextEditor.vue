<!--
  富文本编辑器组件
  基于Quill.js实现，与原版后台保持一致的编辑体验
-->

<template>
  <div class="rich-text-editor">
    <div ref="editorRef" class="editor-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  height: {
    type: String,
    default: '300px'
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const editorRef = ref()
let quillInstance = null

// 编辑器配置
const editorOptions = {
  theme: 'snow',
  placeholder: props.placeholder,
  readOnly: props.readonly,
  modules: {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'font': [] }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'script': 'sub' }, { 'script': 'super' }],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      [{ 'indent': '-1' }, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'align': [] }],
      ['blockquote', 'code-block'],
      ['link', 'image', 'video'],
      ['clean']
    ]
  }
}

// 初始化编辑器
const initEditor = () => {
  if (!editorRef.value) return

  quillInstance = new Quill(editorRef.value, editorOptions)

  // 设置编辑器高度
  const editorContainer = editorRef.value.querySelector('.ql-editor')
  if (editorContainer) {
    editorContainer.style.minHeight = props.height
  }

  // 设置初始内容
  if (props.modelValue) {
    quillInstance.root.innerHTML = props.modelValue
  }

  // 监听内容变化
  quillInstance.on('text-change', () => {
    const html = quillInstance.root.innerHTML
    const text = quillInstance.getText()
    
    // 如果内容为空，返回空字符串
    const content = text.trim() === '' ? '' : html
    
    emit('update:modelValue', content)
    emit('change', content)
  })
}

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (quillInstance && newValue !== quillInstance.root.innerHTML) {
      quillInstance.root.innerHTML = newValue || ''
    }
  }
)

// 监听只读状态变化
watch(
  () => props.readonly,
  (newValue) => {
    if (quillInstance) {
      quillInstance.enable(!newValue)
    }
  }
)

// 生命周期
onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

// 暴露方法
defineExpose({
  getQuill: () => quillInstance,
  getHTML: () => quillInstance?.root.innerHTML || '',
  getText: () => quillInstance?.getText() || '',
  setHTML: (html) => {
    if (quillInstance) {
      quillInstance.root.innerHTML = html
    }
  },
  focus: () => quillInstance?.focus(),
  blur: () => quillInstance?.blur()
})
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-container {
  background: white;
}

:deep(.ql-toolbar) {
  border-bottom: 1px solid #dcdfe6;
  background: #fafafa;
}

:deep(.ql-container) {
  border: none;
  font-size: 14px;
  line-height: 1.6;
}

:deep(.ql-editor) {
  padding: 12px 15px;
  color: #606266;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}

:deep(.ql-snow .ql-tooltip) {
  z-index: 9999;
}

/* 工具栏按钮样式 */
:deep(.ql-toolbar .ql-formats) {
  margin-right: 15px;
}

:deep(.ql-toolbar button) {
  padding: 5px;
  margin: 2px;
  border-radius: 3px;
}

:deep(.ql-toolbar button:hover) {
  background: #e6f7ff;
  color: #409eff;
}

:deep(.ql-toolbar button.ql-active) {
  background: #409eff;
  color: white;
}

/* 下拉选择器样式 */
:deep(.ql-toolbar .ql-picker) {
  color: #606266;
}

:deep(.ql-toolbar .ql-picker-options) {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.ql-toolbar .ql-picker-item:hover) {
  background: #f5f7fa;
}

/* 响应式适配 */
@media (max-width: 768px) {
  :deep(.ql-toolbar) {
    padding: 8px;
  }
  
  :deep(.ql-toolbar .ql-formats) {
    margin-right: 8px;
  }
  
  :deep(.ql-editor) {
    padding: 10px;
  }
}
</style>
