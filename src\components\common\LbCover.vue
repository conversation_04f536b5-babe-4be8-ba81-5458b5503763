<!--
  封面图片上传组件

-->

<template>
  <div class="lb-cover">
    <el-upload
      class="cover-uploader"
      action="#"
      :show-file-list="false"
      :on-change="handleChange"
      :before-upload="beforeUpload"
      :auto-upload="false"
      accept="image/*"
    >
      <img v-if="imageUrl" :src="imageUrl" class="cover-image" />
      <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
    </el-upload>
    
    <!-- 已上传的图片列表 -->
    <div v-if="fileList && fileList.length > 0" class="uploaded-list">
      <div 
        v-for="(item, index) in fileList" 
        :key="index" 
        class="uploaded-item"
      >
        <img :src="item.url || item.src" class="uploaded-image" />
        <div class="uploaded-actions">
          <el-icon @click="previewImage(item)" class="action-icon"><ZoomIn /></el-icon>
          <el-icon @click="removeImage(index)" class="action-icon"><Delete /></el-icon>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="60%">
      <img :src="previewUrl" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, ZoomIn, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  fileList: {
    type: Array,
    default: () => []
  },
  limit: {
    type: Number,
    default: 1
  },
  maxSize: {
    type: Number,
    default: 5 // MB
  }
})

// Emits
const emit = defineEmits(['selectedFiles'])

// 响应式数据
const imageUrl = ref('')
const previewVisible = ref(false)
const previewUrl = ref('')

// 计算属性
const currentFileList = computed(() => props.fileList || [])

// 监听fileList变化
watch(
  () => props.fileList,
  (newList) => {
    if (newList && newList.length > 0) {
      imageUrl.value = newList[0].url || newList[0].src
    } else {
      imageUrl.value = ''
    }
  },
  { immediate: true }
)

// 方法
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < props.maxSize

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error(`图片大小不能超过 ${props.maxSize}MB!`)
    return false
  }
  return true
}

const handleChange = (file, fileList) => {
  if (file.status === 'ready') {
    // 创建预览URL
    const reader = new FileReader()
    reader.onload = (e) => {
      const newFile = {
        name: file.name,
        url: e.target.result,
        raw: file.raw,
        size: file.size,
        type: file.raw.type
      }
      
      // 如果是单图上传，替换现有图片
      if (props.limit === 1) {
        imageUrl.value = newFile.url
        emit('selectedFiles', [newFile])
      } else {
        // 多图上传，添加到列表
        const updatedList = [...currentFileList.value, newFile]
        if (updatedList.length <= props.limit) {
          emit('selectedFiles', updatedList)
        } else {
          ElMessage.warning(`最多只能上传 ${props.limit} 张图片`)
        }
      }
    }
    reader.readAsDataURL(file.raw)
  }
}

const previewImage = (item) => {
  previewUrl.value = item.url || item.src
  previewVisible.value = true
}

const removeImage = (index) => {
  const updatedList = currentFileList.value.filter((_, i) => i !== index)
  emit('selectedFiles', updatedList)
  
  if (props.limit === 1) {
    imageUrl.value = ''
  }
}

// 获取封面图片
const getCover = (files) => {
  emit('selectedFiles', files)
}

// 暴露方法
defineExpose({
  getCover
})
</script>

<style scoped>
.lb-cover {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cover-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.cover-uploader:hover {
  border-color: var(--el-color-primary);
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-image {
  width: 148px;
  height: 148px;
  object-fit: cover;
  display: block;
}

.uploaded-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.uploaded-item {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

.uploaded-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  display: block;
}

.uploaded-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.uploaded-item:hover .uploaded-actions {
  opacity: 1;
}

.action-icon {
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.action-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .cover-uploader-icon,
  .cover-image {
    width: 120px;
    height: 120px;
  }
  
  .uploaded-image {
    width: 80px;
    height: 80px;
  }
}
</style>
