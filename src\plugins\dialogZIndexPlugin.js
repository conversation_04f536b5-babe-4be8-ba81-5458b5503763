/**
 * 弹窗z-index插件
 * 自动处理Element Plus弹窗组件的z-index问题
 */

import { nextTick } from 'vue'
import { manualFixDialog } from '@/utils/dialogFix'

// 需要处理的Element Plus组件列表
const DIALOG_COMPONENTS = [
  'ElDialog',
  'ElDrawer',
  'ElMessageBox',
  'ElNotification'
]

/**
 * 创建弹窗z-index插件
 */
export function createDialogZIndexPlugin() {
  return {
    install(app) {
      // 全局混入，为所有组件添加弹窗z-index处理
      app.mixin({
        mounted() {
          // 如果组件有弹窗相关的响应式数据，监听其变化
          this.setupDialogWatchers && this.setupDialogWatchers()
        },
        methods: {
          // 设置弹窗监听器
          setupDialogWatchers() {
            // 监听常见的弹窗显示状态
            const dialogProps = ['dialogVisible', 'visible', 'show', 'open']
            
            dialogProps.forEach(prop => {
              if (this[prop] !== undefined) {
                this.$watch(prop, (newVal) => {
                  if (newVal) {
                    // 弹窗打开时修复z-index
                    nextTick(() => {
                      manualFixDialog()
                    })
                  }
                })
              }
            })
          },
          
          // 手动修复弹窗z-index的方法
          $fixDialogZIndex() {
            manualFixDialog()
          }
        }
      })

      // 拦截Element Plus的弹窗组件
      const originalMount = app.mount
      app.mount = function(...args) {
        const result = originalMount.apply(this, args)
        
        // 应用挂载后，开始监听弹窗
        nextTick(() => {
          setupGlobalDialogListeners()
        })
        
        return result
      }
    }
  }
}

/**
 * 设置全局弹窗监听器
 */
function setupGlobalDialogListeners() {
  // 监听Element Plus弹窗的打开事件
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // 检查是否是弹窗元素
          if (isDialogElement(node)) {
            // 延迟修复，确保元素完全渲染
            setTimeout(() => {
              manualFixDialog()
            }, 50)
          }
          
          // 检查子元素中是否有弹窗
          if (node.querySelector) {
            const dialogElements = node.querySelectorAll('.el-dialog, .el-drawer, .el-message-box')
            if (dialogElements.length > 0) {
              setTimeout(() => {
                manualFixDialog()
              }, 50)
            }
          }
        }
      })
    })
  })
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
  
  console.log('🔧 全局弹窗监听器已设置')
}

/**
 * 检查元素是否是弹窗元素
 */
function isDialogElement(element) {
  if (!element.classList) return false
  
  const dialogClasses = [
    'el-overlay',
    'el-dialog',
    'el-drawer',
    'el-message-box',
    'el-notification'
  ]
  
  return dialogClasses.some(className => element.classList.contains(className))
}

/**
 * 为Vue组件添加弹窗z-index处理的装饰器
 */
export function withDialogZIndexFix(component) {
  const originalMounted = component.mounted || (() => {})
  const originalUpdated = component.updated || (() => {})
  
  component.mounted = function() {
    originalMounted.call(this)
    
    // 设置弹窗监听
    this.setupDialogWatchers && this.setupDialogWatchers()
  }
  
  component.updated = function() {
    originalUpdated.call(this)
    
    // 更新时检查是否有新的弹窗
    nextTick(() => {
      manualFixDialog()
    })
  }
  
  return component
}

/**
 * 创建一个高阶组件，自动处理弹窗z-index
 */
export function createDialogWrapper(WrappedComponent) {
  return {
    name: `DialogWrapper${WrappedComponent.name || 'Component'}`,
    components: {
      WrappedComponent
    },
    mounted() {
      // 监听弹窗状态变化
      this.setupDialogObserver()
    },
    methods: {
      setupDialogObserver() {
        // 监听子组件的弹窗状态
        this.$nextTick(() => {
          const dialogElements = this.$el.querySelectorAll('.el-dialog, .el-drawer')
          if (dialogElements.length > 0) {
            manualFixDialog()
          }
        })
      }
    },
    render() {
      return h(WrappedComponent, this.$attrs, this.$slots)
    }
  }
}

// 默认导出插件创建函数
export default createDialogZIndexPlugin
