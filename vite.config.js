import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import viteMockPlugin from './vite-mock-plugin.js'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    viteMockPlugin()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    rollupOptions: {
      output: {
        // 代码分割优化
        manualChunks: {
          'vendor': ['vue', 'vue-router', 'vuex'],
          'element': ['element-plus'],
          'utils': ['axios', 'crypto-js']
        }
      }
    }
  },
  server: {
    port: 3000,
    open: true,
    // 配置代理解决CORS问题
    proxy: {
      // 代理真实API请求
      '/api/admin': {
        // target: 'http://192.168.1.65:8889/ims',
        // target: 'http://192.168.1.29:8889/ims',
        target: 'https://m.zskj.asia/ims/',
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🔄 代理请求:', req.method, req.url, '-> ', options.target + req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('✅ 代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // 代理核心API请求（包括文件上传）
      '/api/core': {
        target: 'https://m.zskj.asia/ims/',
        // target: 'http://192.168.1.65:8889/ims',
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🔄 核心API代理请求:', req.method, req.url, '-> ', options.target + req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('✅ 核心API代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  }
})
