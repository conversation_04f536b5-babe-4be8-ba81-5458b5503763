<!--
  弹窗z-index测试页面
  用于验证页面缩放时弹窗不被侧边栏压住的问题
-->

<template>
  <div class="dialog-zindex-test">
    <div class="test-header">
      <h1>弹窗层级测试</h1>
      <p>测试页面缩放时弹窗是否会被侧边栏压住</p>
      <p class="instruction">
        <strong>测试步骤：</strong>
        1. 点击右上角缩放按钮调整页面大小
        2. 点击下方按钮打开各种弹窗
        3. 观察弹窗是否正确显示在最上层，不被侧边栏遮挡
      </p>
    </div>

    <div class="test-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="test-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>对话框测试</span>
              </div>
            </template>
            <div class="button-group">
              <el-button type="primary" @click="showDialog">打开对话框</el-button>
              <el-button type="success" @click="showFormDialog">表单对话框</el-button>
              <el-button type="info" @click="showLargeDialog">大型对话框</el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="test-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>消息提示测试</span>
              </div>
            </template>
            <div class="button-group">
              <el-button @click="showMessage">普通消息</el-button>
              <el-button type="warning" @click="showMessageBox">确认框</el-button>
              <el-button type="danger" @click="showNotification">通知</el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="test-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>其他弹出组件</span>
              </div>
            </template>
            <div class="button-group">
              <el-button @click="showDrawer">抽屉</el-button>
              <el-popover placement="top" title="标题" :width="200" trigger="click">
                <template #default>
                  <p>这是一个popover的内容</p>
                </template>
                <template #reference>
                  <el-button>Popover</el-button>
                </template>
              </el-popover>
              <el-tooltip content="这是一个tooltip" placement="top">
                <el-button>Tooltip</el-button>
              </el-tooltip>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="test-section">
        <h2>当前状态</h2>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前缩放级别">
            {{ currentZoomLevel }}
          </el-descriptions-item>
          <el-descriptions-item label="布局容器状态">
            {{ layoutStatus }}
          </el-descriptions-item>
          <el-descriptions-item label="弹窗z-index模式">
            {{ zIndexMode }}
          </el-descriptions-item>
          <el-descriptions-item label="测试状态">
            <el-tag :type="testStatus.type">{{ testStatus.text }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-section">
        <h2>测试记录</h2>
        <el-table :data="testRecords" style="width: 100%" border>
          <el-table-column prop="time" label="时间" width="120" />
          <el-table-column prop="action" label="操作" width="150" />
          <el-table-column prop="zoomLevel" label="缩放级别" width="120" />
          <el-table-column prop="result" label="结果" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.result === '正常' ? 'success' : 'danger'">
                {{ scope.row.result }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
        </el-table>
      </div>
    </div>

    <!-- 测试对话框 -->
    <el-dialog v-model="dialogVisible" title="测试对话框" width="30%">
      <p>这是一个测试对话框，用于验证在页面缩放时是否会被侧边栏遮挡。</p>
      <p>如果您能正常看到这个对话框，说明z-index设置正确。</p>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="recordTest('对话框', '正常')">确定</el-button>
      </template>
    </el-dialog>

    <!-- 表单对话框 -->
    <el-dialog v-model="formDialogVisible" title="表单对话框" width="40%">
      <el-form :model="testForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="testForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="testForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="testForm.description" type="textarea" rows="3" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="formDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="recordTest('表单对话框', '正常')">提交</el-button>
      </template>
    </el-dialog>

    <!-- 大型对话框 -->
    <el-dialog v-model="largeDialogVisible" title="大型对话框" width="60%" top="5vh">
      <div style="height: 400px; overflow-y: auto;">
        <h3>大型内容测试</h3>
        <p v-for="i in 20" :key="i">
          这是第{{ i }}段测试内容。这个对话框比较大，用于测试在不同缩放级别下大型弹窗的显示效果。
          内容应该完整显示，不被侧边栏或其他元素遮挡。
        </p>
      </div>
      <template #footer>
        <el-button @click="largeDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="recordTest('大型对话框', '正常')">确定</el-button>
      </template>
    </el-dialog>

    <!-- 抽屉 -->
    <el-drawer v-model="drawerVisible" title="测试抽屉" :with-header="true">
      <p>这是一个测试抽屉，用于验证抽屉组件的z-index是否正确。</p>
      <p>抽屉应该显示在侧边栏之上，不被遮挡。</p>
      <el-button type="primary" @click="recordTest('抽屉', '正常')">测试通过</el-button>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { getCurrentZoomLevel } from '@/utils/zIndexManager'

// 响应式数据
const dialogVisible = ref(false)
const formDialogVisible = ref(false)
const largeDialogVisible = ref(false)
const drawerVisible = ref(false)

const testForm = ref({
  username: '',
  email: '',
  description: ''
})

const testRecords = ref([])

// 计算属性
const currentZoomLevel = computed(() => {
  const level = getCurrentZoomLevel()
  const levelMap = {
    'small': '缩小 (80%)',
    'normal': '正常 (100%)',
    'large': '放大 (120%)',
    'extra-large': '超大 (140%)'
  }
  return levelMap[level] || '未知'
})

const layoutStatus = computed(() => {
  const layoutContainer = document.querySelector('.layout-container')
  if (!layoutContainer) return '未找到布局容器'
  
  const isZoomed = layoutContainer.classList.contains('zoom-small') ||
                   layoutContainer.classList.contains('zoom-large') ||
                   layoutContainer.classList.contains('zoom-extra-large')
  
  return isZoomed ? '缩放状态' : '正常状态'
})

const zIndexMode = computed(() => {
  return layoutStatus.value === '缩放状态' ? '高z-index模式' : '正常z-index模式'
})

const testStatus = computed(() => {
  const passedTests = testRecords.value.filter(record => record.result === '正常').length
  const totalTests = testRecords.value.length
  
  if (totalTests === 0) {
    return { type: 'info', text: '未开始测试' }
  } else if (passedTests === totalTests) {
    return { type: 'success', text: '全部通过' }
  } else {
    return { type: 'warning', text: `${passedTests}/${totalTests} 通过` }
  }
})

// 方法
const showDialog = () => {
  dialogVisible.value = true
  // 手动修复z-index（双重保险）
  setTimeout(() => {
    const overlay = document.querySelector('.el-overlay')
    const dialog = document.querySelector('.el-dialog')
    if (overlay) overlay.style.setProperty('z-index', '10001', 'important')
    if (dialog) dialog.style.setProperty('z-index', '10002', 'important')
  }, 100)
}

const showFormDialog = () => {
  formDialogVisible.value = true
}

const showLargeDialog = () => {
  largeDialogVisible.value = true
}

const showDrawer = () => {
  drawerVisible.value = true
}

const showMessage = () => {
  ElMessage.success('这是一个测试消息，应该显示在最上层')
  recordTest('消息提示', '正常')
}

const showMessageBox = () => {
  ElMessageBox.confirm('这是一个确认框，应该显示在最上层', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    recordTest('确认框', '正常')
  }).catch(() => {
    recordTest('确认框', '取消')
  })
}

const showNotification = () => {
  ElNotification({
    title: '通知测试',
    message: '这是一个通知，应该显示在最上层',
    type: 'info',
    duration: 3000
  })
  recordTest('通知', '正常')
}

const recordTest = (action, result) => {
  const now = new Date()
  testRecords.value.unshift({
    time: now.toLocaleTimeString(),
    action,
    zoomLevel: getCurrentZoomLevel(),
    result,
    description: `在${currentZoomLevel.value}下测试${action}`
  })
  
  // 关闭对话框
  dialogVisible.value = false
  formDialogVisible.value = false
  largeDialogVisible.value = false
  drawerVisible.value = false
}

// 生命周期
onMounted(() => {
  console.log('弹窗z-index测试页面已加载')
})
</script>

<style scoped>
.dialog-zindex-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.test-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
}

.test-header p {
  margin: 5px 0;
  opacity: 0.9;
}

.instruction {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 4px;
  margin-top: 15px;
  text-align: left;
}

.test-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.test-card {
  height: 200px;
  margin-bottom: 20px;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.button-group .el-button {
  width: 100%;
}

.test-section {
  margin: 30px 0;
}

.test-section h2 {
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-zindex-test {
    padding: 10px;
  }
  
  .test-header {
    padding: 15px;
  }
  
  .test-header h1 {
    font-size: 24px;
  }
  
  .test-content {
    padding: 15px;
  }
}
</style>
