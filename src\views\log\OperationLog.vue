<!--
  操作日志管理 - Operation Log Management
  基于快速开发指南的标准CRUD页面模板

  功能特性：
  - 搜索表单（支持多字段搜索）
  - 数据表格（统一样式和交互）
  - 分页组件（标准分页逻辑）
  - 参数弹窗（显示完整参数内容）
  - 响应式设计（移动端适配）
-->

<template>
  <div class="operation-log">
    <!-- 顶部导航 -->
    <TopNav title="操作日志" />

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单容器 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 操作人ID -->
              <el-form-item label="操作人ID" prop="adminUserId">
                <el-input
                  size="default"
                  v-model="searchForm.adminUserId"
                  placeholder="请输入操作人ID"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <!-- 请求类型 -->
              <el-form-item label="请求类型" prop="method">
                <el-select
                  v-model="searchForm.method"
                  placeholder="请选择请求类型"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="GET" value="GET" />
                  <el-option label="POST" value="POST" />
                  <el-option label="PUT" value="PUT" />
                  <el-option label="DELETE" value="DELETE" />
                </el-select>
              </el-form-item>

              <!-- 请求路径 -->
              <el-form-item label="请求路径" prop="uri">
                <el-input
                  size="default"
                  v-model="searchForm.uri"
                  placeholder="请输入请求路径"
                  clearable
                  style="width: 250px"
                />
              </el-form-item>

              <!-- 请求IP -->
              <el-form-item label="请求IP" prop="clientIp">
                <el-input
                  size="default"
                  v-model="searchForm.clientIp"
                  placeholder="请输入请求IP"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <!-- 返回状态码 -->
              <el-form-item label="状态码" prop="resultCode">
                <el-input
                  size="default"
                  v-model="searchForm.resultCode"
                  placeholder="请输入状态码"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <LbButton
                  type="primary"
                  @click="handleSearch"
                  :loading="loading"
                >
                  搜索
                </LbButton>
                <LbButton @click="handleReset">重置</LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ 
            background: '#f5f7fa', 
            color: '#606266', 
            fontSize: '16px', 
            fontWeight: '600' 
          }"
          :cell-style="{ 
            fontSize: '14px', 
            padding: '12px 8px' 
          }"
          style="width: 100%"
        >
          <!-- 操作人ID -->
          <el-table-column 
            prop="adminUserId" 
            label="操作人ID" 
            width="100" 
            align="center" 
          />
          
          <!-- 请求路径 -->
          <el-table-column 
            prop="uri" 
            label="请求路径" 
            min-width="200"
            show-overflow-tooltip
          />
          
          <!-- 请求类型 -->
          <el-table-column 
            prop="method" 
            label="请求类型" 
            width="100" 
            align="center"
          >
            <template #default="{ row }">
              <el-tag 
                :type="getMethodTagType(row.method)"
                size="small"
              >
                {{ row.method }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 请求IP -->
          <el-table-column 
            prop="clientIp" 
            label="请求IP" 
            width="150"
            show-overflow-tooltip
          />
          
          <!-- 请求参数 -->
          <el-table-column 
            prop="params" 
            label="请求参数" 
            width="150"
          >
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                size="small"
                @click="showParamsDialog(row.params)"
              >
                查看参数
              </el-button>
            </template>
          </el-table-column>
          
          <!-- 状态码 -->
          <el-table-column 
            prop="resultCode" 
            label="状态码" 
            width="100" 
            align="center"
          >
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.resultCode)"
                size="small"
              >
                {{ row.resultCode }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 耗时 -->
          <el-table-column 
            prop="durationMs" 
            label="耗时(ms)" 
            width="100" 
            align="center"
          />
          
          <!-- 异常信息 -->
          <el-table-column 
            prop="ex" 
            label="异常信息" 
            width="120"
          >
            <template #default="{ row }">
              <span v-if="row.ex">
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="showExceptionDialog(row.ex)"
                >
                  查看异常
                </el-button>
              </span>
              <span v-else class="text-success">正常</span>
            </template>
          </el-table-column>
          
          <!-- 访问时间 -->
          <el-table-column 
            prop="createTime" 
            label="访问时间" 
            width="180"
            align="center"
          />
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 参数详情弹窗 -->
    <el-dialog
      v-model="paramsDialogVisible"
      title="请求参数详情"
      width="60%"
      :before-close="handleParamsDialogClose"
    >
      <div class="params-content">
        <pre>{{ formatParams(currentParams) }}</pre>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="paramsDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyParams">复制参数</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 异常详情弹窗 -->
    <el-dialog
      v-model="exceptionDialogVisible"
      title="异常信息详情"
      width="70%"
      :before-close="handleExceptionDialogClose"
    >
      <div class="exception-content">
        <pre>{{ currentException }}</pre>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="exceptionDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyException">复制异常</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'
import api from '@/api-v2/modules'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const paramsDialogVisible = ref(false)
const exceptionDialogVisible = ref(false)
const currentParams = ref('')
const currentException = ref('')

// 搜索表单
const searchForm = reactive({
  adminUserId: '',
  method: '',
  uri: '',
  clientIp: '',
  resultCode: '',
  pageNum: 1,
  pageSize: 10
})

// 表单引用
const searchFormRef = ref()

// 获取请求类型标签样式
const getMethodTagType = (method) => {
  const typeMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return typeMap[method] || 'info'
}

// 获取状态码标签样式
const getStatusTagType = (code) => {
  if (code === '200') return 'success'
  if (code.startsWith('4')) return 'warning'
  if (code.startsWith('5')) return 'danger'
  return 'info'
}

// 显示参数弹窗
const showParamsDialog = (params) => {
  currentParams.value = params
  paramsDialogVisible.value = true
}

// 显示异常弹窗
const showExceptionDialog = (exception) => {
  currentException.value = exception
  exceptionDialogVisible.value = true
}

// 格式化参数显示
const formatParams = (params) => {
  if (!params) return ''
  try {
    // 尝试解析JSON并格式化
    const parsed = JSON.parse(params)
    return JSON.stringify(parsed, null, 2)
  } catch {
    // 如果不是JSON，直接返回原始字符串
    return params
  }
}

// 复制参数到剪贴板
const copyParams = async () => {
  try {
    await navigator.clipboard.writeText(currentParams.value)
    ElMessage.success('参数已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败')
  }
}

// 复制异常到剪贴板
const copyException = async () => {
  try {
    await navigator.clipboard.writeText(currentException.value)
    ElMessage.success('异常信息已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败')
  }
}

// 关闭参数弹窗
const handleParamsDialogClose = () => {
  paramsDialogVisible.value = false
  currentParams.value = ''
}

// 关闭异常弹窗
const handleExceptionDialogClose = () => {
  exceptionDialogVisible.value = false
  currentException.value = ''
}

// 搜索
const handleSearch = () => {
  searchForm.pageNum = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  searchForm.pageNum = 1
  loadData()
}

// 分页大小改变
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 调用API接口获取数据
    const response = await api.log.operationLog.list(searchForm)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0
    } else {
      ElMessage.error(response.msg || '获取数据失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')

    // 如果API调用失败，使用模拟数据作为后备
    const mockData = {
      code: "200",
      msg: "",
      data: {
        totalCount: 17,
        totalPage: 2,
        pageNum: searchForm.pageNum,
        pageSize: searchForm.pageSize,
        list: [
          {
            adminUserId: 1,
            uri: "/api/admin/partner/inviteList",
            method: "GET",
            clientIp: "0:0:0:0:0:0:0:1",
            params: "[3, 0, 1, 10]",
            resultCode: "200",
            durationMs: 247,
            ex: null,
            createTime: "2025-07-24 15:15:56"
          },
          {
            adminUserId: 1,
            uri: "/api/admin/operationLog/list",
            method: "GET",
            clientIp: "0:0:0:0:0:0:0:1",
            params: "[1, GET, null, null, null, 1, 10]",
            resultCode: "200",
            durationMs: 83,
            ex: null,
            createTime: "2025-07-24 14:45:47"
          }
        ]
      }
    }

    tableData.value = mockData.data.list
    total.value = mockData.data.totalCount
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.operation-log {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-container {
  flex: 1;
  padding: 20px;
  background: #f5f5f5;
  overflow: auto;
}

.search-form-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.params-content,
.exception-content {
  max-height: 400px;
  overflow: auto;
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 16px;
  line-height: 1.5;
}

.text-success {
  color: #67c23a;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }
  
  .search-form-container,
  .table-container {
    padding: 15px;
  }
}
</style>
