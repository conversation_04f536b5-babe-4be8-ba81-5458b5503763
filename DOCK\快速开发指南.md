# 快速开发指南

## 页面结构规范

### 1. Service页面统一结构

#### 完整页面结构模板
```vue
<template>
  <div class="service-xxx">
    <!-- 顶部导航 -->
    <TopNav title="页面标题" />

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单容器 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 搜索字段 -->
              <el-form-item label="字段名" prop="fieldName">
                <el-input
                  size="default"
                  v-model="searchForm.fieldName"
                  placeholder="请输入内容"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <!-- 操作按钮（搜索、重置、新增） -->
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增XXX
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
        >
          <!-- 表格列定义 -->
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
  </div>
</template>
```

#### 容器层级关系
```
service-xxx (页面根容器)
├── TopNav (顶部导航)
└── content-container (主内容容器)
    ├── search-form-container (搜索表单容器)
    │   └── search-form (搜索表单)
    ├── table-container (表格容器)
    │   └── el-table (数据表格)
    └── LbPage (分页组件)
```

## 组件使用规范

### 1. 表单组件
- **输入框**：统一使用 `size="default"`，设置明确宽度
- **下拉选择**：使用 `size="default"` 并设置明确的宽度
- **按钮**：统一使用 `LbButton` 组件，设置 `size="default"`

### 2. 容器样式规范

#### 主内容容器 (content-container)
- **作用**：包裹搜索表单、表格和分页组件的主容器
- **内边距**：20px
- **响应式**：小屏幕时内边距调整为10px

#### 搜索表单容器 (search-form-container)
- **背景颜色**：#f8f9fa（浅灰色）
- **圆角**：8px
- **内边距**：20px
- **底部间距**：20px
- **响应式**：小屏幕时内边距调整为12px

#### 表格容器 (table-container)
- **背景颜色**：#fff（白色）
- **圆角**：8px

- **溢出处理**：hidden

### 3. 字体样式规范
- **表单标签**：14px，字重500，颜色#333
- **输入框内容**：14px
- **表格标题行**：16px，字重600，颜色#606266
- **表格内容行**：14px，字重400，颜色#333
- **按钮文字**：14px

### 3. 表格样式规范
- **容器背景**：#fff（白色）
- **容器圆角**：8px
- **容器阴影**：0 2px 8px rgba(0, 0, 0, 0.1)
- **表头字体**：16px，字重600，颜色#606266，背景#f5f7fa
- **表头内边距**：15px 8px
- **表格内容字体**：14px，颜色#333
- **表格内容内边距**：12px 8px
- **悬停效果**：背景色#f8f9fa
- **无内部边框**：去掉表格内部边框，只保留外边框和表头分隔线

### 4. 按钮样式规范
- **字体大小**：14px
- **内边距**：8px 16px
- **按钮间距**：10px
- **按钮顺序**：搜索 → 重置 → 新增 → 其他操作按钮

### 5. 新增按钮位置规范
- **统一规范**：新增按钮必须放在搜索表单内，与搜索、重置按钮并列
- **不允许**：单独的操作按钮区域
- **必须使用**：`LbButton` 组件，设置 `size="default"`

## API调用模式

### 1. 统一调用方式
```javascript
// 使用 this.$api.xxx 模式
const result = await api.service.methodName(params)
```

### 2. 错误处理
```javascript
try {
  const result = await api.service.methodName(params)
  if (result.code === 200 || result.code === '200') {
    // 处理成功逻辑
  } else {
    ElMessage.error(result.meg || result.msg || '操作失败')
  }
} catch (error) {
  console.error('操作失败:', error)
  ElMessage.error('操作失败')
}
```

## 统计卡片JavaScript规范

### 1. 统计数据定义
```javascript
// 统计数据
const statsData = ref({
  all: 0,      // 全部数据
  pending: 0,  // 待处理
  approved: 0, // 已通过
  rejected: 0  // 已驳回
})

// 当前选中的统计类型
const selectedStatType = ref('all')
```

### 2. 统计卡片点击处理
```javascript
// 统计卡片点击处理
const handleStatClick = (type) => {
  selectedStatType.value = type

  // 根据点击的统计类型设置搜索条件
  switch (type) {
    case 'all':
      searchForm.status = ''
      break
    case 'pending':
      searchForm.status = 1
      break
    case 'approved':
      searchForm.status = 2
      break
    case 'rejected':
      searchForm.status = 4
      break
  }

  // 重置页码并搜索
  searchForm.pageNum = 1
  loadDataList()
}
```

### 3. 统计数据更新
```javascript
// 在获取列表数据时同时更新统计数据
const loadDataList = async () => {
  try {
    loading.value = true
    const result = await proxy.$api.service.list(searchForm)

    if (result.code === '200') {
      tableData.value = result.data.list || []
      total.value = result.data.totalCount || 0

      // 更新统计数据
      statsData.value = {
        all: result.data.all || 0,
        pending: result.data.pending || 0,
        approved: result.data.approved || 0,
        rejected: result.data.rejected || 0
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}
```

## 文件导出功能规范

### 1. 导出按钮HTML结构
```html
<LbButton
  size="default"
  type="success"
  icon="Download"
  @click="handleExport"
  :loading="exportLoading"
>
  导出
</LbButton>
```

### 2. 导出功能JavaScript实现
```javascript
// 导出加载状态
const exportLoading = ref(false)

// 导出Excel - 标准实现方式
const handleExport = () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出Excel...')

    // 构建导出参数，过滤空值
    const params = new URLSearchParams()
    Object.keys(searchForm).forEach(key => {
      if (searchForm[key] !== '' && searchForm[key] !== null && searchForm[key] !== undefined && key !== 'pageNum' && key !== 'pageSize') {
        params.append(key, searchForm[key])
      }
    })

    // 构建下载URL
    const downloadUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3004'}/api/admin/module/export`

    // 创建隐藏的下载链接
    const link = document.createElement('a')
    const fullUrl = params.toString() ? `${downloadUrl}?${params.toString()}` : downloadUrl
    link.href = fullUrl
    link.download = `数据导出_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.target = '_blank'

    // 添加token到请求（如果需要认证）
    const token = sessionStorage.getItem('minitk')
    if (token) {
      const separator = fullUrl.includes('?') ? '&' : '?'
      link.href = `${fullUrl}${separator}token=${encodeURIComponent(token)}`
    }

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('导出开始，请查看浏览器下载')
    console.log('✅ 导出Excel成功')

  } catch (error) {
    console.error('❌ 导出Excel异常:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}
```

### 3. POST方法导出实现（需要传递JSON body参数）
```javascript
// 导出Excel - POST方法，传递JSON body参数
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出Excel...')

    // 构建导出参数JSON对象
    const exportParams = {}

    // 根据实际需要添加参数
    if (searchForm.type !== '' && searchForm.type !== null && searchForm.type !== undefined) {
      exportParams.type = parseInt(searchForm.type)
    }

    if (searchForm.userId !== '' && searchForm.userId !== null && searchForm.userId !== undefined) {
      exportParams.userId = parseInt(searchForm.userId)
    }

    // 添加时间范围
    if (searchForm.startTime) exportParams.startTime = searchForm.startTime
    if (searchForm.endTime) exportParams.endTime = searchForm.endTime

    console.log('📤 导出参数:', exportParams)

    // 使用fetch发送POST请求下载文件
    const token = sessionStorage.getItem('minitk')
    const response = await fetch('/api/admin/module/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify(exportParams)
    })

    if (response.ok) {
      // 检查响应内容类型
      const contentType = response.headers.get('Content-Type')

      // 如果是JSON响应，说明可能是错误信息
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json()
        console.error('❌ 导出返回错误:', errorData)

        if (errorData.code === '-1' || errorData.code === -1) {
          // 显示具体的错误信息
          const errorMsg = errorData.msg || '导出失败'
          ElMessage.error(`导出失败: ${errorMsg}`)

          // 如果是数据库字段映射错误，给出更友好的提示
          if (errorMsg.includes('ResultMapException') || errorMsg.includes('column')) {
            ElMessage.warning('后端数据库字段映射异常，请联系技术人员修复')
          }
        } else {
          ElMessage.error(errorData.msg || '导出失败')
        }
        return
      }

      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = `数据导出_${new Date().toISOString().slice(0, 10)}.xlsx`

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      }

      // 创建blob并下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功，请查看浏览器下载')
    } else {
      // 尝试解析错误响应
      try {
        const errorText = await response.text()
        console.error('❌ 导出HTTP错误:', response.status, response.statusText, errorText)

        // 尝试解析JSON错误信息
        try {
          const errorData = JSON.parse(errorText)
          if (errorData.msg) {
            ElMessage.error(`导出失败: ${errorData.msg}`)
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        } catch (parseError) {
          throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
        }
      } catch (textError) {
        throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
      }
    }

  } catch (error) {
    console.error('❌ 导出Excel异常:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}
```

### 4. 导出功能要点说明

#### GET方法导出（URL参数）
- **使用直接下载链接**：通过创建`<a>`标签触发浏览器下载
- **参数过滤**：排除分页参数（pageNum、pageSize）和空值
- **Token认证**：如果需要认证，将token添加到URL参数中
- **文件命名**：使用有意义的文件名，包含日期
- **错误处理**：提供友好的错误提示
- **加载状态**：显示导出进度，防止重复点击

#### POST方法导出（JSON body参数）
- **使用fetch API**：发送POST请求，传递JSON格式的body参数
- **参数构建**：根据接口要求构建JSON对象，进行类型转换
- **Token认证**：通过Authorization请求头传递token
- **文件处理**：使用Blob API处理响应文件流
- **文件名解析**：从Content-Disposition响应头获取文件名
- **内存管理**：及时清理URL对象，避免内存泄漏

## 字体和样式规范

### 1. 字体大小
- **表格标题行**：16px，字重600
- **表格内容行**：14px，字重400
- **表单标签**：14px，字重500
- **输入框内容**：14px
- **按钮文字**：14px

### 2. 颜色规范
- **主要文字**：#333
- **次要文字**：#606266
- **表头背景**：#f5f7fa
- **搜索表单背景**：#f8f9fa
- **悬停背景**：#f8f9fa

### 3. 间距规范
- **表格单元格内边距**：12px 8px
- **表头单元格内边距**：15px 8px
- **搜索表单内边距**：20px
- **按钮间距**：10px

## 统计卡片规范

### 1. 统计卡片HTML结构
```html
<!-- 统计卡片 -->
<el-row :gutter="20" class="stats-cards">
  <el-col :span="6">
    <el-card
      class="stat-card clickable"
      :class="{ active: selectedStatType === 'all' }"
      @click="handleStatClick('all')"
    >
      <div class="stat-content">
        <div class="stat-value">{{ statsData.all || 0 }}</div>
        <div class="stat-label">全部数据</div>
      </div>
    </el-card>
  </el-col>
  <!-- 更多统计卡片... -->
</el-row>
```

### 2. 统计卡片样式规范
- **容器类名**：`.stats-cards` - 统计卡片容器
- **卡片类名**：`.stat-card` - 单个统计卡片（使用全局样式）
- **可点击类名**：`.clickable` - 可点击的统计卡片
- **激活状态**：`.active` - 当前选中的统计卡片
- **内容容器**：`.stat-content` - 统计内容容器
- **数值显示**：`.stat-value` - 统计数值
- **标签显示**：`.stat-label` - 统计标签

### 3. 统计卡片CSS样式
```css
/* 统计卡片容器 */
.stats-cards {
  margin-bottom: 20px;
}

/* 可点击统计卡片扩展样式 */
.stat-card.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card.clickable:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-card.clickable.active {
  border-color: var(--color-primary);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.stat-card.clickable.active .stat-value {
  color: var(--color-primary);
}

/* 统计内容布局 */
.stat-content {
  text-align: center;
  padding: 10px 0;
}
```

## 统一样式代码

### 1. 完整CSS样式模板
```css
/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}


/* 统一的搜索表单样式 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }



}
```

### 2. 数据处理模板
```javascript
// 列表数据获取
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加搜索条件
    if (searchForm.fieldName) params.fieldName = searchForm.fieldName

    const result = await api.service.methodName(params)

    if (result.code === 200 || result.code === '200') {
      const data = result.data
      if (data.list && Array.isArray(data.list)) {
        tableData.value = data.list
        total.value = data.totalCount || 0
      }
    } else {
      ElMessage.error(result.meg || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}
```

## 重要提醒

### 1. 必须遵循的规范
- ✅ 新增按钮必须放在搜索表单内
- ✅ 表格必须使用容器div包裹
- ✅ 搜索表单必须使用统一的容器样式
- ✅ 字体大小严格按照规范设置

### 2. 禁止的做法
- ❌ 不允许单独的操作按钮区域
- ❌ 不允许表格没有容器包裹
- ❌ 不允许不统一的字体大小
- ❌ 不允许不一致的样式风格

这些规范确保了项目的一致性和可维护性，所有service页面都应遵循此标准。

---

## 📥 文件下载和批量导入功能开发指南

### 1. 功能概述

基于ServicePeizhi.vue的实现，总结了文件下载模板和批量导入Excel功能的完整开发方法。

### 2. 核心功能特性

#### 2.1 下载模板功能
- 支持直接浏览器下载.xlsx/.xls文件
- 自动添加token认证参数
- 友好的用户提示信息

#### 2.2 批量导入功能
- 支持拖拽上传Excel文件
- 无文件大小限制
- 完整的导入结果处理（成功/失败）
- 失败数据详情展示和导出

### 3. API接口实现

#### 3.1 下载模板API
```javascript
/**
 * 下载服务配置模板
 * @returns {Promise} 返回模板文件下载结果
 */
priceSettingTemplate() {
  console.log('📥 下载服务配置模板API-V2请求')
  return get('/api/admin/priceSetting/template')
},
```

#### 3.2 批量导入API
```javascript
/**
 * 批量导入服务配置
 * @param {FormData} formData 包含文件的表单数据
 * @param {File} formData.file Excel文件（服务价格配置导入模板.xlsx）
 * @returns {Promise} 返回导入结果
 */
priceSettingImport(formData) {
  if (!formData || !formData.get('file')) {
    return Promise.reject(new Error('请选择要导入的文件'))
  }
  console.log('📤 批量导入服务配置API-V2请求')
  return postUpload('/api/admin/priceSetting/import', formData)
}
```

### 4. 前端实现要点

#### 4.1 下载模板实现
```javascript
// 下载服务配置模板
const handleDownloadTemplate = async () => {
  try {
    console.log('📥 开始下载服务配置模板...')

    // 直接通过浏览器下载文件
    const downloadUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://************:8889/ims'}/api/admin/priceSetting/template`

    // 创建一个隐藏的下载链接
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = '服务价格配置导入模板.xlsx'
    link.target = '_blank'

    // 添加token到请求头（如果需要）
    const token = sessionStorage.getItem('minitk')
    if (token) {
      // 对于文件下载，我们需要在URL中添加token参数
      const separator = downloadUrl.includes('?') ? '&' : '?'
      link.href = `${downloadUrl}${separator}token=${encodeURIComponent(token)}`
    }

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('模板下载开始，请查看浏览器下载')

  } catch (error) {
    console.error('下载服务配置模板失败:', error)
    ElMessage.error('下载模板失败')
  }
}
```

#### 4.2 文件上传组件配置
```vue
<el-upload
  ref="uploadRef"
  :auto-upload="false"
  :show-file-list="true"
  :limit="1"
  accept=".xlsx,.xls"
  :on-change="handleFileChange"
  :on-remove="handleFileRemove"
  drag
>
  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
  <div class="el-upload__text">
    将文件拖到此处，或<em>点击上传</em>
  </div>
  <template #tip>
    <div class="el-upload__tip">
      只能上传 xlsx/xls 文件
    </div>
  </template>
</el-upload>
```

### 5. 导入结果处理

#### 5.1 导入结果数据结构
```javascript
// API响应格式
{
  "code": "-1",
  "msg": "成功3条，失败6条",
  "data": {
    "successCount": 3,
    "failCount": 6,
    "failList": [
      {
        "type": 3,
        "serviceId": 1001,
        "problemDesc": "测试问题描述4",
        "problemContent": "测试问题详情4",
        "isRequired": 2,
        "inputType": 5,
        "options": "a,b,c"
      }
      // ... 更多失败数据
    ],
    "failReasons": [
      "报价类型必须为0或1；必填项必须为0或1；填写类型必须是1、2、3、4之一；",
      // ... 更多失败原因
    ]
  }
}
```

#### 5.2 导入结果处理逻辑
```javascript
// 处理导入结果
if (result.code === 200 || result.code === '200') {
  // 完全成功
  ElMessage.success('批量导入成功')
  importDialogVisible.value = false
  selectedFile.value = null
  // 刷新列表
  getList()
} else if (result.code === '-1') {
  // 部分成功，部分失败
  const { successCount = 0, failCount = 0, failList = [], failReasons = [] } = result.data || {}

  // 保存导入结果数据
  importResult.value = {
    successCount,
    failCount,
    failList,
    failReasons
  }

  // 显示结果消息
  ElMessage.warning(result.msg || `成功${successCount}条，失败${failCount}条`)

  // 关闭导入对话框
  importDialogVisible.value = false
  selectedFile.value = null

  // 显示失败数据详情对话框
  failDataDialogVisible.value = true

  // 刷新列表（显示成功导入的数据）
  getList()
} else {
  // 完全失败
  ElMessage.error(result.meg || result.msg || '批量导入失败')
}
```

### 6. 失败数据展示功能

#### 6.1 失败数据表格
```vue
<el-table
  :data="importResult.failList || []"
  :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
  style="width: 100%; margin-top: 10px;"
  max-height="400"
>
  <el-table-column prop="serviceId" label="服务ID" width="100" />
  <el-table-column prop="problemDesc" label="配置名称" width="150" show-overflow-tooltip />
  <el-table-column prop="problemContent" label="配置描述" min-width="200" show-overflow-tooltip />
  <el-table-column prop="type" label="报价类型" width="100" align="center">
    <template #default="scope">
      <el-tag :type="scope.row.type === 1 ? 'success' : 'warning'" size="small">
        {{ scope.row.type === 1 ? '一口价' : '报价模式' }}
      </el-tag>
    </template>
  </el-table-column>
  <!-- 更多列... -->
</el-table>
```

#### 6.2 失败原因展示
```vue
<div class="fail-reasons" v-if="importResult.failReasons && importResult.failReasons.length > 0">
  <h4>失败原因：</h4>
  <div class="reason-list">
    <div
      v-for="(reason, index) in importResult.failReasons"
      :key="index"
      class="reason-item"
    >
      <el-alert
        :title="`第 ${index + 1} 条数据：${reason}`"
        type="error"
        :closable="false"
        show-icon
        style="margin-bottom: 10px;"
      />
    </div>
  </div>
</div>
```

### 7. 导出失败数据功能

#### 7.1 导出失败数据实现
```javascript
// 导出失败数据
const exportFailData = () => {
  try {
    if (!importResult.value.failList || importResult.value.failList.length === 0) {
      ElMessage.warning('没有失败数据可导出')
      return
    }

    // 准备导出数据
    const exportData = importResult.value.failList.map((item, index) => ({
      '序号': index + 1,
      '服务ID': item.serviceId || '',
      '配置名称': item.problemDesc || '',
      '配置描述': item.problemContent || '',
      '报价类型': item.type === 1 ? '一口价' : '报价模式',
      '是否必填': item.isRequired === 1 ? '必填' : '非必填',
      '配置类型': getInputTypeText(item.inputType),
      '可选项': item.options || '',
      '失败原因': importResult.value.failReasons[index] || '未知错误'
    }))

    // 转换为CSV格式
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `导入失败数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('失败数据导出成功')
  } catch (error) {
    console.error('导出失败数据错误:', error)
    ElMessage.error('导出失败数据失败')
  }
}
```

### 8. 按钮配置

#### 8.1 操作按钮布局
```vue
<LbButton
  size="default"
  type="success"
  icon="Download"
  @click="handleDownloadTemplate"
>
  下载服务配置模板
</LbButton>
<LbButton
  size="default"
  type="warning"
  icon="Upload"
  @click="handleBatchImport"
>
  批量导入服务配置
</LbButton>
```

### 9. 响应式变量配置

#### 9.1 必需的响应式变量
```javascript
const importDialogVisible = ref(false)
const importLoading = ref(false)
const selectedFile = ref(null)
const uploadRef = ref(null)
const failDataDialogVisible = ref(false)
const importResult = ref({
  successCount: 0,
  failCount: 0,
  failList: [],
  failReasons: []
})
```

### 10. 样式配置

#### 10.1 导入对话框样式
```css
/* 导入对话框样式 */
.import-content {
  max-height: 70vh;
  overflow-y: auto;
}

.import-summary {
  margin-bottom: 20px;
}

.fail-data-table h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.fail-reasons h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.reason-list {
  max-height: 300px;
  overflow-y: auto;
}

/* 失败数据表格样式优化 */
.fail-data-table .el-table {
  border-radius: 6px;
  overflow: hidden;
}

.fail-data-table .el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-weight: 600 !important;
}

.fail-data-table .el-table .el-table__body-wrapper td {
  padding: 12px 8px !important;
}
```

### 11. 开发要点总结

#### 11.1 关键技术要点
- **文件下载**：使用直接链接下载，避免API响应处理复杂性
- **文件上传**：使用FormData和postUpload方法
- **结果处理**：根据不同的响应码进行分类处理
- **失败数据展示**：使用表格和警告组件展示详细信息
- **数据导出**：使用CSV格式导出失败数据

#### 11.2 用户体验优化
- **加载状态**：导入过程显示loading状态
- **操作反馈**：成功/失败都有明确的消息提示
- **数据展示**：失败数据以表格形式清晰展示
- **导出功能**：支持导出失败数据进行修正

#### 11.3 错误处理
- **文件验证**：检查文件格式和是否选择文件
- **API错误**：捕获并显示API调用错误
- **数据处理**：安全处理API响应数据
- **用户提示**：友好的错误提示信息

#### 11.4 开发检查清单
- [ ] 下载模板按钮功能正常
- [ ] 批量导入对话框正常显示
- [ ] 文件上传组件配置正确
- [ ] 导入结果处理逻辑完整
- [ ] 失败数据展示功能正常
- [ ] 导出失败数据功能正常
- [ ] 响应式变量配置完整
- [ ] 样式配置符合规范
- [ ] 错误处理机制完善
- [ ] 用户体验友好

### 12. 常见问题解决

#### 12.1 下载模板无效
- 检查API接口地址是否正确
- 确认token参数是否正确添加
- 验证浏览器是否支持下载功能

#### 12.2 文件上传失败
- 检查文件格式是否为.xlsx/.xls
- 确认FormData构建是否正确
- 验证API接口参数名是否为'file'

#### 12.3 失败数据不显示
- 检查API响应数据结构是否正确
- 确认failList和failReasons数据是否存在
- 验证表格组件配置是否正确

通过遵循本指南，可以快速实现完整的文件下载和批量导入功能，确保用户体验和功能完整性。

---

## 🔄 状态切换组件最佳实践指南

### 1. 问题背景

在开发过程中发现，使用 `el-switch` 组件进行状态切换时，容易出现以下问题：
- 页面初始化时异常触发状态切换API
- 数据类型不匹配导致的意外触发
- 状态修改后刷新页面状态丢失

### 2. 推荐解决方案

#### 2.1 使用按钮替代开关组件

**❌ 不推荐的做法（容易异常触发）：**
```vue
<el-table-column label="状态" width="100">
  <template #default="scope">
    <el-switch
      v-model="scope.row.status"
      :active-value="1"
      :inactive-value="-1"
      @change="handleStatusChange(scope.row)"
    />
  </template>
</el-table-column>
```

**✅ 推荐的做法（安全可控）：**
```vue
<el-table-column label="状态" width="120">
  <template #default="scope">
    <el-button
      :type="scope.row.status === 1 ? 'success' : 'danger'"
      :loading="scope.row.statusLoading"
      size="small"
      @click="handleStatusToggle(scope.row)"
    >
      {{ scope.row.status === 1 ? '可用' : '不可用' }}
    </el-button>
  </template>
</el-table-column>
```

#### 2.2 状态值统一处理

在数据加载时统一状态值格式，避免类型不匹配：

```javascript
// 处理API响应数据时统一状态值
const getTableDataList = async (flag) => {
  try {
    const result = await api.service.bannerList(params)

    if (result.code === '200') {
      const data = result.data
      // 统一状态值格式，避免类型不匹配
      tableData.value = (data.list || []).map(item => ({
        ...item,
        // 统一状态值：0转换为1（可用），其他保持原值
        status: item.status === 0 ? 1 : item.status,
        statusLoading: false
      }))
      total.value = data.totalCount || data.total || 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}
```

#### 2.3 安全的状态切换方法

```javascript
// 状态切换方法 - 使用按钮点击方式，避免响应式触发问题
const handleStatusToggle = async (row) => {
  // 防止频繁调用状态切换API
  if (isStatusChanging.value) {
    console.log('⚠️ 状态切换API正在调用中，跳过重复请求')
    return
  }

  // 计算新状态（切换状态）
  const newStatus = row.status === 1 ? -1 : 1

  // 设置加载状态和防重复调用标志
  row.statusLoading = true
  isStatusChanging.value = true

  try {
    const result = await api.service.bannerStatus({
      id: row.id,
      status: newStatus
    })

    if (result.code === '200') {
      // 更新前端状态
      row.status = newStatus
      ElMessage.success('状态更新成功')
      console.log(`✅ 轮播图 ${row.id} 状态已更新为: ${newStatus}`)
    } else {
      ElMessage.error(result.meg || result.msg || '状态更新失败')
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  } finally {
    // 清除加载状态和防重复调用标志
    row.statusLoading = false
    isStatusChanging.value = false
  }
}
```

#### 2.4 防重复调用机制

```javascript
// 防止频繁调用的标志
const isApiCalling = ref(false)
const isStatusChanging = ref(false)
const isSubmitting = ref(false)
// 页面初始化完成标志，防止初始化时触发状态切换
const isPageInitialized = ref(false)
```

### 3. 状态参数说明

根据API文档，状态参数的标准格式：

| 参数名 | 数值 | 类型 | 说明 |
|--------|------|------|------|
| status | 1 | Number | 可用/启用状态 |
| status | -1 | Number | 不可用/禁用状态 |


**注意**：不同模块的状态值可能略有差异：
- **轮播图（ServiceBanner.vue）**：使用 `1`（可用）和 `-1`（不可用），API返回的 `0` 需转换为 `1`
- **金刚区（ServiceJingang.vue）**：使用 `1`（启用）和 `-1`（禁用）
- **代理商（ServiceDaili.vue）**：使用 `1`（上架）和 `-1`（下架）

### 4. 完整实现模板

#### 4.1 HTML模板
```vue
<template>
  <div class="service-xxx">
    <!-- 表格中的状态列 -->
    <el-table-column label="状态" width="120">
      <template #default="scope">
        <el-button
          :type="scope.row.status === 1 ? 'success' : 'danger'"
          :loading="scope.row.statusLoading"
          size="small"
          @click="handleStatusToggle(scope.row)"
        >
          {{ scope.row.status === 1 ? '可用' : '不可用' }}
        </el-button>
      </template>
    </el-table-column>
  </div>
</template>
```

#### 4.2 JavaScript模板
```javascript
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { api } from '@/api-v2'

// 防止频繁调用的标志
const isStatusChanging = ref(false)
const tableData = ref([])

// 数据加载方法
const getTableDataList = async () => {
  try {
    const result = await api.service.list(params)

    if (result.code === '200') {
      const data = result.data
      // 统一状态值格式并添加loading属性
      tableData.value = (data.list || []).map(item => ({
        ...item,
        status: item.status === 0 ? 1 : item.status,
        statusLoading: false
      }))
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 状态切换方法
const handleStatusToggle = async (row) => {
  if (isStatusChanging.value) {
    console.log('⚠️ 状态切换API正在调用中，跳过重复请求')
    return
  }

  const newStatus = row.status === 1 ? -1 : 1

  row.statusLoading = true
  isStatusChanging.value = true

  try {
    const result = await api.service.updateStatus({
      id: row.id,
      status: newStatus
    })

    if (result.code === '200') {
      row.status = newStatus
      ElMessage.success('状态更新成功')
    } else {
      ElMessage.error(result.meg || '状态更新失败')
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  } finally {
    row.statusLoading = false
    isStatusChanging.value = false
  }
}

onMounted(() => {
  getTableDataList()
})
</script>
```

### 5. 关键优势

#### 5.1 安全性
- ✅ 避免页面初始化时的异常API调用
- ✅ 防止数据类型不匹配导致的意外触发
- ✅ 完善的防重复调用机制

#### 5.2 用户体验
- ✅ 状态显示更直观（按钮颜色和文字）
- ✅ 加载状态反馈（按钮loading效果）
- ✅ 操作结果及时反馈

#### 5.3 维护性
- ✅ 代码逻辑清晰，易于理解
- ✅ 错误处理完善
- ✅ 日志记录详细

### 6. 常见问题解决

#### 6.1 状态修改后刷新页面又恢复了
**原因**：前端状态更新了，但后端API调用失败或状态没有正确保存

**解决方案**：
1. 检查API接口是否正确调用
2. 确认API响应是否成功
3. 验证后端数据是否正确保存
4. 添加详细的错误日志

#### 6.2 页面初始加载时异常调用状态API
**原因**：`el-switch` 组件在数据初始化时触发 `@change` 事件

**解决方案**：
1. 使用按钮替代开关组件
2. 添加页面初始化完成标志
3. 统一数据格式，避免类型不匹配

#### 6.3 频繁点击导致多次API调用
**原因**：没有防重复调用机制

**解决方案**：
1. 添加 `isStatusChanging` 标志
2. 使用按钮的 `loading` 状态
3. 在API调用期间禁用操作

### 7. 开发检查清单

- [ ] 使用按钮替代 `el-switch` 组件
- [ ] 添加防重复调用机制
- [ ] 统一状态值数据格式
- [ ] 添加加载状态反馈
- [ ] 完善错误处理逻辑
- [ ] 添加操作日志记录
- [ ] 测试状态切换功能
- [ ] 验证状态持久化效果

通过遵循这些最佳实践，可以避免状态切换相关的常见问题，确保功能稳定可靠。
