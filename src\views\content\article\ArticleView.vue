<!--
  文章管理模块
   内容管理功能的Vue3重构版本
  
  功能：
  - 文章列表展示
  - 文章搜索和筛选
  - 文章新增、编辑、删除
  - 文章状态管理
  - 分类和标签管理
-->

<template>
  <div class="article-management">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>文章管理</h1>
      <p class="page-subtitle">管理网站文章内容和发布状态</p>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :model="searchForm" :inline="true" size="default">
        <el-form-item label="标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入文章标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="作者">
          <el-input
            v-model="searchForm.author"
            placeholder="请输入作者"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="searchForm.categoryId"
            placeholder="请选择分类"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="category in categoryOptions"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
            <el-option label="已下线" value="offline" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleAdd">
          <el-icon><EditPen /></el-icon>
          写文章
        </el-button>
        <el-button 
          type="success" 
          :disabled="!selectedArticles.length"
          @click="handleBatchPublish"
        >
          <el-icon><Upload /></el-icon>
          批量发布
        </el-button>
        <el-button 
          type="warning" 
          :disabled="!selectedArticles.length"
          @click="handleBatchOffline"
        >
          <el-icon><Download /></el-icon>
          批量下线
        </el-button>
        <el-button 
          type="danger" 
          :disabled="!selectedArticles.length"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-tooltip content="刷新数据">
          <el-button circle @click="loadArticleList">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="列设置">
          <el-button circle @click="showColumnSettings = true">
            <el-icon><Setting /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 文章列表表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="articleList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          sortable="custom"
        />
        <el-table-column
          prop="cover"
          label="封面"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-image
              v-if="row.cover"
              :src="row.cover"
              :preview-src-list="[row.cover]"
              style="width: 60px; height: 40px"
              fit="cover"
              preview-teleported
            />
            <span v-else class="no-cover">无封面</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="标题"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-link @click="handlePreview(row)" type="primary">
              {{ row.title }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="author"
          label="作者"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="categoryName"
          label="分类"
          width="120"
        >
          <template #default="{ row }">
            <el-tag size="small">{{ row.categoryName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="tags"
          label="标签"
          width="150"
        >
          <template #default="{ row }">
            <el-tag
              v-for="tag in row.tags"
              :key="tag"
              size="small"
              type="info"
              style="margin-right: 4px"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="状态"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="viewCount"
          label="阅读量"
          width="100"
          sortable="custom"
          align="center"
        />
        <el-table-column
          prop="publishTime"
          label="发布时间"
          width="160"
          sortable="custom"
        />
        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="160"
          sortable="custom"
        />
        <el-table-column
          label="操作"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.status === 'draft'"
              type="success"
              size="small"
              @click="handlePublish(row)"
            >
              发布
            </el-button>
            <el-button
              v-else-if="row.status === 'published'"
              type="warning"
              size="small"
              @click="handleOffline(row)"
            >
              下线
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          共 {{ total }} 条记录，第 {{ currentPage }} / {{ Math.ceil(total / pageSize) }} 页
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 文章预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="文章预览"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="previewArticle" class="article-preview">
        <h2>{{ previewArticle.title }}</h2>
        <div class="article-meta">
          <span>作者：{{ previewArticle.author }}</span>
          <span>分类：{{ previewArticle.categoryName }}</span>
          <span>发布时间：{{ previewArticle.publishTime }}</span>
        </div>
        <div class="article-content" v-html="previewArticle.content"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  EditPen,
  Upload,
  Download,
  Delete,
  Setting
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const previewVisible = ref(false)
const showColumnSettings = ref(false)
const previewArticle = ref(null)

// 搜索表单
const searchForm = reactive({
  title: '',
  author: '',
  categoryId: '',
  status: ''
})

// 表格数据
const articleList = ref([])
const selectedArticles = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 分类选项
const categoryOptions = ref([
  { id: 1, name: '技术分享' },
  { id: 2, name: '产品动态' },
  { id: 3, name: '行业资讯' },
  { id: 4, name: '公司新闻' }
])

// Mock数据
const mockArticles = [
  {
    id: 1,
    title: 'Vue3 + Vite 项目搭建最佳实践',
    author: '张三',
    categoryId: 1,
    categoryName: '技术分享',
    tags: ['Vue3', 'Vite', '前端'],
    status: 'published',
    viewCount: 1234,
    cover: 'https://picsum.photos/300/200?random=1',
    content: '<p>这是一篇关于Vue3项目搭建的文章...</p>',
    publishTime: '2025-01-10 10:00:00',
    updateTime: '2025-01-11 15:30:00'
  },
  {
    id: 2,
    title: 'Element Plus 组件库使用指南',
    author: '李四',
    categoryId: 1,
    categoryName: '技术分享',
    tags: ['Element Plus', 'Vue3', 'UI'],
    status: 'draft',
    viewCount: 0,
    cover: 'https://picsum.photos/300/200?random=2',
    content: '<p>Element Plus是基于Vue3的组件库...</p>',
    publishTime: null,
    updateTime: '2025-01-11 14:20:00'
  },
  {
    id: 3,
    title: '2025年前端技术趋势展望',
    author: '王五',
    categoryId: 3,
    categoryName: '行业资讯',
    tags: ['前端', '趋势', '2025'],
    status: 'published',
    viewCount: 856,
    cover: 'https://picsum.photos/300/200?random=3',
    content: '<p>2025年前端技术将会有哪些新的发展...</p>',
    publishTime: '2025-01-09 16:45:00',
    updateTime: '2025-01-09 16:45:00'
  },
  {
    id: 4,
    title: '公司年度技术大会圆满举办',
    author: '赵六',
    categoryId: 4,
    categoryName: '公司新闻',
    tags: ['技术大会', '公司'],
    status: 'offline',
    viewCount: 432,
    cover: null,
    content: '<p>我们公司的年度技术大会成功举办...</p>',
    publishTime: '2025-01-08 09:00:00',
    updateTime: '2025-01-11 11:00:00'
  }
]

// 方法
const loadArticleList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 应用搜索过滤
    let filteredArticles = [...mockArticles]

    if (searchForm.title) {
      filteredArticles = filteredArticles.filter(article =>
        article.title.includes(searchForm.title)
      )
    }

    if (searchForm.author) {
      filteredArticles = filteredArticles.filter(article =>
        article.author.includes(searchForm.author)
      )
    }

    if (searchForm.categoryId) {
      filteredArticles = filteredArticles.filter(article =>
        article.categoryId === searchForm.categoryId
      )
    }

    if (searchForm.status) {
      filteredArticles = filteredArticles.filter(article =>
        article.status === searchForm.status
      )
    }

    total.value = filteredArticles.length

    // 分页处理
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    articleList.value = filteredArticles.slice(start, end)

  } catch (error) {
    ElMessage.error('加载文章列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadArticleList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    author: '',
    categoryId: '',
    status: ''
  })
  handleSearch()
}

const handleAdd = () => {
  ElMessage.info('跳转到文章编辑器...')
  // 这里应该跳转到文章编辑页面
}

const handleEdit = (row) => {
  ElMessage.info(`编辑文章: ${row.title}`)
  // 这里应该跳转到文章编辑页面
}

const handlePreview = (row) => {
  previewArticle.value = row
  previewVisible.value = true
}

const handlePublish = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要发布文章 "${row.title}" 吗？`,
      '发布确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟发布API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    row.status = 'published'
    row.publishTime = new Date().toLocaleString()

    ElMessage.success('文章发布成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('文章发布失败')
    }
  }
}

const handleOffline = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要下线文章 "${row.title}" 吗？`,
      '下线确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟下线API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    row.status = 'offline'

    ElMessage.success('文章下线成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('文章下线失败')
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文章 "${row.title}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟删除API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    ElMessage.success('删除成功')
    loadArticleList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchPublish = async () => {
  if (!selectedArticles.value.length) {
    ElMessage.warning('请选择要发布的文章')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要发布选中的 ${selectedArticles.value.length} 篇文章吗？`,
      '批量发布确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟批量发布API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('批量发布成功')
    selectedArticles.value = []
    loadArticleList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量发布失败')
    }
  }
}

const handleBatchOffline = async () => {
  if (!selectedArticles.value.length) {
    ElMessage.warning('请选择要下线的文章')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要下线选中的 ${selectedArticles.value.length} 篇文章吗？`,
      '批量下线确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟批量下线API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('批量下线成功')
    selectedArticles.value = []
    loadArticleList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量下线失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (!selectedArticles.value.length) {
    ElMessage.warning('请选择要删除的文章')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedArticles.value.length} 篇文章吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟批量删除API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('批量删除成功')
    selectedArticles.value = []
    loadArticleList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedArticles.value = selection
}

const handleSortChange = ({ prop, order }) => {
  // 实现排序逻辑
  console.log('排序:', prop, order)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadArticleList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadArticleList()
}

const getStatusType = (status) => {
  const statusMap = {
    draft: 'info',
    published: 'success',
    offline: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const statusMap = {
    draft: '草稿',
    published: '已发布',
    offline: '已下线'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  loadArticleList()
})
</script>

<style scoped>
.article-management {
  padding: var(--spacing-lg);
}

.page-title {
  margin-bottom: var(--spacing-xl);
}

.page-title h1 {
  font-size: var(--font-size-xxl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.search-form {
  background: var(--bg-color-base);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  margin-bottom: var(--spacing-lg);
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-sm);
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
}

.table-container {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
}

.pagination-info {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.no-cover {
  color: var(--color-text-placeholder);
  font-size: var(--font-size-xs);
}

.article-preview {
  max-height: 600px;
  overflow-y: auto;
}

.article-preview h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color-light);
}

.article-meta {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--bg-color-column);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.article-content {
  line-height: 1.8;
  color: var(--color-text-primary);
}

.article-content :deep(p) {
  margin-bottom: var(--spacing-md);
}

.article-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-sm);
}

/* Element Plus 表格样式覆盖 */
:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background-color: var(--table-header-bg);
  color: var(--color-text-primary);
  font-weight: 500;
}

:deep(.el-table tr:hover > td) {
  background-color: var(--table-row-hover-bg);
}

:deep(.el-table .el-table__cell) {
  border-bottom: 1px solid var(--table-border-color);
}

/* 图片预览样式 */
:deep(.el-image) {
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

/* 对话框样式 */
:deep(.el-dialog__header) {
  background-color: var(--bg-color-column);
  border-bottom: 1px solid var(--border-color-light);
}

:deep(.el-dialog__body) {
  padding: var(--spacing-xl);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .article-management {
    padding: var(--spacing-md);
  }

  .search-form :deep(.el-form--inline .el-form-item) {
    display: block;
    margin-bottom: var(--spacing-md);
  }

  .toolbar {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .pagination-container {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  :deep(.el-table .el-table__cell) {
    padding: var(--spacing-sm);
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: var(--spacing-md);
  }

  .article-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .page-title h1 {
    font-size: var(--font-size-xl);
  }

  .toolbar-left,
  .toolbar-right {
    flex-direction: column;
  }

  :deep(.el-table .el-table__cell:nth-child(n+7)) {
    display: none;
  }
}
</style>
