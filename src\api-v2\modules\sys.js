/**
 * 系统设置模块 - V2版本
 * 按照API封装规范文档实现系统设置相关接口
 */

import { get, post, del } from '../index'

export default {
  // ==================== 版本管理 ====================
  
  /**
   * 获取系统版本信息
   * @returns {Promise} 返回版本信息
   */
  versionInfo() {
    console.log('📱 获取系统版本信息API-V2请求')
    return get('/api/admin/sys/version/info')
  },

  /**
   * 系统升级
   * @param {Object} querys 升级参数
   * @param {string} querys.version 目标版本号
   * @param {string} querys.upgradeUrl 升级包URL
   * @returns {Promise} 返回升级结果
   */
  systemUpgrade(querys) {
    console.log('⬆️ 系统升级API-V2请求:', querys)
    return post('/api/admin/sys/upgrade', querys)
  },

  /**
   * 上传微信审核
   * @param {Object} querys 审核参数
   * @param {string} querys.version 版本号
   * @param {string} querys.description 版本描述
   * @returns {Promise} 返回上传结果
   */
  wechatExamine(querys) {
    console.log('📤 上传微信审核API-V2请求:', querys)
    return post('/api/admin/sys/wechat/examine', querys)
  },

  /**
   * 获取版本管理列表
   * @param {Object} querys 查询参数
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回版本列表
   */
  versionList(querys) {
    console.log('📋 版本管理列表API-V2请求参数:', querys)
    return get('/api/admin/sys/version/list', querys)
  },

  // ==================== 小程序设置 ====================
  
  /**
   * 获取小程序配置
   * @returns {Promise} 返回小程序配置
   */
  wechatConfig() {
    console.log('📱 获取小程序配置API-V2请求')
    return get('/api/admin/sys/wechat/config')
  },

  /**
   * 更新小程序配置
   * @param {Object} querys 配置数据
   * @param {string} querys.appId 小程序AppID
   * @param {string} querys.appSecret 小程序AppSecret
   * @param {string} querys.mchId 商户号
   * @param {string} querys.mchKey 商户密钥
   * @returns {Promise} 返回更新结果
   */
  wechatConfigUpdate(querys) {
    console.log('✏️ 更新小程序配置API-V2请求:', querys)
    return post('/api/admin/sys/wechat/config/update', querys)
  },

  // ==================== 公众号设置 ====================
  
  /**
   * 获取公众号配置
   * @returns {Promise} 返回公众号配置
   */
  webConfig() {
    console.log('🌐 获取公众号配置API-V2请求')
    return get('/api/admin/sys/web/config')
  },

  /**
   * 更新公众号配置
   * @param {Object} querys 配置数据
   * @param {string} querys.appId 公众号AppID
   * @param {string} querys.appSecret 公众号AppSecret
   * @param {string} querys.token 令牌
   * @param {string} querys.encodingAESKey 消息加解密密钥
   * @returns {Promise} 返回更新结果
   */
  webConfigUpdate(querys) {
    console.log('✏️ 更新公众号配置API-V2请求:', querys)
    return post('/api/admin/sys/web/config/update', querys)
  },

  // ==================== APP设置 ====================
  
  /**
   * 获取APP配置
   * @returns {Promise} 返回APP配置
   */
  appConfig() {
    console.log('📱 获取APP配置API-V2请求')
    return get('/api/admin/sys/app/config')
  },

  /**
   * 更新APP配置
   * @param {Object} querys 配置数据
   * @param {string} querys.appName APP名称
   * @param {string} querys.appVersion APP版本
   * @param {string} querys.downloadUrl 下载地址
   * @param {string} querys.updateContent 更新内容
   * @param {number} querys.forceUpdate 是否强制更新，1是，0否
   * @returns {Promise} 返回更新结果
   */
  appConfigUpdate(querys) {
    console.log('✏️ 更新APP配置API-V2请求:', querys)
    return post('/api/admin/sys/app/config/update', querys)
  },

  // ==================== 隐私协议 ====================
  
  /**
   * 获取隐私协议
   * @returns {Promise} 返回隐私协议内容
   */
  privacyInfo() {
    console.log('📄 获取隐私协议API-V2请求')
    return get('/api/admin/sys/privacy/info')
  },

  /**
   * 更新隐私协议
   * @param {Object} querys 协议数据
   * @param {string} querys.title 协议标题
   * @param {string} querys.content 协议内容
   * @returns {Promise} 返回更新结果
   */
  privacyUpdate(querys) {
    console.log('✏️ 更新隐私协议API-V2请求:', querys)
    return post('/api/admin/sys/privacy/update', querys)
  },

  // ==================== 隐私协议配置 ====================

  /**
   * 获取隐私协议配置
   * @returns {Promise} 返回隐私协议配置数据
   */
  getPrivacyAgreement() {
    console.log('📄 获取隐私协议配置API-V2请求')
    return get('/api/admin/config/getPrivacyAgreement')
  },

  /**
   * 更新隐私协议配置
   * @param {Object} querys 协议配置数据
   * @param {string} querys.loginProtocol 用户隐私协议
   * @param {string} querys.informationProtection 个人信息保护指引
   * @param {string} querys.content 师傅端隐私协议
   * @param {string} querys.shifuServiceAgreement 师傅端服务协议
   * @param {string} querys.shifuSecurityProtocol 师傅端安全协议
   * @param {string} querys.shifuQualityCommitment 服务质量保障承诺
   * @param {string} querys.userNotice 用户须知
   * @param {string} querys.masterNotice 师傅须知
   * @returns {Promise} 返回更新结果
   */
  updatePrivacyAgreement(querys) {
    console.log('✏️ 更新隐私协议配置API-V2请求:', querys)
    return post('/api/admin/config/updatePrivacyAgreement', querys)
  },

  // ==================== 交易设置配置 ====================

  /**
   * 获取交易设置配置
   * @returns {Promise} 返回交易设置配置数据
   */
  getTradeSettings() {
    console.log('💰 获取交易设置配置API-V2请求')
    return get('/api/admin/config/getTradeSettings')
  },

  /**
   * 更新交易设置配置
   * @param {Object} querys 交易设置配置数据
   * @param {number} querys.companyPay 转账方式 1企业转账 2商家转账
   * @param {number} querys.overTime 订单超时时间（分钟）
   * @param {number} querys.maxDay 最长预约时间（天）
   * @param {number} querys.timeUnit 时长单位（分钟）
   * @param {string} querys.tradingRules 交易规则（富文本）
   * @returns {Promise} 返回更新结果
   */
  updateTradeSettings(querys) {
    console.log('✏️ 更新交易设置配置API-V2请求:', querys)
    return post('/api/admin/config/updateTradeSettings', querys)
  },

  // ==================== 支付配置 ====================
  
  /**
   * 获取支付配置
   * @returns {Promise} 返回支付配置
   */
  paymentConfig() {
    console.log('💳 获取支付配置API-V2请求')
    return get('/api/admin/sys/payment/config')
  },

  /**
   * 更新支付配置
   * @param {Object} querys 支付配置数据
   * @param {Object} querys.wechat 微信支付配置
   * @param {Object} querys.alipay 支付宝配置
   * @param {Object} querys.unionpay 银联支付配置
   * @returns {Promise} 返回更新结果
   */
  paymentConfigUpdate(querys) {
    console.log('✏️ 更新支付配置API-V2请求:', querys)
    return post('/api/admin/sys/payment/config/update', querys)
  },

  // ==================== 上传配置 ====================
  
  /**
   * 获取上传配置
   * @returns {Promise} 返回上传配置
   */
  uploadConfig() {
    console.log('📤 获取上传配置API-V2请求')
    return get('/api/admin/sys/upload/config')
  },

  /**
   * 更新上传配置
   * @param {Object} querys 上传配置数据
   * @param {string} querys.type 上传类型，local/qiniu/aliyun/tencent
   * @param {Object} querys.config 具体配置参数
   * @returns {Promise} 返回更新结果
   */
  uploadConfigUpdate(querys) {
    console.log('✏️ 更新上传配置API-V2请求:', querys)
    return post('/api/admin/sys/upload/config/update', querys)
  },

  // ==================== 交易设置 ====================
  
  /**
   * 获取交易设置
   * @returns {Promise} 返回交易设置
   */
  transactionConfig() {
    console.log('💰 获取交易设置API-V2请求')
    return get('/api/admin/sys/transaction/config')
  },

  /**
   * 更新交易设置
   * @param {Object} querys 交易设置数据
   * @param {number} querys.orderTimeout 订单超时时间（分钟）
   * @param {number} querys.autoConfirm 自动确认时间（小时）
   * @param {number} querys.refundTimeout 退款超时时间（天）
   * @returns {Promise} 返回更新结果
   */
  transactionConfigUpdate(querys) {
    console.log('✏️ 更新交易设置API-V2请求:', querys)
    return post('/api/admin/sys/transaction/config/update', querys)
  },

  // ==================== 通知设置 ====================
  
  /**
   * 获取万能通知配置
   * @returns {Promise} 返回通知配置
   */
  noticeConfig() {
    console.log('📢 获取万能通知配置API-V2请求')
    return get('/api/admin/sys/notice/config')
  },

  /**
   * 更新万能通知配置
   * @param {Object} querys 通知配置数据
   * @param {Object} querys.wechat 微信通知配置
   * @param {Object} querys.sms 短信通知配置
   * @param {Object} querys.email 邮件通知配置
   * @returns {Promise} 返回更新结果
   */
  noticeConfigUpdate(querys) {
    console.log('✏️ 更新万能通知配置API-V2请求:', querys)
    return post('/api/admin/sys/notice/config/update', querys)
  },

  /**
   * 获取短信通知配置
   * @returns {Promise} 返回短信配置
   */
  messageConfig() {
    console.log('📱 获取短信通知配置API-V2请求')
    return get('/api/admin/sys/message/config')
  },

  /**
   * 更新短信通知配置
   * @param {Object} querys 短信配置数据
   * @param {string} querys.provider 短信服务商
   * @param {Object} querys.config 服务商配置
   * @returns {Promise} 返回更新结果
   */
  messageConfigUpdate(querys) {
    console.log('✏️ 更新短信通知配置API-V2请求:', querys)
    return post('/api/admin/sys/message/config/update', querys)
  },

  // ==================== 备案信息 ====================
  
  /**
   * 获取备案信息
   * @returns {Promise} 返回备案信息
   */
  informationConfig() {
    console.log('📋 获取备案信息API-V2请求')
    return get('/api/admin/sys/information/config')
  },

  /**
   * 更新备案信息
   * @param {Object} querys 备案信息数据
   * @param {string} querys.companyName 公司名称
   * @param {string} querys.icpNumber ICP备案号
   * @param {string} querys.address 公司地址
   * @param {string} querys.phone 联系电话
   * @returns {Promise} 返回更新结果
   */
  informationConfigUpdate(querys) {
    console.log('✏️ 更新备案信息API-V2请求:', querys)
    return post('/api/admin/sys/information/config/update', querys)
  },

  // ==================== 其他设置 ====================

  /**
   * 获取打印机设置
   * @returns {Promise} 返回打印机设置
   */
  printConfig() {
    console.log('🖨️ 获取打印机设置API-V2请求')
    return get('/api/admin/sys/print/config')
  },

  /**
   * 更新打印机设置
   * @param {Object} querys 打印机设置数据
   * @param {string} querys.brand 打印机品牌
   * @param {Object} querys.config 打印机配置
   * @param {number} querys.status 状态，1启用，0禁用
   * @returns {Promise} 返回更新结果
   */
  printConfigUpdate(querys) {
    console.log('✏️ 更新打印机设置API-V2请求:', querys)
    return post('/api/admin/sys/print/config/update', querys)
  },

  /**
   * 获取车费设置
   * @returns {Promise} 返回车费设置
   */
  carFeeConfig() {
    console.log('🚗 获取车费设置API-V2请求')
    return get('/api/admin/sys/car_fee/config')
  },

  /**
   * 更新车费设置
   * @param {Object} querys 车费设置数据
   * @param {number} querys.baseFee 起步费
   * @param {number} querys.perKmFee 每公里费用
   * @param {number} querys.freeDistance 免费距离
   * @returns {Promise} 返回更新结果
   */
  carFeeConfigUpdate(querys) {
    console.log('✏️ 更新车费设置API-V2请求:', querys)
    return post('/api/admin/sys/car_fee/config/update', querys)
  },

  /**
   * 获取城市设置列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，1启用，0禁用
   * @param {string} querys.name 城市名称，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回城市列表
   */
  cityList(querys) {
    console.log('🏙️ 城市设置列表API-V2请求参数:', querys)
    return get('/api/admin/sys/city/list', querys)
  },

  /**
   * 新增城市
   * @param {Object} querys 城市数据
   * @param {string} querys.name 城市名称
   * @param {string} querys.code 城市编码
   * @param {number} querys.sort 排序
   * @param {number} querys.status 状态，1启用，0禁用
   * @returns {Promise} 返回新增结果
   */
  cityAdd(querys) {
    if (!querys || !querys.name) {
      return Promise.reject(new Error('城市名称不能为空'))
    }

    console.log('➕ 新增城市API-V2请求数据:', querys)
    return post('/api/admin/sys/city/add', querys)
  },

  /**
   * 编辑城市
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  cityUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('城市ID不能为空'))
    }

    console.log('✏️ 编辑城市API-V2请求:', querys)
    return post('/api/admin/sys/city/update', querys)
  },

  /**
   * 删除城市
   * @param {Object} querys 删除参数
   * @param {number} querys.id 城市ID
   * @returns {Promise} 返回删除结果
   */
  cityDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('城市ID不能为空'))
    }

    console.log('🗑️ 删除城市API-V2请求:', querys)
    return post(`/api/admin/sys/city/delete/${querys.id}`)
  },

  /**
   * 获取出行设置
   * @returns {Promise} 返回出行设置
   */
  travelConfig() {
    console.log('🚌 获取出行设置API-V2请求')
    return get('/api/admin/sys/travel/config')
  },

  /**
   * 更新出行设置
   * @param {Object} querys 出行设置数据
   * @param {number} querys.maxDistance 最大服务距离
   * @param {number} querys.orderTimeout 订单超时时间
   * @param {Array} querys.serviceTime 服务时间段
   * @returns {Promise} 返回更新结果
   */
  travelConfigUpdate(querys) {
    console.log('✏️ 更新出行设置API-V2请求:', querys)
    return post('/api/admin/sys/travel/config/update', querys)
  },

  /**
   * 获取其他设置
   * @returns {Promise} 返回其他设置
   */
  otherConfig() {
    console.log('⚙️ 获取其他设置API-V2请求')
    return get('/api/admin/sys/other/config')
  },

  /**
   * 更新其他设置
   * @param {Object} querys 其他设置数据
   * @param {string} querys.siteName 网站名称
   * @param {string} querys.siteKeywords 网站关键词
   * @param {string} querys.siteDescription 网站描述
   * @param {string} querys.customerService 客服电话
   * @returns {Promise} 返回更新结果
   */
  otherConfigUpdate(querys) {
    console.log('✏️ 更新其他设置API-V2请求:', querys)
    return post('/api/admin/sys/other/config/update', querys)
  },

  // ==================== APP版本管理 ====================

  /**
   * 获取App wgt版本信息列表
   * @param {Object} params 查询参数
   * @param {string} params.appid 应用ID（可选）
   * @param {string} params.version 版本号（可选）
   * @param {number} params.platform 平台类型：1-师傅端，2-用户端（可选）
   * @param {number} params.forceUpdate 是否强制更新（1是0否）（可选）
   * @param {number} params.pageNum 当前页（可选）
   * @param {number} params.pageSize 每页数（可选）
   * @returns {Promise} 返回版本列表数据
   */
  getAppWgtList(params = {}) {
    console.log('📱 获取App wgt版本信息列表API-V2请求:', params)
    return get('/api/admin/config/appWgt/list', params)
  },

  /**
   * 获取App wgt版本信息详情
   * @param {number|string} id 版本ID
   * @returns {Promise} 返回版本详情数据
   */
  getAppWgtDetail(id) {
    console.log('📱 获取App wgt版本信息详情API-V2请求:', id)
    return get(`/api/admin/config/appWgt/detail/${id}`)
  },

  /**
   * 上传App wgt版本包
   * @param {FormData} formData 包含文件的FormData对象
   * @returns {Promise} 返回上传结果
   */
  uploadAppWgt(formData) {
    console.log('📤 上传App wgt版本包API-V2请求')
    return post('/api/admin/config/appWgt/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 新增App wgt版本信息
   * @param {Object} data 版本信息数据
   * @param {string} data.appid 应用ID（AppID）
   * @param {string} data.version 资源包版本号（如1.0.2）
   * @param {string} data.wgtUrl wgt升级包下载地址
   * @param {string} data.description 升级说明
   * @param {number} data.forceUpdate 是否强制更新（1是0否）
   * @param {number} data.platform 平台类型：1-师傅端，2-用户端
   * @returns {Promise} 返回新增结果
   */
  addAppWgt(data) {
    console.log('➕ 新增App wgt版本信息API-V2请求:', data)
    return post('/api/admin/config/appWgt/add', data)
  },

  /**
   * 更新App wgt版本信息
   * @param {Object} data 版本信息数据
   * @param {number|string} data.id 版本ID
   * @param {string} data.appid 应用ID（AppID）
   * @param {string} data.version 资源包版本号（如1.0.2）
   * @param {string} data.wgtUrl wgt升级包下载地址
   * @param {string} data.description 升级说明
   * @param {number} data.forceUpdate 是否强制更新（1是0否）
   * @param {number} data.platform 平台类型：1-师傅端，2-用户端
   * @returns {Promise} 返回更新结果
   */
  updateAppWgt(data) {
    console.log('✏️ 更新App wgt版本信息API-V2请求:', data)
    return post('/api/admin/config/appWgt/update', data)
  },

  /**
   * 删除App wgt版本信息
   * @param {number|string} id 版本ID
   * @returns {Promise} 返回删除结果
   */
  deleteAppWgt(id) {
    console.log('🗑️ 删除App wgt版本信息API-V2请求:', id)
    return del(`/api/admin/config/appWgt/delete/${id}`)
  }
}
