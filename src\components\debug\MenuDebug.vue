<!--
  菜单调试组件
  用于显示当前动态菜单的状态和数据
-->

<template>
  <div class="menu-debug" v-if="showDebug">
    <div class="debug-header">
      <h4>🎛️ 动态菜单调试信息</h4>
      <button @click="toggleDebug" class="close-btn">×</button>
    </div>
    
    <div class="debug-content">
      <div class="debug-section">
        <h5>📊 菜单状态</h5>
        <div class="status-item">
          <span class="label">菜单已加载:</span>
          <span :class="menuLoaded ? 'status-success' : 'status-error'">
            {{ menuLoaded ? '✅ 是' : '❌ 否' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">主菜单数量:</span>
          <span class="value">{{ mainMenuList.length }}</span>
        </div>
        <div class="status-item">
          <span class="label">子菜单映射:</span>
          <span class="value">{{ Object.keys(submenuMap).length }}</span>
        </div>
      </div>

      <div class="debug-section">
        <h5>🎯 主菜单列表</h5>
        <div class="menu-list">
          <div 
            v-for="menu in mainMenuList" 
            :key="menu.id"
            class="menu-item"
          >
            <div class="menu-info">
              <div class="menu-name">{{ menu.name }}</div>
              <div class="menu-path">{{ menu.path }}</div>
              <div class="menu-meta">图标: {{ menu.icon }} | 排序: {{ menu.sort }}</div>
            </div>
          </div>
          <div v-if="mainMenuList.length === 0" class="empty-state">
            暂无菜单数据
          </div>
        </div>
      </div>

      <div class="debug-section">
        <h5>🔗 子菜单映射</h5>
        <div class="route-mapping">
          <div
            v-for="(submenu, route) in submenuMap"
            :key="route"
            class="route-item"
          >
            <div class="route-header">
              <div class="route-from">{{ route }}</div>
              <div class="submenu-count">{{ getSubmenuCount(submenu) }}个子菜单</div>
            </div>
            <div class="submenu-details">
              <div
                v-for="(group, groupIndex) in submenu"
                :key="groupIndex"
                class="submenu-group"
              >
                <div class="group-name">{{ group.name }}</div>
                <div class="submenu-urls">
                  <span
                    v-for="(url, urlIndex) in group.url"
                    :key="urlIndex"
                    class="submenu-url"
                  >
                    {{ url.name }} ({{ url.url }})
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="Object.keys(submenuMap).length === 0" class="empty-state">
            暂无子菜单映射
          </div>
        </div>
      </div>

      <div class="debug-actions">
        <button @click="refreshMenus" class="refresh-btn">🔄 刷新菜单</button>
        <button @click="exportMenuData" class="export-btn">📤 导出数据</button>
      </div>
    </div>
  </div>

  <!-- 调试按钮 -->
  <div class="debug-toggle" @click="toggleDebug" v-if="!showDebug">
    🎛️
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const showDebug = ref(false)

// 计算属性
const menuLoaded = computed(() => store.getters['menu/menuLoaded'])
const mainMenuList = computed(() => store.getters['menu/mainMenuList'])
const submenuMap = computed(() => store.getters['menu/submenuMap'])

// 方法
const toggleDebug = () => {
  showDebug.value = !showDebug.value
}

const getSubmenuCount = (submenu) => {
  return submenu.reduce((count, group) => count + group.url.length, 0)
}

const refreshMenus = async () => {
  try {
    // 不再调用 fetchUserMenus，改为使用降级菜单
    await store.dispatch('menu/useFallbackMenus')
    console.log('🔄 菜单数据刷新成功（使用降级菜单）')
  } catch (error) {
    console.error('❌ 菜单数据刷新失败:', error)
  }
}

const exportMenuData = () => {
  const data = {
    menuLoaded: menuLoaded.value,
    mainMenuList: mainMenuList.value,
    submenuMap: submenuMap.value,
    timestamp: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `menu-debug-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}
</script>

<style scoped>
.menu-debug {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  z-index: 9999;
  overflow: hidden;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.debug-header h4 {
  margin: 0;
  font-size: 16px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-content {
  padding: 20px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.debug-section {
  margin-bottom: 20px;
}

.debug-section h5 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 5px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  font-size: 13px;
}

.label {
  color: #6c757d;
}

.value {
  font-weight: bold;
  color: #495057;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

.menu-list, .route-mapping {
  max-height: 200px;
  overflow-y: auto;
}

.menu-item {
  padding: 10px;
  margin: 5px 0;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

.menu-name {
  font-weight: bold;
  color: #495057;
  font-size: 13px;
}

.menu-path {
  color: #6c757d;
  font-size: 12px;
  margin: 2px 0;
}

.menu-meta {
  color: #6c757d;
  font-size: 11px;
}

.route-item {
  padding: 12px;
  margin: 8px 0;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.route-from {
  color: #495057;
  font-weight: bold;
  font-size: 13px;
}

.submenu-count {
  background: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.submenu-details {
  margin-top: 8px;
}

.submenu-group {
  margin-bottom: 6px;
}

.group-name {
  font-weight: bold;
  color: #6c757d;
  font-size: 11px;
  margin-bottom: 4px;
}

.submenu-urls {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.submenu-url {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  border: 1px solid #bbdefb;
}

.empty-state {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
  font-size: 13px;
}

.debug-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #dee2e6;
}

.refresh-btn, .export-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
}

.refresh-btn {
  background: #28a745;
  color: white;
}

.refresh-btn:hover {
  background: #218838;
}

.export-btn {
  background: #007bff;
  color: white;
}

.export-btn:hover {
  background: #0056b3;
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
  z-index: 9998;
  font-size: 20px;
  transition: transform 0.2s;
}

.debug-toggle:hover {
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .menu-debug {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
}
</style>
