<!--
  支付配置页面
-->

<template>
  <div class="lb-system-payment">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>支付配置</span>
          </div>
        </template>
        
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 微信支付 -->
          <el-tab-pane label="微信支付" name="wechat">
            <el-form 
              :model="wechatForm" 
              :rules="wechatRules" 
              ref="wechatFormRef" 
              label-width="140px"
              class="config-form"
            >
              <el-form-item label="商户号" prop="mch_id">
                <el-input v-model="wechatForm.mch_id" placeholder="请输入微信支付商户号" />
              </el-form-item>
              
              <el-form-item label="商户密钥" prop="mch_key">
                <el-input v-model="wechatForm.mch_key" placeholder="请输入商户密钥" type="password" show-password />
              </el-form-item>
              
              <el-form-item label="证书路径" prop="cert_path">
                <el-input v-model="wechatForm.cert_path" placeholder="请输入证书文件路径" />
              </el-form-item>
              
              <el-form-item label="启用状态" prop="status">
                <el-radio-group v-model="wechatForm.status">
                  <el-radio :value="1">启用</el-radio>
                  <el-radio :value="0">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item>
                <LbButton type="primary" @click="saveWechatConfig" :loading="wechatLoading">保存配置</LbButton>
                <LbButton @click="testWechatConfig" style="margin-left: 10px;">测试连接</LbButton>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <!-- 支付宝 -->
          <el-tab-pane label="支付宝" name="alipay">
            <el-form 
              :model="alipayForm" 
              :rules="alipayRules" 
              ref="alipayFormRef" 
              label-width="140px"
              class="config-form"
            >
              <el-form-item label="应用ID" prop="app_id">
                <el-input v-model="alipayForm.app_id" placeholder="请输入支付宝应用ID" />
              </el-form-item>
              
              <el-form-item label="私钥" prop="private_key">
                <el-input v-model="alipayForm.private_key" type="textarea" :rows="4" placeholder="请输入应用私钥" />
              </el-form-item>
              
              <el-form-item label="公钥" prop="public_key">
                <el-input v-model="alipayForm.public_key" type="textarea" :rows="4" placeholder="请输入支付宝公钥" />
              </el-form-item>
              
              <el-form-item label="启用状态" prop="status">
                <el-radio-group v-model="alipayForm.status">
                  <el-radio :value="1">启用</el-radio>
                  <el-radio :value="0">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item>
                <LbButton type="primary" @click="saveAlipayConfig" :loading="alipayLoading">保存配置</LbButton>
                <LbButton @click="testAlipayConfig" style="margin-left: 10px;">测试连接</LbButton>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const activeTab = ref('wechat')
const wechatLoading = ref(false)
const alipayLoading = ref(false)
const wechatFormRef = ref()
const alipayFormRef = ref()

const wechatForm = reactive({
  mch_id: '',
  mch_key: '',
  cert_path: '',
  status: 1
})

const alipayForm = reactive({
  app_id: '',
  private_key: '',
  public_key: '',
  status: 1
})

const wechatRules = {
  mch_id: [{ required: true, message: '请输入商户号', trigger: 'blur' }],
  mch_key: [{ required: true, message: '请输入商户密钥', trigger: 'blur' }]
}

const alipayRules = {
  app_id: [{ required: true, message: '请输入应用ID', trigger: 'blur' }],
  private_key: [{ required: true, message: '请输入私钥', trigger: 'blur' }]
}

const getConfig = async () => {
  try {
    const response = await fetch('/api/system/payment/config')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(wechatForm, result.data.wechat || {})
      Object.assign(alipayForm, result.data.alipay || {})
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

const saveWechatConfig = async () => {
  try {
    await wechatFormRef.value.validate()
    wechatLoading.value = true
    
    const response = await fetch('/api/system/payment/wechat', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(wechatForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('微信支付配置保存成功')
    } else {
      ElMessage.error(result.meg || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    wechatLoading.value = false
  }
}

const saveAlipayConfig = async () => {
  try {
    await alipayFormRef.value.validate()
    alipayLoading.value = true
    
    const response = await fetch('/api/system/payment/alipay', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(alipayForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('支付宝配置保存成功')
    } else {
      ElMessage.error(result.meg || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    alipayLoading.value = false
  }
}

const testWechatConfig = async () => {
  try {
    const response = await fetch('/api/system/payment/wechat/test', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(wechatForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('微信支付连接测试成功')
    } else {
      ElMessage.error(result.meg || '连接测试失败')
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  }
}

const testAlipayConfig = async () => {
  try {
    const response = await fetch('/api/system/payment/alipay/test', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(alipayForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('支付宝连接测试成功')
    } else {
      ElMessage.error(result.meg || '连接测试失败')
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  }
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-payment {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
  padding: 20px;
}

@media (max-width: 768px) {
  .lb-system-payment {
    padding: 10px;
  }
  
  .config-form {
    max-width: 100%;
    padding: 10px;
  }
}
</style>
