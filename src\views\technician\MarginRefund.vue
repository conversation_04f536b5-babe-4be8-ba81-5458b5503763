<!--
  保证金退款管理页面
  根据API接口文档开发的保证金退款管理功能
  支持保证金退款列表查询、审核通过、审核驳回等功能
-->

<template>
  <div class="margin-refund">
    <!-- 顶部导航 -->
    <TopNav title="保证金退款管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" class="search-form">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="师傅名" prop="coachName">
                <el-input size="default" v-model="searchForm.coachName" placeholder="请输入师傅名" clearable
                  style="width: 200px" />
              </el-form-item>
              <el-form-item label="选择城市" prop="cityId">
                <el-cascader size="default" v-model="searchForm.cityId" :options="cityOptions" :props="cascaderProps"
                  placeholder="请选择城市" clearable style="width: 200px" @change="handleCityChange" />
              </el-form-item>
              <el-form-item>
                <LbButton size="default" type="primary" icon="Search" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton size="default" icon="RefreshLeft" @click="handleReset">
                  重置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table v-loading="loading" :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="marginCode" label="缴费成功三方单号" width="250" show-overflow-tooltip />
          <el-table-column prop="refundCode" label="退款订单号" width="250" show-overflow-tooltip />
          <el-table-column prop="coachName" label="师傅名称" width="120" />
          <el-table-column prop="price" label="实际支付金额" width="120" align="right">
            <template #default="scope">
              <span>￥{{ scope.row.price || '0.00' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="cashPledgeFreeze" label="冻结金额" width="120" align="right">
            <template #default="scope">
              <span>￥{{ scope.row.cashPledgeFreeze || '0.00' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="refundPrice" label="退款金额" width="120" align="right">
            <template #default="scope">
              <span>￥{{ scope.row.refundPrice || '0.00' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="default">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="wxTransactionId" label="微信退款单号" width="180" show-overflow-tooltip />
          <el-table-column prop="createTime" label="申请时间" width="160" />
          <el-table-column prop="arriveTime" label="退款到账时间" width="160" />
          <el-table-column label="操作" min-width="200" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <LbButton v-if="scope.row.status === 0" size="default" type="success" @click="handleApprove(scope.row)">
                  同意退款
                </LbButton>
                <LbButton v-if="scope.row.status === 0" size="default" type="danger" @click="handleReject(scope.row)">
                  拒绝退款
                </LbButton>
                <LbButton size="default" type="primary" @click="handleView(scope.row)">
                  查看详情
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage :page="searchForm.pageNum" :page-size="searchForm.pageSize" :total="total"
        @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditDialogVisible" :title="auditTitle" width="500px">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="师傅名称">
          <span>{{ currentRefund.coachName }}</span>
        </el-form-item>
        <el-form-item label="退款金额">
          <span>￥{{ currentRefund.refundPrice || '0.00' }}</span>
        </el-form-item>
        <el-form-item label="备注" required>
          <el-input v-model="auditForm.text" type="textarea" :rows="4"
            :placeholder="auditType === 'approve' ? '请输入同意退款的备注' : '请输入拒绝退款的原因'" maxlength="200" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <LbButton @click="auditDialogVisible = false">取消</LbButton>
        <LbButton :type="auditType === 'approve' ? 'success' : 'danger'" @click="confirmAudit" :loading="auditLoading">
          {{ auditType === 'approve' ? '确认同意' : '确认拒绝' }}
        </LbButton>
      </template>
    </el-dialog>

    <!-- 详情查看对话框 -->
    <el-dialog v-model="detailDialogVisible" title="保证金退款详情" width="800px">
      <div v-loading="detailLoading" class="refund-detail">
        <el-row :gutter="20" v-if="refundDetail">
          <el-col :span="12">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-item">
                <label>退款ID：</label>
                <span>{{ refundDetail.id }}</span>
              </div>
              <div class="detail-item">
                <label>师傅名称：</label>
                <span>{{ refundDetail.coachName }}</span>
              </div>
              <div class="detail-item">
                <label>缴费成功三方单号：</label>
                <span>{{ refundDetail.marginCode || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>退款订单号：</label>
                <span>{{ refundDetail.refundCode || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>微信退款单号：</label>
                <span>{{ refundDetail.wxTransactionId || '暂无' }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-section">
              <h4>金额信息</h4>
              <div class="detail-item">
                <label>实际支付金额：</label>
                <span>￥{{ refundDetail.price || '0.00' }}</span>
              </div>
              <div class="detail-item">
                <label>冻结金额：</label>
                <span>￥{{ refundDetail.cashPledgeFreeze || '0.00' }}</span>
              </div>
              <div class="detail-item">
                <label>退款金额：</label>
                <span>￥{{ refundDetail.refundPrice || '0.00' }}</span>
              </div>
              <div class="detail-item">
                <label>状态：</label>
                <el-tag :type="getStatusType(refundDetail.status)" size="default">
                  {{ getStatusText(refundDetail.status) }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>申请时间：</label>
                <span>{{ refundDetail.createTime || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>退款到账时间：</label>
                <span>{{ refundDetail.arriveTime || '暂无' }}</span>
              </div>
              <div class="detail-item" v-if="refundDetail.shText">
                <label>审核备注：</label>
                <span>{{ refundDetail.shText }}</span>
              </div>
              <div class="detail-item" v-if="refundDetail.failText">
                <label>退款失败原因：</label>
                <span>{{ refundDetail.failText }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <LbButton @click="detailDialogVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const searchFormRef = ref()
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 城市选择相关
const cityOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true, // 返回完整路径，便于显示省市区
  checkStrictly: false, // 只能选择叶子节点（区县级别）
  expandTrigger: 'hover' // 鼠标悬停展开
}

// 搜索表单
const searchForm = reactive({
  coachName: '',
  pageNum: 1,
  pageSize: 10,
  cityId: []
})

// 审核相关
const auditDialogVisible = ref(false)
const auditLoading = ref(false)
const auditType = ref('') // 'approve' 或 'reject'
const auditTitle = ref('')
const currentRefund = ref({})
const auditForm = reactive({
  id: null,
  text: ''
})

// 详情查看相关
const detailDialogVisible = ref(false)
const detailLoading = ref(false)
const refundDetail = ref(null)

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    0: 'warning',  // 待审核
    1: 'info',     // 退款中
    2: 'success',  // 退款已到账
    3: 'danger',   // 申请已驳回
    '-1': 'danger' // 退款失败
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '待审核',
    1: '退款中',
    2: '退款已到账',
    3: '申请已驳回',
    '-1': '退款失败'
  }
  return statusMap[status] || '未知状态'
}

// 搜索处理
const handleSearch = () => {
  searchForm.pageNum = 1
  getRefundList()
}

// 重置处理
const handleReset = () => {
  searchFormRef.value?.resetFields()
  searchForm.coachName = ''
  searchForm.cityId = []
  searchForm.pageNum = 1
  getRefundList()
}

// 城市选择处理
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)

  if (value && value.length > 0) {
    // 取最后一级的城市ID（区县级别）
    const selectedCityId = value[value.length - 1]
    console.log('🏙️ 选中的城市ID:', selectedCityId)
  } else {
    console.log('🏙️ 清空城市选择')
  }
}

/**
 * 获取城市数据（用于级联选择器）
 */
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await proxy.$api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getRefundList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getRefundList()
}

// 同意退款
const handleApprove = (row) => {
  auditType.value = 'approve'
  auditTitle.value = '同意保证金退款'
  currentRefund.value = row
  auditForm.id = row.id
  auditForm.text = ''
  auditDialogVisible.value = true
}

// 拒绝退款
const handleReject = (row) => {
  auditType.value = 'reject'
  auditTitle.value = '拒绝保证金退款'
  currentRefund.value = row
  auditForm.id = row.id
  auditForm.text = ''
  auditDialogVisible.value = true
}

// 确认审核
const confirmAudit = async () => {
  if (!auditForm.text.trim()) {
    ElMessage.warning('请输入备注信息')
    return
  }

  try {
    auditLoading.value = true

    const apiMethod = auditType.value === 'approve'
      ? proxy.$api.technician.passMarginRefund
      : proxy.$api.technician.noMarginRefund

    const result = await apiMethod({
      id: auditForm.id,
      text: auditForm.text
    })

    if (result.code === '200') {
      ElMessage.success(auditType.value === 'approve' ? '同意退款成功' : '拒绝退款成功')
      auditDialogVisible.value = false
      getRefundList() // 刷新列表
    } else {
      ElMessage.error(result.msg || '操作失败')
    }
  } catch (error) {
    console.error('审核操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    auditLoading.value = false
  }
}

// 查看详情
const handleView = (row) => {
  refundDetail.value = row
  detailDialogVisible.value = true
}

// API调用方法
const getRefundList = async () => {
  try {
    loading.value = true

    // 构建请求参数，处理城市ID
    const requestParams = { ...searchForm }
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      requestParams.cityId = searchForm.cityId.join(',')
      console.log('🏙️ 保证金退款查询包含城市参数:', requestParams.cityId)
    }

    const result = await proxy.$api.technician.marginRefundList(requestParams)

    if (result.code === '200') {
      tableData.value = result.data.list || []
      total.value = result.data.totalCount || 0
    } else {
      ElMessage.error(result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取保证金退款列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  getRefundList()
  getCityData() // 获取城市数据
})
</script>

<style scoped>
/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-buttons .el-button {
  font-size: 12px;
  padding: 6px 12px;
}

/* 详情对话框样式 */
.refund-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item label {
  min-width: 120px;
  font-weight: 500;
  color: #666;
  margin-right: 10px;
}

.detail-item span {
  flex: 1;
  color: #333;
  word-break: break-all;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button+.el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 12px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
    margin-left: 0 !important;
    margin-bottom: 4px;
  }
}
</style>
