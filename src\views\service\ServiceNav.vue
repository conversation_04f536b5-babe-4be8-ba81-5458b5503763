<template>
  <div class="service-nav">
    <!-- 顶部导航 -->
    <TopNav title="金刚区管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="标题查询" prop="title">
                <el-input
                  size="default"
                  v-model="searchForm.title"
                  placeholder="请输入金刚区标题"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="可用" :value="1" />
                  <el-option label="不可用" :value="-1" />
                </el-select>
              </el-form-item>
              <el-form-item label="类型" prop="type">
                <el-select
                  size="default"
                  v-model="searchForm.type"
                  placeholder="请选择类型"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="用户端" :value="1" />
                  <el-option label="师傅端" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增金刚区
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>



      <!-- 表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
        >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="img" label="图标" width="120">
          <template #default="scope">
            <LbImage :src="scope.row.img" width="80" height="50" />
          </template>
        </el-table-column>

        <el-table-column prop="title" label="标题" min-width="150" />

        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 1 ? 'primary' : 'warning'">
              {{ scope.row.type === 1 ? '用户端' : '师傅端' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="link" label="链接地址" min-width="200">
          <template #default="scope">
            <el-link :href="scope.row.link" target="_blank" type="primary">
              {{ scope.row.link || '无链接' }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="top" label="排序" width="80" />

        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '可用' : '不可用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="时间" width="160">
          <template #default="scope">
            <div class="time-column">
              <p>{{ formatDate(scope.row.create_time) }}</p>
              <p>{{ formatTime(scope.row.create_time) }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <LbButton
              size="default"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </LbButton>
            <LbButton
              size="default"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </LbButton>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="form.id ? '编辑金刚区' : '新增金刚区'"
      width="600px"
      :before-close="handleClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入金刚区标题" />
        </el-form-item>

        <el-form-item label="图标" prop="img">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :on-change="handleImageChange"
            :file-list="fileList"
            list-type="picture-card"
            :limit="1"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <el-form-item label="链接地址" prop="link">
          <el-input v-model="form.link" placeholder="请输入链接地址（可选）" />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
            <el-option label="用户端" :value="1" />
            <el-option label="师傅端" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="排序" prop="top">
          <el-input-number
            v-model="form.top"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :value="1">可用</el-radio>
            <el-radio :value="-1">不可用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="handleClose">取消</LbButton>
          <LbButton
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbImage from '@/components/common/LbImage.vue'
import LbPage from '@/components/common/LbPage.vue'

// 直接导入API
import { api } from '@/api-v2'
// 旧版API导入已删除，现在使用api-v2

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const tableData = ref([])
const total = ref(0)
const fileList = ref([])

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  status: null,
  type: null
})

// 编辑表单
const form = reactive({
  id: null,
  title: '',
  img: '',
  link: '',
  type: 1,
  top: 0,
  status: 1
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入金刚区标题', trigger: 'blur' }
  ],
  img: [
    { required: true, message: '请上传金刚区图标', trigger: 'change' }
  ]
}

// 引用
const searchFormRef = ref()
const formRef = ref()

// 方法
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }
    
    // 添加可选参数
    if (searchForm.title) params.title = searchForm.title
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.type !== null && searchForm.type !== '') params.type = searchForm.type

    // 使用整合后的API-V2调用方式
    const result = await api.service.navList(params)
    console.log('📋 金刚区列表数据:', result)
    
    // 处理真实API的响应格式
    if (result.code === '200') {
      // 根据真实API数据结构处理
      const data = result.data
      tableData.value = data.list || []
      total.value = data.totalCount || data.total || 0
      
      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.meg || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取金刚区列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  getTableDataList(1)
}

const handleReset = () => {
  searchForm.title = ''
  searchForm.status = null
  searchForm.type = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  form.id = row.id
  form.title = row.title || ''
  form.img = row.img || ''
  form.link = row.link || ''
  form.type = row.type || 1
  form.top = row.top || 0
  form.status = row.status || 1
  
  // 如果有图片，设置文件列表用于显示
  if (row.img) {
    fileList.value = [{
      name: 'image',
      url: row.img
    }]
  }
  
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个金刚区吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await deleteNav(row.id)

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除金刚区失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    let result
    if (form.id) {
      // 编辑金刚区，使用整合后的API-V2调用方式
      result = await api.service.navUpdate(form)
    } else {
      // 新增金刚区，使用整合后的API-V2调用方式
      result = await api.service.navAdd(form)
    }

    if (result.code === '200') {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

const handleImageChange = (file, fileList) => {
  // 这里可以处理图片上传逻辑
  console.log('图片变更:', file, fileList)
  form.img = file.url || URL.createObjectURL(file.raw)
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  form.id = null
  form.title = ''
  form.img = ''
  form.link = ''
  form.type = 1
  form.top = 0
  form.status = 1
  fileList.value = []
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[0]
}

const formatTime = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[1]
}

// 生命周期
onMounted(() => {
  getTableDataList()
})
</script>

<style scoped>
.service-nav {
  padding: 0px;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
} 

.action-buttons {
  margin-bottom: 20px;
}

.time-column p {
  margin: 0;
 line-height: 1.4;
  font-size: 14px;
}

.time-column p:first-child {
  font-weight: 500;
}

.time-column p:last-child {
  color: #999;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.upload-demo {
  width: 100%;
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }
}
</style>
