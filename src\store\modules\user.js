/**
 * 用户状态管理模块
 * 
 * 功能：
 * - 用户偏好设置
 * - 用户行为记录
 * - 个人配置管理
 */

const state = {
  // 用户偏好设置
  preferences: {
    // 每页显示数量
    pageSize: 20,
    // 表格密度
    tableDensity: 'default',
    // 是否显示行号
    showRowNumber: true,
    // 默认排序
    defaultSort: 'desc'
  },
  
  // 用户行为记录
  behavior: {
    // 最近访问的页面
    recentPages: [],
    // 搜索历史
    searchHistory: [],
    // 操作历史
    actionHistory: []
  },
  
  // 个人配置
  profile: {
    // 头像
    avatar: '',
    // 个性签名
    signature: '',
    // 联系方式
    contact: {
      email: '',
      phone: '',
      address: ''
    }
  }
}

const mutations = {
  // 设置用户偏好
  SET_PREFERENCE(state, { key, value }) {
    if (state.preferences.hasOwnProperty(key)) {
      state.preferences[key] = value
    }
  },
  
  // 添加最近访问页面
  ADD_RECENT_PAGE(state, page) {
    const index = state.behavior.recentPages.findIndex(p => p.path === page.path)
    if (index > -1) {
      state.behavior.recentPages.splice(index, 1)
    }
    state.behavior.recentPages.unshift(page)
    
    // 最多保留10个
    if (state.behavior.recentPages.length > 10) {
      state.behavior.recentPages.pop()
    }
  },
  
  // 添加搜索历史
  ADD_SEARCH_HISTORY(state, keyword) {
    if (!keyword || keyword.trim() === '') return
    
    const index = state.behavior.searchHistory.indexOf(keyword)
    if (index > -1) {
      state.behavior.searchHistory.splice(index, 1)
    }
    state.behavior.searchHistory.unshift(keyword)
    
    // 最多保留20个
    if (state.behavior.searchHistory.length > 20) {
      state.behavior.searchHistory.pop()
    }
  },
  
  // 清除搜索历史
  CLEAR_SEARCH_HISTORY(state) {
    state.behavior.searchHistory = []
  },
  
  // 添加操作历史
  ADD_ACTION_HISTORY(state, action) {
    state.behavior.actionHistory.unshift({
      ...action,
      timestamp: Date.now()
    })
    
    // 最多保留50个
    if (state.behavior.actionHistory.length > 50) {
      state.behavior.actionHistory.pop()
    }
  },
  
  // 设置个人资料
  SET_PROFILE(state, profile) {
    state.profile = { ...state.profile, ...profile }
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.behavior.recentPages = []
    state.behavior.searchHistory = []
    state.behavior.actionHistory = []
  }
}

const actions = {
  // 设置用户偏好
  setPreference({ commit }, payload) {
    commit('SET_PREFERENCE', payload)
  },
  
  // 记录页面访问
  recordPageVisit({ commit }, page) {
    commit('ADD_RECENT_PAGE', {
      path: page.path,
      title: page.meta?.title || 'Unknown',
      timestamp: Date.now()
    })
  },
  
  // 记录搜索
  recordSearch({ commit }, keyword) {
    commit('ADD_SEARCH_HISTORY', keyword)
  },
  
  // 清除搜索历史
  clearSearchHistory({ commit }) {
    commit('CLEAR_SEARCH_HISTORY')
  },
  
  // 记录操作
  recordAction({ commit }, action) {
    commit('ADD_ACTION_HISTORY', action)
  },
  
  // 更新个人资料
  updateProfile({ commit }, profile) {
    commit('SET_PROFILE', profile)
  },
  
  // 重置状态
  resetState({ commit }) {
    commit('RESET_STATE')
  }
}

const getters = {
  // 用户偏好
  preferences: state => state.preferences,
  
  // 最近访问页面
  recentPages: state => state.behavior.recentPages,
  
  // 搜索历史
  searchHistory: state => state.behavior.searchHistory,
  
  // 操作历史
  actionHistory: state => state.behavior.actionHistory,
  
  // 个人资料
  profile: state => state.profile
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
