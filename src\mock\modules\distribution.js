/**
 * 分销管理模块Mock数据
 */

import Mock from 'mockjs'
import { successResponse, createCrudMock } from '../utils.js'

const distributionList = Mock.mock({
  'list|20-50': [{
    'id|+1': 1,
    'name': '@cname',
    'phone': '@phone',
    'level|1-3': 1,
    'commission|0-10000.2': 1,
    'inviteCount|0-100': 1,
    'status|1': [0, 1, 2],
    'createTime': '@datetime'
  }]
}).list

createCrudMock('/api/distribution', distributionList)

console.log('分销管理模块Mock数据已加载')
export { distributionList }
