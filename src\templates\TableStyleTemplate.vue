<!--
  表格样式模板 - Table Style Template
  基于ServiceJingang.vue的表格样式实现
  用于统一项目中所有表格的背景阴影和样式效果
-->

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <TopNav title="页面标题" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 搜索表单项 -->
              <el-form-item label="搜索条件" prop="keyword">
                <el-input
                  size="default"
                  v-model="searchForm.keyword"
                  placeholder="请输入搜索关键词"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item>
                <LbButton size="default" type="primary" icon="Search">
                  搜索
                </LbButton>
                <LbButton size="default" icon="RefreshLeft">
                  重置
                </LbButton>
                <LbButton size="default" type="primary" icon="Plus">
                  新增
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 - 关键样式实现 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ 
            background: '#f5f7fa', 
            color: '#606266', 
            fontSize: '16px', 
            fontWeight: '600' 
          }"
          :cell-style="{ 
            fontSize: '14px', 
            padding: '12px 8px' 
          }"
          style="width: 100%"
        >
          <!-- 表格列定义 -->
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="name" label="名称" min-width="150" />
          <!-- 更多列... -->
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: ''
})

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
}
</script>

<style scoped>
/* ===== 核心样式实现 ===== */

/* 1. 页面容器 - 基础布局 */
.page-container {
  padding: 0px;
}

/* 2. 内容容器 - 白色背景 + 圆角 + 阴影 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 3. 搜索表单 - 灰色背景区域 */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 4. 表格容器 - 关键的阴影和圆角效果 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 5. 表格样式 - 深度选择器覆盖Element Plus默认样式 */
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

/* 表头样式 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

/* 表格内容样式 */
.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

/* 行悬停效果 */
.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 6. 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 7. 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }
}
</style>
