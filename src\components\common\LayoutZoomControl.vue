<!--
  布局容器缩放控制组件
  专门用于处理整个布局容器的缩放
-->

<template>
  <div class="layout-zoom-controls" v-if="visible">
    <el-button-group size="small">
      <el-button 
        :type="currentZoom === 'small' ? 'primary' : 'default'"
        @click="setZoom('small')"
        title="缩小 (80%)"
      >
        <el-icon><ZoomOut /></el-icon>
      </el-button>
      <el-button 
        :type="currentZoom === 'normal' ? 'primary' : 'default'"
        @click="setZoom('normal')"
        title="正常 (100%)"
      >
        <el-icon><FullScreen /></el-icon>
      </el-button>
      <el-button 
        :type="currentZoom === 'large' ? 'primary' : 'default'"
        @click="setZoom('large')"
        title="放大 (120%)"
      >
        <el-icon><ZoomIn /></el-icon>
      </el-button>
      <el-button 
        :type="currentZoom === 'extra-large' ? 'primary' : 'default'"
        @click="setZoom('extra-large')"
        title="超大 (140%)"
      >
        <el-icon><Plus /></el-icon>
      </el-button>
    </el-button-group>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ZoomOut, ZoomIn, FullScreen, Plus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 是否显示控制按钮
  visible: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['zoom-change'])

// 响应式数据
const currentZoom = ref('normal')

// 缩放控制方法
const setZoom = (zoom) => {
  currentZoom.value = zoom
  applyLayoutZoom(zoom)
  // 保存用户的缩放偏好到localStorage
  localStorage.setItem('layout-zoom-preference', zoom)
  // 触发事件
  emit('zoom-change', zoom)
}

// 应用布局缩放样式
const applyLayoutZoom = (zoom) => {
  nextTick(() => {
    const layoutContainer = document.querySelector('.layout-container')
    if (layoutContainer) {
      // 移除所有缩放类
      layoutContainer.classList.remove('zoom-small', 'zoom-normal', 'zoom-large', 'zoom-extra-large')
      // 添加新的缩放类
      layoutContainer.classList.add(`zoom-${zoom}`)
      
      // 调整body的样式以适应缩放
      const body = document.body
      body.classList.remove('layout-zoom-small', 'layout-zoom-normal', 'layout-zoom-large', 'layout-zoom-extra-large')
      body.classList.add(`layout-zoom-${zoom}`)
    }
  })
}

// 从localStorage恢复缩放设置
const restoreZoomPreference = () => {
  const savedZoom = localStorage.getItem('layout-zoom-preference')
  if (savedZoom && ['small', 'normal', 'large', 'extra-large'].includes(savedZoom)) {
    currentZoom.value = savedZoom
    applyLayoutZoom(savedZoom)
  }
}

// 生命周期
onMounted(() => {
  restoreZoomPreference()
})

// 暴露方法给父组件
defineExpose({
  setZoom,
  currentZoom
})
</script>

<style scoped>
.layout-zoom-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.layout-zoom-controls:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.2);
}

.layout-zoom-controls .el-button-group {
  display: flex;
}

.layout-zoom-controls .el-button {
  border-radius: 4px;
  margin: 0 2px;
  min-width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
}

.layout-zoom-controls .el-button:hover {
  transform: translateY(-1px);
}

.layout-zoom-controls .el-button.el-button--primary {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.layout-zoom-controls .el-button .el-icon {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-zoom-controls {
    top: 10px;
    right: 10px;
    padding: 6px;
  }
  
  .layout-zoom-controls .el-button {
    min-width: 28px;
    height: 28px;
  }
  
  .layout-zoom-controls .el-button .el-icon {
    font-size: 12px;
  }
}

/* 确保在任何缩放级别下都保持正常大小 */
.layout-zoom-controls {
  transform: scale(1) !important;
}
</style>
