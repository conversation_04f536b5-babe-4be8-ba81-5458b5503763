<template>
  <div class="data-sync-test">
    <TopNav title="数据同步功能测试" />
    
    <div class="test-container">
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>事件总线测试</span>
          </div>
        </template>
        
        <div class="test-section">
          <h3>发送刷新事件</h3>
          <div class="button-group">
            <el-button type="primary" @click="refreshTechnician">刷新技师列表</el-button>
            <el-button type="success" @click="refreshService">刷新服务列表</el-button>
            <el-button type="warning" @click="refreshMarket">刷新营销列表</el-button>
            <el-button type="info" @click="refreshUser">刷新用户列表</el-button>
            <el-button type="danger" @click="refreshAll">刷新所有列表</el-button>
          </div>
        </div>

        <div class="test-section">
          <h3>模拟操作成功事件</h3>
          <div class="button-group">
            <el-button @click="simulateAdd">模拟新增成功</el-button>
            <el-button @click="simulateEdit">模拟编辑成功</el-button>
            <el-button @click="simulateDelete">模拟删除成功</el-button>
            <el-button @click="simulateStatusChange">模拟状态变更</el-button>
          </div>
        </div>

        <div class="test-section">
          <h3>事件监听状态</h3>
          <div class="stats-display">
            <pre>{{ JSON.stringify(eventStats, null, 2) }}</pre>
          </div>
        </div>

        <div class="test-section">
          <h3>事件日志</h3>
          <div class="log-container">
            <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-type" :class="log.type">{{ log.type }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
          <el-button @click="clearLogs" size="small">清空日志</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import eventBus, { EVENT_TYPES, refreshUtils, operationUtils } from '@/utils/eventBus'

// 响应式数据
const eventStats = ref({})
const eventLogs = ref([])

// 事件监听器清理函数
const cleanupFunctions = []

// 添加日志
const addLog = (type, message) => {
  eventLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    message
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 清空日志
const clearLogs = () => {
  eventLogs.value = []
}

// 更新事件统计
const updateStats = () => {
  eventStats.value = eventBus.getStats()
}

// 刷新事件测试
const refreshTechnician = () => {
  refreshUtils.refreshTechnicianList({ test: true, timestamp: Date.now() })
  ElMessage.success('已发送技师列表刷新事件')
  addLog('REFRESH', '发送技师列表刷新事件')
}

const refreshService = () => {
  refreshUtils.refreshServiceList({ test: true, timestamp: Date.now() })
  ElMessage.success('已发送服务列表刷新事件')
  addLog('REFRESH', '发送服务列表刷新事件')
}

const refreshMarket = () => {
  refreshUtils.refreshMarketList({ test: true, timestamp: Date.now() })
  ElMessage.success('已发送营销列表刷新事件')
  addLog('REFRESH', '发送营销列表刷新事件')
}

const refreshUser = () => {
  refreshUtils.refreshUserList({ test: true, timestamp: Date.now() })
  ElMessage.success('已发送用户列表刷新事件')
  addLog('REFRESH', '发送用户列表刷新事件')
}

const refreshAll = () => {
  refreshUtils.refreshAll({ test: true, timestamp: Date.now() })
  ElMessage.success('已发送全局刷新事件')
  addLog('REFRESH', '发送全局刷新事件')
}

// 操作成功事件测试
const simulateAdd = () => {
  operationUtils.addSuccess('test', { id: Date.now(), name: '测试数据' })
  ElMessage.success('已发送新增成功事件')
  addLog('OPERATION', '发送新增成功事件')
}

const simulateEdit = () => {
  operationUtils.editSuccess('test', { id: Date.now(), name: '编辑测试数据' })
  ElMessage.success('已发送编辑成功事件')
  addLog('OPERATION', '发送编辑成功事件')
}

const simulateDelete = () => {
  operationUtils.deleteSuccess('test', { id: Date.now() })
  ElMessage.success('已发送删除成功事件')
  addLog('OPERATION', '发送删除成功事件')
}

const simulateStatusChange = () => {
  operationUtils.statusChangeSuccess('test', { id: Date.now(), status: 1 })
  ElMessage.success('已发送状态变更事件')
  addLog('OPERATION', '发送状态变更事件')
}

// 设置事件监听器
const setupEventListeners = () => {
  // 监听所有刷新事件
  Object.values(EVENT_TYPES).forEach(eventType => {
    if (eventType.includes('REFRESH') || eventType.includes('SUCCESS')) {
      const unsubscribe = eventBus.on(eventType, (data) => {
        addLog('RECEIVED', `接收到事件: ${eventType}`)
        updateStats()
      })
      cleanupFunctions.push(unsubscribe)
    }
  })
}

// 清理事件监听器
const cleanup = () => {
  cleanupFunctions.forEach(fn => fn())
  cleanupFunctions.length = 0
}

// 生命周期
onMounted(() => {
  setupEventListeners()
  updateStats()
  addLog('SYSTEM', '测试页面已加载，开始监听事件')
})

onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.data-sync-test {
  padding: 0;
  background-color: transparent;
  min-height: calc(100vh - 60px);
}

.test-container {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.test-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: bold;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.stats-display {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.stats-display pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #e4e7ed;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.log-type {
  margin-right: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  min-width: 80px;
  text-align: center;
  font-size: 11px;
}

.log-type.REFRESH {
  background-color: #e1f3d8;
  color: #67c23a;
}

.log-type.OPERATION {
  background-color: #ecf5ff;
  color: #409eff;
}

.log-type.RECEIVED {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.log-type.SYSTEM {
  background-color: #f4f4f5;
  color: #909399;
}

.log-message {
  color: #303133;
  flex: 1;
}

@media (max-width: 768px) {
  .test-container {
    padding: 10px;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .button-group .el-button {
    width: 100%;
  }
}
</style>
