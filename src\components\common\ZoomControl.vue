<!--
  页面缩放控制组件
  支持页面整体缩放功能
-->

<template>
  <div class="zoom-controls">
    <el-button-group size="small">
      <el-button 
        :type="currentZoom === 'small' ? 'primary' : 'default'"
        @click="setZoom('small')"
        title="缩小 (80%)"
      >
        <el-icon><ZoomOut /></el-icon>
      </el-button>
      <el-button 
        :type="currentZoom === 'normal' ? 'primary' : 'default'"
        @click="setZoom('normal')"
        title="正常 (100%)"
      >
        <el-icon><FullScreen /></el-icon>
      </el-button>
      <el-button 
        :type="currentZoom === 'large' ? 'primary' : 'default'"
        @click="setZoom('large')"
        title="放大 (120%)"
      >
        <el-icon><ZoomIn /></el-icon>
      </el-button>
      <el-button 
        :type="currentZoom === 'extra-large' ? 'primary' : 'default'"
        @click="setZoom('extra-large')"
        title="超大 (140%)"
      >
        <el-icon><Plus /></el-icon>
      </el-button>
    </el-button-group>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ZoomOut, ZoomIn, FullScreen, Plus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 目标元素的选择器，默认为页面根容器
  targetSelector: {
    type: String,
    default: '.page-container'
  },
  // 是否显示控制按钮
  visible: {
    type: Boolean,
    default: true
  },
  // 是否自动检测布局容器
  autoDetectLayout: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['zoom-change'])

// 响应式数据
const currentZoom = ref('normal')

// 缩放控制方法
const setZoom = (zoom) => {
  currentZoom.value = zoom
  applyZoom(zoom)
  // 保存用户的缩放偏好到localStorage
  localStorage.setItem('page-zoom-preference', zoom)
  // 触发事件
  emit('zoom-change', zoom)
}

// 应用缩放样式
const applyZoom = (zoom) => {
  const targetElement = document.querySelector(props.targetSelector)
  if (targetElement) {
    // 移除所有缩放类
    targetElement.classList.remove('zoom-small', 'zoom-normal', 'zoom-large', 'zoom-extra-large')
    // 添加新的缩放类
    targetElement.classList.add(`zoom-${zoom}`)
  }

  // 自动检测并处理布局容器的缩放
  if (props.autoDetectLayout) {
    const layoutContainer = document.querySelector('.layout-container')
    if (layoutContainer && layoutContainer !== targetElement) {
      // 移除所有缩放类
      layoutContainer.classList.remove('zoom-small', 'zoom-normal', 'zoom-large', 'zoom-extra-large')
      // 添加新的缩放类
      layoutContainer.classList.add(`zoom-${zoom}`)
    }
  }
}

// 从localStorage恢复缩放设置
const restoreZoomPreference = () => {
  const savedZoom = localStorage.getItem('page-zoom-preference')
  if (savedZoom && ['small', 'normal', 'large', 'extra-large'].includes(savedZoom)) {
    currentZoom.value = savedZoom
    applyZoom(savedZoom)
  }
}

// 监听目标选择器变化
watch(() => props.targetSelector, () => {
  applyZoom(currentZoom.value)
})

// 生命周期
onMounted(() => {
  restoreZoomPreference()
})

// 暴露方法给父组件
defineExpose({
  setZoom,
  currentZoom
})
</script>

<style scoped>
.zoom-controls {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  transform: scale(1) !important;
}

.zoom-controls:hover {
  background: rgba(255, 255, 255, 0.95);
}

.zoom-controls .el-button-group {
  display: flex;
}

.zoom-controls .el-button {
  border-radius: 4px;
  margin: 0 2px;
  min-width: 32px;
  height: 32px;
}

.zoom-controls .el-button .el-icon {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .zoom-controls {
    top: 60px;
    right: 10px;
    padding: 6px;
  }
  
  .zoom-controls .el-button {
    min-width: 28px;
    height: 28px;
  }
  
  .zoom-controls .el-button .el-icon {
    font-size: 12px;
  }
}
</style>
