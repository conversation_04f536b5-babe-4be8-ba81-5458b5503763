/**
 * Mock数据工具函数
 * 提供通用的Mock数据生成和响应函数
 */

import Mock from 'mockjs'

// 成功响应格式
export const successResponse = (data = null, message = '操作成功') => {
  return Mock.mock({
    code: 200,
    message,
    data,
    timestamp: Date.now()
  })
}

// 分页响应格式
export const pageResponse = (list = [], total = 0, page = 1, pageSize = 10) => {
  return successResponse({
    list,
    total,
    page: parseInt(page),
    pageSize: parseInt(pageSize),
    totalPages: Math.ceil(total / pageSize)
  })
}

// 错误响应格式
export const errorResponse = (message = '操作失败', code = 500) => {
  return Mock.mock({
    code,
    message,
    data: null,
    timestamp: Date.now()
  })
}

// 通用CRUD操作Mock生成器
export const createCrudMock = (baseUrl, mockData = []) => {
  console.log(`🔧 创建CRUD Mock: ${baseUrl}, 数据量: ${mockData.length}`)

  // 设置Mock拦截
  Mock.setup({
    timeout: '200-500'
  })

  // 列表查询 - 使用正则表达式匹配查询参数
  const listUrlPattern = new RegExp(`^${baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/list(\\?.*)?$`)
  console.log(`🔧 注册列表API: ${baseUrl}/list`)
  Mock.mock(listUrlPattern, 'get', (options) => {
    try {
      const url = new URL('http://localhost' + options.url)
      const page = parseInt(url.searchParams.get('page') || '1')
      const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
      const keyword = url.searchParams.get('keyword') || ''
      
      let filteredData = [...mockData]
      if (keyword) {
        filteredData = mockData.filter(item => 
          JSON.stringify(item).toLowerCase().includes(keyword.toLowerCase())
        )
      }
      
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = filteredData.slice(start, end)
      
      return pageResponse(list, filteredData.length, page, pageSize)
    } catch (error) {
      console.error('Mock列表查询错误:', error)
      return errorResponse('查询失败')
    }
  })
  
  // 详情查询
  const detailUrl = new RegExp(`${baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/detail/(\\d+)`)
  console.log(`🔧 注册详情API: ${baseUrl}/detail/:id`)
  Mock.mock(detailUrl, 'get', (options) => {
    try {
      const id = parseInt(options.url.match(/\/detail\/(\d+)/)[1])
      const item = mockData.find(item => item.id === id)

      if (item) {
        return successResponse(item)
      } else {
        return errorResponse('数据不存在', 404)
      }
    } catch (error) {
      console.error('Mock详情查询错误:', error)
      return errorResponse('查询失败')
    }
  })

  // 新增
  const addUrlPattern = new RegExp(`^${baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/add$`)
  console.log(`🔧 注册新增API: ${baseUrl}/add`)
  Mock.mock(addUrlPattern, 'post', (options) => {
    try {
      const newItem = JSON.parse(options.body)
      newItem.id = mockData.length > 0 ? Math.max(...mockData.map(item => item.id)) + 1 : 1
      newItem.create_time = Mock.Random.datetime()
      newItem.update_time = Mock.Random.datetime()
      newItem.createTime = newItem.create_time // 兼容字段
      newItem.updateTime = newItem.update_time

      mockData.push(newItem)
      return successResponse(newItem, '新增成功')
    } catch (error) {
      console.error('Mock新增错误:', error)
      return errorResponse('新增失败')
    }
  })

  // 更新
  const updateUrl = new RegExp(`${baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/update/(\\d+)`)
  console.log(`🔧 注册更新API: ${baseUrl}/update/:id`)
  Mock.mock(updateUrl, 'put', (options) => {
    try {
      const id = parseInt(options.url.match(/\/update\/(\d+)/)[1])
      const updateData = JSON.parse(options.body)
      const index = mockData.findIndex(item => item.id === id)

      if (index !== -1) {
        mockData[index] = {
          ...mockData[index],
          ...updateData,
          update_time: Mock.Random.datetime()
        }
        mockData[index].updateTime = mockData[index].update_time // 兼容字段
        return successResponse(mockData[index], '更新成功')
      } else {
        return errorResponse('数据不存在', 404)
      }
    } catch (error) {
      console.error('Mock更新错误:', error)
      return errorResponse('更新失败')
    }
  })

  // 删除
  const deleteUrl = new RegExp(`${baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/delete/(\\d+)`)
  console.log(`🔧 注册删除API: ${baseUrl}/delete/:id`)
  Mock.mock(deleteUrl, 'delete', (options) => {
    try {
      const id = parseInt(options.url.match(/\/delete\/(\d+)/)[1])
      const index = mockData.findIndex(item => item.id === id)

      if (index !== -1) {
        mockData.splice(index, 1)
        return successResponse(null, '删除成功')
      } else {
        return errorResponse('数据不存在', 404)
      }
    } catch (error) {
      console.error('Mock删除错误:', error)
      return errorResponse('删除失败')
    }
  })
}

console.log('✅ Mock工具函数加载完成')
