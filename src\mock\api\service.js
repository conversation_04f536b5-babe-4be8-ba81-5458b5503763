/**
 * 服务项目模块Mock API
 * 使用vite-plugin-mock格式
 */

import <PERSON>ck from 'mockjs'

// 生成服务项目数据
const generateServiceList = (count = 50) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    title: Mock.Random.ctitle(5, 15),
    category: Mock.Random.pick(['家政服务', '维修服务', '清洁服务', '搬家服务', '装修服务']),
    price: Mock.Random.float(50, 500, 2, 2),
    originalPrice: Mock.Random.float(60, 600, 2, 2),
    description: Mock.Random.cparagraph(1, 3),
    image: Mock.Random.image('300x200', Mock.Random.color(), '#FFF', 'png', Mock.Random.word()),
    status: Mock.Random.pick([0, 1]),
    sort: Mock.Random.integer(1, 100),
    viewCount: Mock.Random.integer(100, 10000),
    orderCount: Mock.Random.integer(10, 1000),
    rating: Mock.Random.float(3.5, 5.0, 1, 1),
    tags: Mock.Random.shuffle(['热门', '推荐', '新品', '特价']).slice(0, Mock.Random.integer(1, 3)),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime(),
    createTime: Mock.Random.datetime(),
    updateTime: Mock.Random.datetime()
  }))
}

// 生成轮播图数据
const generateBannerList = (count = 15) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    title: Mock.Random.ctitle(5, 10),
    image: Mock.Random.image('800x400', Mock.Random.color(), '#FFF', 'png', 'Banner'),
    link: Mock.Random.url(),
    sort: Mock.Random.integer(1, 100),
    status: Mock.Random.pick([0, 1]),
    clickCount: Mock.Random.integer(100, 5000),
    startTime: Mock.Random.datetime(),
    endTime: Mock.Random.datetime(),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime()
  }))
}

// 生成分类数据
const generateCategoryList = (count = 25) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: Mock.Random.ctitle(2, 6),
    parent_id: index < 5 ? 0 : Mock.Random.integer(1, 5),
    icon: Mock.Random.pick(['Service', 'Tool', 'Home', 'Car', 'Beauty']),
    sort: Mock.Random.integer(1, 100),
    status: Mock.Random.pick([0, 1]),
    is_recommend: Mock.Random.pick([0, 1]),
    description: Mock.Random.csentence(5, 20),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime()
  }))
}

// 生成金刚区数据
const generateJingangList = (count = 12) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: Mock.Random.ctitle(2, 4),
    icon: Mock.Random.pick(['Service', 'Tool', 'Home', 'Car', 'Beauty', 'Clean', 'Repair', 'Move']),
    link: Mock.Random.url(),
    sort: Mock.Random.integer(1, 100),
    position: Mock.Random.integer(1, 8),
    status: Mock.Random.pick([0, 1]),
    clickCount: Mock.Random.integer(50, 2000),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime()
  }))
}

// 生成服务点数据
const generateServicePointList = (count = 30) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: Mock.Random.ctitle(5, 10) + '服务点',
    address: Mock.Random.county(true) + Mock.Random.ctitle(5, 15),
    contact: Mock.Random.cname(),
    phone: Mock.Random.pick(['138', '139', '150', '151', '152']) + Mock.Random.string('number', 8),
    latitude: Mock.Random.float(39.8, 40.2, 6, 6),
    longitude: Mock.Random.float(116.2, 116.8, 6, 6),
    status: Mock.Random.pick([0, 1]),
    serviceRadius: Mock.Random.integer(5, 50),
    rating: Mock.Random.float(3.5, 5.0, 1, 1),
    orderCount: Mock.Random.integer(10, 500),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime()
  }))
}

// 生成配置数据
const generateConfigList = (count = 25) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    key: `config_${index + 1}`,
    name: Mock.Random.ctitle(3, 8),
    value: Mock.Random.pick(['0.05', '100', '30', '7', 'true', 'false']),
    type: Mock.Random.pick(['number', 'string', 'boolean']),
    group: Mock.Random.pick(['基础设置', '支付设置', '订单设置', '用户设置']),
    description: Mock.Random.csentence(10, 30),
    sort: Mock.Random.integer(1, 100),
    create_time: Mock.Random.datetime(),
    update_time: Mock.Random.datetime()
  }))
}

// 初始化数据
const serviceList = generateServiceList()
const bannerList = generateBannerList()
const categoryList = generateCategoryList()
const jingangList = generateJingangList()
const servicePointList = generateServicePointList()
const configList = generateConfigList()

// 生成分类树
const categoryTreeList = categoryList
  .filter(item => item.parent_id === 0)
  .map(parent => ({
    ...parent,
    children: categoryList.filter(child => child.parent_id === parent.id)
  }))

// 通用响应格式
const successResponse = (data = null, message = '操作成功') => ({
  code: 200,
  message,
  data,
  timestamp: Date.now()
})

const pageResponse = (list = [], total = 0, page = 1, pageSize = 10) => ({
  code: 200,
  message: '操作成功',
  data: {
    list,
    total,
    page: parseInt(page),
    pageSize: parseInt(pageSize),
    totalPages: Math.ceil(total / pageSize)
  },
  timestamp: Date.now()
})

const errorResponse = (message = '操作失败', code = 500) => ({
  code,
  message,
  data: null,
  timestamp: Date.now()
})

// 通用分页处理
const handlePagination = (data, url) => {
  const urlObj = new URL(url, 'http://localhost')
  const page = parseInt(urlObj.searchParams.get('page') || '1')
  const pageSize = parseInt(urlObj.searchParams.get('pageSize') || '10')
  const keyword = urlObj.searchParams.get('keyword') || ''
  
  let filteredData = [...data]
  if (keyword) {
    filteredData = data.filter(item => 
      JSON.stringify(item).toLowerCase().includes(keyword.toLowerCase())
    )
  }
  
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)
  
  return pageResponse(list, filteredData.length, page, pageSize)
}

console.log('🔧 服务项目Mock API已加载，数据统计:')
console.log(`   - 服务项目: ${serviceList.length} 条`)
console.log(`   - 轮播图: ${bannerList.length} 条`)
console.log(`   - 分类: ${categoryList.length} 条`)
console.log(`   - 金刚区: ${jingangList.length} 条`)
console.log(`   - 服务点: ${servicePointList.length} 条`)
console.log(`   - 配置: ${configList.length} 条`)

export default [
  // 服务项目管理
  {
    url: '/api/service/list',
    method: 'get',
    response: ({ url }) => {
      console.log('📋 服务项目列表API被调用:', url)
      return handlePagination(serviceList, url)
    }
  },
  {
    url: '/api/service/add',
    method: 'post',
    response: ({ body }) => {
      console.log('➕ 服务项目新增API被调用:', body)
      const newItem = JSON.parse(body)
      newItem.id = serviceList.length + 1
      newItem.create_time = Mock.Random.datetime()
      newItem.update_time = Mock.Random.datetime()
      serviceList.push(newItem)
      return successResponse(newItem, '新增成功')
    }
  },
  {
    url: '/api/service/update/:id',
    method: 'put',
    response: ({ url, body }) => {
      console.log('✏️ 服务项目更新API被调用:', url, body)
      const id = parseInt(url.split('/').pop())
      const updateData = JSON.parse(body)
      const index = serviceList.findIndex(item => item.id === id)
      
      if (index !== -1) {
        serviceList[index] = { ...serviceList[index], ...updateData, update_time: Mock.Random.datetime() }
        return successResponse(serviceList[index], '更新成功')
      } else {
        return errorResponse('数据不存在', 404)
      }
    }
  },
  {
    url: '/api/service/delete/:id',
    method: 'delete',
    response: ({ url }) => {
      console.log('🗑️ 服务项目删除API被调用:', url)
      const id = parseInt(url.split('/').pop())
      const index = serviceList.findIndex(item => item.id === id)
      
      if (index !== -1) {
        serviceList.splice(index, 1)
        return successResponse(null, '删除成功')
      } else {
        return errorResponse('数据不存在', 404)
      }
    }
  },
  {
    url: '/api/service/statistics',
    method: 'get',
    response: () => {
      console.log('📊 服务项目统计API被调用')
      return successResponse({
        totalServices: serviceList.length,
        activeServices: serviceList.filter(item => item.status === 1).length,
        totalCategories: categoryList.length,
        totalBanners: bannerList.length,
        totalPoints: servicePointList.length,
        totalJingang: jingangList.length,
        totalConfigs: configList.length,
        monthlyOrders: Mock.Random.integer(100, 1000),
        monthlyRevenue: Mock.Random.float(10000, 100000, 2, 2),
        todayOrders: Mock.Random.integer(10, 100),
        todayRevenue: Mock.Random.float(1000, 10000, 2, 2)
      })
    }
  },

  // 轮播图设置
  {
    url: '/api/service/banner',
    method: 'get',
    response: ({ url }) => {
      console.log('🖼️ 轮播图列表API被调用:', url)
      return handlePagination(bannerList, url)
    }
  },
  {
    url: '/api/service/banner/add',
    method: 'post',
    response: ({ body }) => {
      console.log('➕ 轮播图新增API被调用:', body)
      const newItem = JSON.parse(body)
      newItem.id = bannerList.length + 1
      newItem.create_time = Mock.Random.datetime()
      newItem.update_time = Mock.Random.datetime()
      bannerList.push(newItem)
      return successResponse(newItem, '新增成功')
    }
  },
  {
    url: '/api/service/banner/update/:id',
    method: 'put',
    response: ({ url, body }) => {
      console.log('✏️ 轮播图更新API被调用:', url, body)
      const id = parseInt(url.split('/').pop())
      const updateData = JSON.parse(body)
      const index = bannerList.findIndex(item => item.id === id)
      
      if (index !== -1) {
        bannerList[index] = { ...bannerList[index], ...updateData, update_time: Mock.Random.datetime() }
        return successResponse(bannerList[index], '更新成功')
      } else {
        return errorResponse('数据不存在', 404)
      }
    }
  },
  {
    url: '/api/service/banner/delete/:id',
    method: 'delete',
    response: ({ url }) => {
      console.log('🗑️ 轮播图删除API被调用:', url)
      const id = parseInt(url.split('/').pop())
      const index = bannerList.findIndex(item => item.id === id)
      
      if (index !== -1) {
        bannerList.splice(index, 1)
        return successResponse(null, '删除成功')
      } else {
        return errorResponse('数据不存在', 404)
      }
    }
  },
  {
    url: '/api/service/banner/click/:id',
    method: 'post',
    response: ({ url }) => {
      console.log('👆 轮播图点击统计API被调用:', url)
      const id = parseInt(url.split('/').pop())
      const banner = bannerList.find(item => item.id === id)
      
      if (banner) {
        banner.clickCount = (banner.clickCount || 0) + 1
        return successResponse({ clickCount: banner.clickCount }, '点击统计成功')
      } else {
        return errorResponse('轮播图不存在', 404)
      }
    }
  },

  // 分类设置
  {
    url: '/api/service/category',
    method: 'get',
    response: ({ url }) => {
      console.log('📂 分类列表API被调用:', url)
      return handlePagination(categoryList, url)
    }
  },
  {
    url: '/api/service/category/tree',
    method: 'get',
    response: () => {
      console.log('🌳 分类树API被调用')
      return successResponse(categoryTreeList)
    }
  },
  {
    url: '/api/service/category/add',
    method: 'post',
    response: ({ body }) => {
      console.log('➕ 分类新增API被调用:', body)
      const newItem = JSON.parse(body)
      newItem.id = categoryList.length + 1
      newItem.create_time = Mock.Random.datetime()
      newItem.update_time = Mock.Random.datetime()
      categoryList.push(newItem)
      return successResponse(newItem, '新增成功')
    }
  },
  {
    url: '/api/service/category/recommend/:id',
    method: 'put',
    response: ({ url, body }) => {
      console.log('⭐ 分类推荐切换API被调用:', url, body)
      const id = parseInt(url.split('/').pop())
      const { is_recommend } = JSON.parse(body)
      const index = categoryList.findIndex(item => item.id === id)

      if (index !== -1) {
        categoryList[index].is_recommend = is_recommend
        categoryList[index].update_time = Mock.Random.datetime()
        return successResponse(categoryList[index], '推荐状态更新成功')
      } else {
        return errorResponse('分类不存在', 404)
      }
    }
  },

  // 金刚区设置
  {
    url: '/api/service/jingang',
    method: 'get',
    response: ({ url }) => {
      console.log('💎 金刚区列表API被调用:', url)
      return handlePagination(jingangList, url)
    }
  },
  {
    url: '/api/service/jingang/add',
    method: 'post',
    response: ({ body }) => {
      console.log('➕ 金刚区新增API被调用:', body)
      const newItem = JSON.parse(body)
      newItem.id = jingangList.length + 1
      newItem.create_time = Mock.Random.datetime()
      newItem.update_time = Mock.Random.datetime()
      jingangList.push(newItem)
      return successResponse(newItem, '新增成功')
    }
  },
  {
    url: '/api/service/jingang/sort',
    method: 'put',
    response: ({ body }) => {
      console.log('🔄 金刚区排序API被调用:', body)
      const { sortData } = JSON.parse(body)

      sortData.forEach(item => {
        const index = jingangList.findIndex(j => j.id === item.id)
        if (index !== -1) {
          jingangList[index].sort = item.sort
          jingangList[index].position = item.position
          jingangList[index].update_time = Mock.Random.datetime()
        }
      })

      return successResponse(null, '排序更新成功')
    }
  },

  // 服务点设置
  {
    url: '/api/service/point',
    method: 'get',
    response: ({ url }) => {
      console.log('📍 服务点列表API被调用:', url)
      return handlePagination(servicePointList, url)
    }
  },
  {
    url: '/api/service/point/add',
    method: 'post',
    response: ({ body }) => {
      console.log('➕ 服务点新增API被调用:', body)
      const newItem = JSON.parse(body)
      newItem.id = servicePointList.length + 1
      newItem.create_time = Mock.Random.datetime()
      newItem.update_time = Mock.Random.datetime()
      servicePointList.push(newItem)
      return successResponse(newItem, '新增成功')
    }
  },
  {
    url: '/api/service/point/batch-status',
    method: 'put',
    response: ({ body }) => {
      console.log('🔄 服务点批量状态更新API被调用:', body)
      const { ids, status } = JSON.parse(body)

      let updatedCount = 0
      ids.forEach(id => {
        const index = servicePointList.findIndex(item => item.id === id)
        if (index !== -1) {
          servicePointList[index].status = status
          servicePointList[index].update_time = Mock.Random.datetime()
          updatedCount++
        }
      })

      return successResponse({ updatedCount }, `批量更新成功，共更新${updatedCount}条记录`)
    }
  },

  // 项目配置
  {
    url: '/api/service/config',
    method: 'get',
    response: ({ url }) => {
      console.log('⚙️ 配置列表API被调用:', url)
      return handlePagination(configList, url)
    }
  },
  {
    url: '/api/service/config/groups',
    method: 'get',
    response: () => {
      console.log('📋 配置分组API被调用')
      const groups = [...new Set(configList.map(item => item.group))]
      return successResponse(groups)
    }
  },
  {
    url: '/api/service/config/batch',
    method: 'put',
    response: ({ body }) => {
      console.log('🔄 配置批量更新API被调用:', body)
      const { configs } = JSON.parse(body)

      let updatedCount = 0
      configs.forEach(config => {
        const index = configList.findIndex(item => item.id === config.id)
        if (index !== -1) {
          configList[index].value = config.value
          configList[index].update_time = Mock.Random.datetime()
          updatedCount++
        }
      })

      return successResponse({ updatedCount }, `批量更新成功，共更新${updatedCount}条配置`)
    }
  }
]
