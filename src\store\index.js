/**
 * Vuex4 Store 主配置文件

 * 
 * 功能：
 * - 模块化状态管理
 * - 状态持久化
 * - 开发工具集成
 * - 插件配置
 */

import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'

// 导入模块
import auth from './modules/auth'
import user from './modules/user'
import ui from './modules/ui'
import routes from './modules/routes'
import app from './modules/app'
import menu from './modules/menu'

// 创建store实例
const store = createStore({
  modules: {
    auth,
    user,
    ui,
    routes,
    app,
    menu
  },
  
  // 全局状态
  state: {
    // 应用版本
    version: '3.0.0',
    // 构建时间
    buildTime: new Date().toISOString()
  },
  
  // 全局getters
  getters: {
    // 获取应用版本
    version: state => state.version,
    // 获取构建时间
    buildTime: state => state.buildTime,
    // 获取所有模块状态
    allModules: state => Object.keys(state)
  },
  
  // 全局mutations
  mutations: {
    // 重置所有状态
    RESET_ALL_STATE(state) {
      // 这里可以添加重置逻辑
      console.log('重置所有状态')
    }
  },
  
  // 全局actions
  actions: {
    // 重置应用状态
    resetApp({ commit, dispatch }) {
      return new Promise((resolve) => {
        // 重置各模块状态
        dispatch('auth/resetState')
        dispatch('user/resetState')
        dispatch('ui/resetState')
        dispatch('routes/resetState')
        dispatch('app/resetState')
        dispatch('menu/resetMenus')
        
        commit('RESET_ALL_STATE')
        resolve()
      })
    },
    
    // 初始化应用
    initApp({ dispatch }) {
      return new Promise(async (resolve) => {
        try {
          // 初始化UI状态
          await dispatch('ui/initializeApp')

          // 初始化应用设置
          await dispatch('app/loadSettings')

          // 不再在应用初始化时获取菜单数据
          // 菜单数据应该在登录时设置，或者在路由守卫中使用降级菜单
          console.log('📱 应用初始化完成，跳过菜单数据获取')

          resolve()
        } catch (error) {
          console.error('应用初始化失败:', error)
          resolve()
        }
      })
    }
  },
  
  // 插件配置
  plugins: [
    // 状态持久化插件
    createPersistedState({
      key: 'admin-system-v3',
      paths: [
        'auth.token',
        'auth.refreshToken',
        'user.userInfo',
        'ui.theme',
        'ui.sidebarCollapsed',
        'ui.language',
        'app.settings'
      ],
      storage: window.localStorage
    })
  ],
  
  // 严格模式（开发环境启用）
  strict: import.meta.env.DEV
})

export default store
