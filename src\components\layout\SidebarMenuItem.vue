<!--
  SidebarMenuItem 组件
  递归渲染侧边栏菜单项
  
  功能：
  - 支持多级菜单
  - 图标显示
  - 权限控制
  - 外链处理
-->

<template>
  <!-- 有子菜单的情况 -->
  <el-sub-menu
    v-if="hasChildren && !route.meta?.hideChildren"
    :index="route.path"
    :popper-class="collapsed ? 'sidebar-popper' : ''"
  >
    <template #title>
      <el-icon v-if="route.meta?.icon">
        <component :is="route.meta.icon" />
      </el-icon>
      <span v-if="route.meta?.title">{{ route.meta.title }}</span>
    </template>
    
    <sidebar-menu-item
      v-for="child in route.children"
      :key="child.path"
      :route="child"
      :collapsed="collapsed"
      :base-path="resolvePath(route.path, child.path)"
    />
  </el-sub-menu>

  <!-- 单个菜单项 -->
  <el-menu-item
    v-else
    :index="resolvePath(basePath, route.path)"
    @click="handleClick"
  >
    <el-icon v-if="route.meta?.icon">
      <component :is="route.meta.icon" />
    </el-icon>
    <template #title>
      <span v-if="route.meta?.title">{{ route.meta.title }}</span>
      <el-tag
        v-if="route.meta?.badge"
        :type="route.meta.badgeType || 'danger'"
        size="small"
        class="menu-badge"
      >
        {{ route.meta.badge }}
      </el-tag>
    </template>
  </el-menu-item>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  route: {
    type: Object,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
})

const router = useRouter()

// 计算属性
const hasChildren = computed(() => {
  return props.route.children && 
         props.route.children.length > 0 && 
         props.route.children.some(child => !child.meta?.hidden)
})

// 方法
const resolvePath = (basePath, path) => {
  if (path.startsWith('/')) {
    return path
  }
  if (basePath.endsWith('/')) {
    return basePath + path
  }
  return basePath + '/' + path
}

const handleClick = () => {
  const { path, meta } = props.route
  
  // 处理外链
  if (meta?.isExternal) {
    window.open(path, '_blank')
    return
  }
  
  // 处理内部路由
  const fullPath = resolvePath(props.basePath, path)
  router.push(fullPath)
}
</script>

<style scoped>
.menu-badge {
  margin-left: var(--spacing-xs);
  transform: scale(0.8);
}

/* 折叠状态下的弹出菜单样式 */
:global(.sidebar-popper) {
  margin-left: 8px !important;
}

:global(.sidebar-popper .el-menu) {
  background-color: var(--bg-color-base);
  border: 1px solid var(--border-color-light);
  box-shadow: var(--box-shadow-base);
  border-radius: var(--border-radius-sm);
}

:global(.sidebar-popper .el-menu-item) {
  margin: 0;
  border-radius: 0;
}

:global(.sidebar-popper .el-menu-item:first-child) {
  border-top-left-radius: var(--border-radius-sm);
  border-top-right-radius: var(--border-radius-sm);
}

:global(.sidebar-popper .el-menu-item:last-child) {
  border-bottom-left-radius: var(--border-radius-sm);
  border-bottom-right-radius: var(--border-radius-sm);
}
</style>
