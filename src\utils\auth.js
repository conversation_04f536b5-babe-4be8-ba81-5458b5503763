/**
 * 认证工具函数
 *
 * 功能：
 * - Token管理（支持Cookie和localStorage）
 * - 本地存储操作
 * - 认证状态检查
 */

const TokenKey = 'admin-token'
const RefreshTokenKey = 'admin-refresh-token'
const CookieTokenKey = 'autograph' // Cookie中的token键名

/**
 * 从Cookie中获取指定名称的值
 * @param {string} name Cookie名称
 * @returns {string|null} Cookie值
 */
function getCookie(name) {
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) {
    return parts.pop().split(';').shift()
  }
  return null
}

/**
 * 设置Cookie
 * @param {string} name <PERSON>ie名称
 * @param {string} value Cookie值
 * @param {number} days 过期天数
 */
function setCookie(name, value, days = 30) {
  const expires = new Date()
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`
}

/**
 * 删除Cookie
 * @param {string} name Cookie名称
 */
function deleteCookie(name) {
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
}

// 获取Token（优先从Cookie获取，然后从localStorage获取）
export function getToken() {
  // 优先从Cookie中获取autograph token
  const cookieToken = getCookie(CookieTokenKey)
  if (cookieToken) {
    return cookieToken
  }

  // 如果Cookie中没有，则从localStorage获取
  return localStorage.getItem(TokenKey)
}

// 设置Token（仅用于状态管理，不覆盖后端设置的cookie）
export function setToken(token) {
  // 在Cookie认证模式下，只设置localStorage用于状态管理
  // 不覆盖后端设置的autograph cookie
  console.log('🔧 setToken: 仅设置localStorage状态，不覆盖cookie')
  return localStorage.setItem(TokenKey, token)
}

// 专门用于设置Cookie的函数（谨慎使用）
export function setTokenCookie(token) {
  console.warn('⚠️ setTokenCookie: 手动设置autograph cookie，可能覆盖后端设置')
  setCookie(CookieTokenKey, token)
  return localStorage.setItem(TokenKey, token)
}

// 移除Token（同时从Cookie和localStorage移除）
export function removeToken() {
  // 删除Cookie中的token
  deleteCookie(CookieTokenKey)

  // 删除localStorage中的token
  localStorage.removeItem(TokenKey)
  localStorage.removeItem(RefreshTokenKey)
}

// 获取刷新Token
export function getRefreshToken() {
  return localStorage.getItem(RefreshTokenKey)
}

// 设置刷新Token
export function setRefreshToken(refreshToken) {
  return localStorage.setItem(RefreshTokenKey, refreshToken)
}

// 检查是否已登录 - 优先检查Cookie中的autograph
export function isLoggedIn() {
  // 优先检查Cookie中的autograph
  const cookieToken = getCookie(CookieTokenKey)
  if (cookieToken && cookieToken !== 'undefined') {
    return true
  }

  // 如果Cookie中没有有效token，检查localStorage
  const localToken = localStorage.getItem(TokenKey)
  return !!localToken && localToken !== 'undefined'
}

// 检查Token是否过期
export function isTokenExpired(token) {
  if (!token) return true
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    return payload.exp < currentTime
  } catch (error) {
    return true
  }
}

// 获取Token信息
export function getTokenInfo(token) {
  if (!token) return null
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return {
      userId: payload.sub,
      username: payload.username,
      roles: payload.roles || [],
      exp: payload.exp,
      iat: payload.iat
    }
  } catch (error) {
    return null
  }
}
