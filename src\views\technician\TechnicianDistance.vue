<!--
  师傅接单范围页面
 /src/view/technician/distance.vue重构
-->

<template>
  <div class="page">
    <TopNav />
    <div class="page-main">
      <div class="distance-setting">
        <div class="setting-item">
          <label class="setting-label">师傅接单距离：</label>
          <el-input-number
            v-model="distance"
            placeholder="请输入接单距离"
            :min="1"
            :max="100"
            style="width: 200px;"
          />
          <span class="unit">km</span>
        </div>

        <div class="setting-actions">
          <LbButton
            type="primary"
            @click="save"
            :loading="saveLoading"
            size="default"
          >
            保存设置
          </LbButton>
          <LbButton
            @click="reset"
            style="margin-left: 12px;"
            size="default"
          >
            重置
          </LbButton>
        </div>
      </div>

      <!-- 距离设置说明 -->
      <div class="setting-description">
        <h3>设置说明</h3>
        <ul>
          <li>师傅接单距离是指师傅可以接收订单的最大距离范围</li>
          <li>距离以师傅当前位置为中心，向四周扩散的半径距离</li>
          <li>设置范围：1-100公里</li>
          <li>建议根据实际业务需求和师傅分布情况合理设置</li>
          <li>距离过小可能导致师傅接单困难，距离过大可能影响服务质量</li>
        </ul>
      </div>

      <!-- 师傅距离统计 -->
      <div class="distance-statistics">
        <h3>师傅分布统计</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.total }}</div>
              <div class="stat-label">总师傅数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.within_5km }}</div>
              <div class="stat-label">5km内师傅</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.within_10km }}</div>
              <div class="stat-label">10km内师傅</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.beyond_10km }}</div>
              <div class="stat-label">10km外师傅</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 区域管理 -->
      <div class="area-management">
        <h3>区域管理</h3>
        <div class="area-actions">
          <LbButton type="primary" @click="showAreaDialog = true">
            新增服务区域
          </LbButton>
        </div>

        <el-table
          v-loading="areaLoading"
          :data="areaList"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%; margin-top: 16px;"
        >
          <el-table-column prop="id" label="区域ID" width="80" />
          <el-table-column prop="name" label="区域名称" width="150" />
          <el-table-column prop="center_address" label="中心地址" min-width="200" />
          <el-table-column prop="radius" label="服务半径" width="120">
            <template #default="scope">
              {{ scope.row.radius }}km
            </template>
          </el-table-column>
          <el-table-column prop="technician_count" label="师傅数量" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="170" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <LbButton size="mini" type="primary" @click="editArea(scope.row)">
                编辑
              </LbButton>
              <LbButton
                size="mini"
                :type="scope.row.status === 1 ? 'warning' : 'success'"
                @click="toggleAreaStatus(scope.row)"
              >
                {{ scope.row.status === 1 ? '禁用' : '启用' }}
              </LbButton>
              <LbButton size="mini" type="danger" @click="deleteArea(scope.row)">
                删除
              </LbButton>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增/编辑区域对话框 -->
      <el-dialog
        :title="areaForm.id ? '编辑服务区域' : '新增服务区域'"
        v-model="showAreaDialog"
        width="600px"
      >
        <el-form
          :model="areaForm"
          ref="areaFormRef"
          :rules="areaRules"
          label-width="120px"
        >
          <el-form-item label="区域名称" prop="name">
            <el-input v-model="areaForm.name" placeholder="请输入区域名称" />
          </el-form-item>
          <el-form-item label="中心地址" prop="center_address">
            <el-input v-model="areaForm.center_address" placeholder="请输入中心地址" />
          </el-form-item>
          <el-form-item label="服务半径" prop="radius">
            <el-input-number
              v-model="areaForm.radius"
              :min="1"
              :max="50"
              placeholder="请输入服务半径"
              style="width: 100%;"
            />
            <span style="margin-left: 8px;">km</span>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="areaForm.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <LbButton @click="showAreaDialog = false">取消</LbButton>
            <LbButton type="primary" @click="saveArea" :loading="areaSubmitLoading">
              确定
            </LbButton>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const distance = ref(1)
const saveLoading = ref(false)
const areaLoading = ref(false)
const areaSubmitLoading = ref(false)
const showAreaDialog = ref(false)
const areaFormRef = ref()
const areaList = ref([])

// 统计数据
const statistics = reactive({
  total: 0,
  within_5km: 0,
  within_10km: 0,
  beyond_10km: 0
})

// 区域表单
const areaForm = reactive({
  id: null,
  name: '',
  center_address: '',
  radius: 5,
  status: 1
})

// 区域表单验证规则
const areaRules = {
  name: [
    { required: true, message: '请输入区域名称', trigger: 'blur' }
  ],
  center_address: [
    { required: true, message: '请输入中心地址', trigger: 'blur' }
  ],
  radius: [
    { required: true, message: '请输入服务半径', trigger: 'blur' },
    { type: 'number', min: 1, max: 50, message: '服务半径必须在1-50km之间', trigger: 'blur' }
  ]
}

// 方法
const save = async () => {
  if (distance.value === '' || distance.value < 1) {
    return ElMessage.error('请填写正确的距离')
  }

  saveLoading.value = true
  try {
    const response = await fetch('/api/technician/distance/config', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ distance: distance.value })
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(result.meg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const reset = () => {
  distance.value = 1
}

const getConfigInfo = async () => {
  try {
    const response = await fetch('/api/technician/distance/config')
    const result = await response.json()

    if (result.code === 200) {
      distance.value = result.data.distance || 1
    } else {
      ElMessage.error(result.meg || '获取配置失败')
    }
  } catch (error) {
    console.error('获取配置失败:', error)
    ElMessage.error('获取配置失败')
  }
}

const getStatistics = async () => {
  try {
    const response = await fetch('/api/technician/distance/statistics')
    const result = await response.json()

    if (result.code === 200) {
      Object.assign(statistics, result.data)
    } else {
      ElMessage.error(result.meg || '获取统计数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

const getAreaList = async () => {
  areaLoading.value = true
  try {
    const response = await fetch('/api/technician/area/list')
    const result = await response.json()

    if (result.code === 200) {
      areaList.value = result.data.list || []
    } else {
      ElMessage.error(result.meg || '获取区域列表失败')
    }
  } catch (error) {
    console.error('获取区域列表失败:', error)
    ElMessage.error('获取区域列表失败')
  } finally {
    areaLoading.value = false
  }
}

const editArea = (area) => {
  Object.assign(areaForm, area)
  showAreaDialog.value = true
}

const saveArea = async () => {
  try {
    await areaFormRef.value.validate()

    areaSubmitLoading.value = true

    const url = areaForm.id ? `/api/technician/area/update/${areaForm.id}` : '/api/technician/area/add'
    const method = areaForm.id ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(areaForm)
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success(areaForm.id ? '更新成功' : '新增成功')
      showAreaDialog.value = false
      resetAreaForm()
      getAreaList()
    } else {
      ElMessage.error(result.meg || '操作失败')
    }
  } catch (error) {
    console.error('保存区域失败:', error)
    ElMessage.error('操作失败')
  } finally {
    areaSubmitLoading.value = false
  }
}

const toggleAreaStatus = async (area) => {
  try {
    const newStatus = area.status === 1 ? 0 : 1
    const action = newStatus === 1 ? '启用' : '禁用'

    await ElMessageBox.confirm(
      `确定要${action}区域 "${area.name}" 吗？`,
      '状态切换确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/technician/area/toggle/${area.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status: newStatus })
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success(`${action}成功`)
      area.status = newStatus
    } else {
      ElMessage.error(result.meg || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态切换失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const deleteArea = async (area) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除区域 "${area.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/technician/area/delete/${area.id}`, {
      method: 'DELETE'
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('删除成功')
      getAreaList()
    } else {
      ElMessage.error(result.meg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const resetAreaForm = () => {
  areaForm.id = null
  areaForm.name = ''
  areaForm.center_address = ''
  areaForm.radius = 5
  areaForm.status = 1
}

// 生命周期
onMounted(() => {
  getConfigInfo()
  getStatistics()
  getAreaList()
})
</script>

<style scoped>
.page {
  padding: 50px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  max-width: 1200px;
  margin: 0 auto;
}

.distance-setting {
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.setting-label {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-right: 16px;
  min-width: 140px;
}

.unit {
  margin-left: 8px;
  color: #606266;
  font-size: 14px;
}

.setting-actions {
  margin-top: 20px;
}

.setting-description {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.setting-description h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.setting-description ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  line-height: 1.6;
}

.setting-description li {
  margin-bottom: 8px;
}

.distance-statistics {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.distance-statistics h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.stat-card {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.area-management {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.area-management h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.area-actions {
  margin-bottom: 16px;
}

.el-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .page {
    padding: 20px;
  }

  .distance-setting,
  .setting-description,
  .distance-statistics,
  .area-management {
    padding: 15px;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .setting-label {
    margin-bottom: 8px;
    margin-right: 0;
  }

  .el-col {
    margin-bottom: 16px;
  }

  .stat-number {
    font-size: 24px;
  }
}
</style>
