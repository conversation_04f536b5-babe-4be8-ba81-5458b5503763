# 今师傅 Final

<div align="center">
  <img src="./public/logo.jpg" alt="今师傅" width="120" height="120">

  <h1>今师傅</h1>
  <p>基于 Vue3 + Element Plus 的现代化后台管理系统</p>

  [![Vue](https://img.shields.io/badge/Vue-3.5.17-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
  [![Element Plus](https://img.shields.io/badge/Element%20Plus-2.10.4-409EFF?style=flat-square&logo=element)](https://element-plus.org/)
  [![Vite](https://img.shields.io/badge/Vite-7.0.0-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
  [![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](./LICENSE)
</div>

---

## 🎯 项目概述

今师傅 ，严格遵循**布局守恒、数据独立、代码原创、技术栈统一**四大核心原则，使用 Vue3 + Element Plus + Mock.js 技术栈构建的企业级后台管理系统。

### ✨ 核心特性

- 🚀 **Vue3 Composition API** - 使用最新的 Vue3 语法和特性
- 🎨 **Element Plus** - 现代化的 UI 组件库
- 📊 **完整Mock系统** - 166+ API接口，支持离线开发
- 🔐 **权限管理** - RBAC权限系统，精细化权限控制
- 📱 **响应式设计** - 支持多端适配
- 🛠️ **TypeScript支持** - 可选的类型安全
- 🔧 **开发工具** - ESLint + Prettier + Playwright

### 📊 项目规模

| 统计项 | 数量 | 说明 |
|--------|------|------|
| 功能模块 | 9个 | 服务项目、师傅管理、营销管理等 |
| 页面总数 | 80+ | 包含列表、详情、编辑页面 |
| API接口 | 166+ | 完整的Mock API接口 |
| 组件数量 | 50+ | 可复用组件 |
| 代码行数 | 20,000+ | 高质量代码实现 |

---

## 🏗️ 技术栈

### 核心框架
- **Vue 3.5.17** - 渐进式JavaScript框架
- **Vue Router 4.2.0** - 官方路由管理器
- **Vuex 4.1.0** - 状态管理模式
- **Element Plus 2.10.4** - Vue3组件库

### 构建工具
- **Vite 7.0.0** - 下一代前端构建工具
- **ESLint 8.57.0** - 代码质量检查
- **Prettier 3.1.0** - 代码格式化
- **Playwright 1.54.1** - E2E测试框架

### 开发依赖
- **Mock.js 1.1.0** - 数据模拟
- **Axios 1.6.0** - HTTP客户端
- **NProgress 0.2.0** - 进度条
- **Quill 2.0.3** - 富文本编辑器

---

## 🚀 快速开始

### 环境要求

- **Node.js** >= 16.0.0
- **npm** >= 8.0.0
- **现代浏览器** (Chrome 90+, Firefox 88+, Safari 14+)

### 安装和运行

```bash
# 克隆项目
git clone <repository-url>
cd admin_system_v3_final

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 代码检查
npm run lint

# 代码格式化
npm run format
```

### 访问地址

- **开发环境**: http://localhost:3000
- **生产预览**: http://localhost:4173 (运行 `npm run preview`)

---

## 📁 项目结构

```
admin_system_v3_final/
├── docs/                    # 📚 项目文档
│   ├── DEVELOPMENT_GUIDE.md # 开发指南
│   ├── API_INTEGRATION_GUIDE.md # API对接文档
│   ├── FEATURE_MODULES.md   # 功能模块总结
│   ├── NEW_FEATURE_GUIDE.md # 新功能开发指南
│   └── DEPLOYMENT_GUIDE.md  # 部署指南
├── public/                  # 🌐 静态资源
├── src/                     # 💻 源代码
│   ├── api/                 # 🔌 API接口层
│   ├── assets/              # 🎨 静态资源
│   ├── components/          # 🧩 组件
│   │   ├── common/          # 通用组件
│   │   ├── layout/          # 布局组件
│   │   └── icons/           # 图标组件
│   ├── config/              # ⚙️ 配置文件
│   ├── mock/                # 🎭 Mock数据
│   ├── router/              # 🛣️ 路由配置
│   ├── store/               # 🗄️ 状态管理
│   ├── styles/              # 💄 样式文件
│   ├── utils/               # 🔧 工具函数
│   ├── views/               # 📄 页面组件
│   │   ├── service/         # 服务项目模块
│   │   ├── technician/      # 师傅管理模块
│   │   ├── market/          # 营销管理模块
│   │   ├── shop/            # 订单管理模块
│   │   ├── distribution/    # 分销管理模块
│   │   ├── finance/         # 财务管理模块
│   │   ├── user/            # 用户管理模块
│   │   ├── account/         # 账号设置模块
│   │   └── system/          # 系统设置模块
│   ├── App.vue              # 根组件
│   └── main.js              # 应用入口
├── tests/                   # 🧪 测试文件
├── vite-mock-plugin.js      # 🎭 Mock插件
├── vite.config.js           # ⚡ Vite配置
└── package.json             # 📦 项目配置
```

---

## 🎨 功能模块

### 1. 服务项目管理 📋
- 服务列表管理
- 轮播图配置
- 分类树形管理
- 金刚区设置
- 服务点管理
- 项目配置

### 2. 师傅管理 👨‍🔧
- 师傅档案管理
- 等级体系配置
- 技能标签系统
- 押金管理
- 接单距离配置
- 服务区域管理

### 3. 营销管理 🎯
- 卡券管理
- 活动配置
- 通知推送

### 4. 订单管理 🛒
- 订单查询处理
- 退款管理
- 佣金结算
- 售后服务

### 5. 分销管理 👥
- 分销商审核
- 分销关系管理
- 佣金配置

### 6. 财务管理 💰
- 财务数据统计
- 提现审核
- 储值管理

### 7. 用户管理 👤
- 用户档案管理
- 评价审核

### 8. 账号设置 ⚙️
- 管理员管理
- 角色权限配置
- 菜单管理
- 代理商管理
- 第三方服务

### 9. 系统设置 🔧
- 版本管理
- 微信生态配置
- 支付配置
- 业务规则设置

---

## 📖 开发文档

详细的开发文档请查看 `docs/` 目录：

- **[开发指南](./docs/DEVELOPMENT_GUIDE.md)** - 项目概述和开发规范
- **[API对接文档](./docs/API_INTEGRATION_GUIDE.md)** - Mock API到真实API的迁移
- **[功能模块总结](./docs/FEATURE_MODULES.md)** - 各功能模块详细说明
- **[新功能开发指南](./docs/NEW_FEATURE_GUIDE.md)** - 如何添加新功能
- **[部署指南](./docs/DEPLOYMENT_GUIDE.md)** - 生产环境部署和维护

---

## 🧪 测试

```bash
# 运行E2E测试
npm run test

# 运行特定测试
npx playwright test tests/mock-api.spec.js

# 生成测试报告
npx playwright show-report
```

---

## 🤝 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发规范

- 使用 Composition API 语法
- 遵循 ESLint 代码规范
- 编写清晰的注释
- 提交前运行测试

---

## 📄 许可证

本项目采用 [MIT](./LICENSE) 许可证。

---

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 📧 **邮箱**: <EMAIL>
- 🐛 **Bug报告**: [GitHub Issues](https://github.com/yourorg/admin-system/issues)
- 📖 **文档**: [在线文档](https://docs.yourdomain.com)
- 💬 **讨论**: [GitHub Discussions](https://github.com/yourorg/admin-system/discussions)

---

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

<div align="center">
  <p>⭐ 如果这个项目对你有帮助，请给它一个星标！</p>
  <p>Made with ❤️ by Development Team</p>
</div>
