<!--
  角色管理页面
  根据API接口文档重构，支持7个接口的完整功能
-->

<template>
  <div class="lb-account-role">
    <TopNav />
    <div class="page-main">
      <!-- 搜索区域 -->
      <el-card class="search-card" shadow="never">
        <el-form :model="searchForm" inline>
          <el-form-item label="角色名称">
            <el-input
              v-model="searchForm.roleName"
              placeholder="请输入角色名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <LbButton type="primary" @click="handleSearch">搜索</LbButton>
            <LbButton @click="handleReset">重置</LbButton>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 操作按钮 -->
      <el-row class="page-header">
        <LbButton type="primary" @click="handleAdd">新增角色</LbButton>
      </el-row>
      
      <!-- 数据表格 -->
      <el-card class="table-card" shadow="never">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
          border
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="roleName" label="角色名称" width="150" />
          <el-table-column prop="rules" label="角色权限" min-width="300">
            <template #default="scope">
              <el-tag
                v-for="permission in getRolePermissions(scope.row.rules)"
                :key="permission.id"
                size="small"
                style="margin-right: 5px; margin-bottom: 5px;"
              >
                {{ permission.menuName }}
              </el-tag>
              <span v-if="!scope.row.rules || scope.row.rules === ''" class="text-muted">
                暂无权限
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="170">
            <template #default="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="修改时间" width="170">
            <template #default="scope">
              {{ formatDateTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="mini"
                  type="primary"
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </LbButton>
                <LbButton
                  size="mini"
                  type="danger"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="type === 'add' ? '新增角色' : '编辑角色'"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="选择权限" prop="ruleIds">
          <el-checkbox-group v-model="form.ruleIds" class="permission-group">
            <el-checkbox
              v-for="menu in menuList"
              :key="menu.id"
              :value="menu.id"
              class="permission-item"
            >
              {{ menu.menuName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <LbButton @click="handleClose">取消</LbButton>
        <LbButton type="primary" @click="handleConfirm" :loading="submitLoading">确定</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const type = ref('add')
const formRef = ref()
const submitLoading = ref(false)
const menuList = ref([])

// 搜索表单
const searchForm = reactive({
  roleName: ''
})

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  totalCount: 0,
  totalPage: 0
})

// 表单数据
const form = reactive({
  id: '',
  roleName: '',
  ruleIds: []
})

// 表单验证规则
const formRules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  ruleIds: [
    { required: true, message: '请选择角色权限', trigger: 'change' }
  ]
}

// 方法
const getRoleList = async (pageNum = 1) => {
  loading.value = true
  pagination.pageNum = pageNum

  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }

    // 如果有搜索条件，添加到参数中
    if (searchForm.roleName) {
      params.roleName = searchForm.roleName
    }

    const response = await api.role.getRoleList(params)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      pagination.totalCount = response.data.totalCount || 0
      pagination.totalPage = response.data.totalPage || 0
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const getMenuList = async () => {
  try {
    const response = await api.role.getMenuList()

    if (response.code === '200') {
      menuList.value = response.data || []
    }
  } catch (error) {
    console.error('获取菜单列表失败:', error)
  }
}

// 根据rules字符串获取权限名称列表
const getRolePermissions = (rulesStr) => {
  if (!rulesStr || rulesStr === '') return []

  const ruleIds = rulesStr.split(',').map(id => parseInt(id.trim()))
  return menuList.value.filter(menu => ruleIds.includes(menu.id))
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  return dateTimeStr.replace('T', ' ').substring(0, 19)
}

// 搜索处理
const handleSearch = () => {
  getRoleList(1)
}

// 重置搜索
const handleReset = () => {
  searchForm.roleName = ''
  getRoleList(1)
}

const handleAdd = () => {
  type.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  type.value = 'edit'

  try {
    // 获取角色详情
    const response = await api.role.getRoleDetail(row.id)

    if (response.code === '200') {
      Object.assign(form, {
        id: response.data.id,
        roleName: response.data.roleName,
        ruleIds: response.data.ruleIds || []
      })
      dialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取角色详情失败')
    }
  } catch (error) {
    console.error('获取角色详情失败:', error)
    ElMessage.error('获取角色详情失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.roleName}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.role.deleteRole(row.id)

    if (response.code === '200') {
      ElMessage.success('删除成功')
      getRoleList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleConfirm = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    const requestData = {
      roleName: form.roleName,
      ruleIds: form.ruleIds
    }

    // 如果是编辑，添加id
    if (type.value === 'edit') {
      requestData.id = form.id
    }

    const response = type.value === 'add'
      ? await api.role.addRole(requestData)
      : await api.role.editRole(requestData)

    if (response.code === '200') {
      ElMessage.success(type.value === 'add' ? '新增成功' : '编辑成功')
      dialogVisible.value = false
      getRoleList()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    id: '',
    roleName: '',
    ruleIds: []
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getRoleList(1)
}

const handleCurrentChange = (page) => {
  getRoleList(page)
}

// 生命周期
onMounted(() => {
  getRoleList()
  getMenuList()
})
</script>

<style scoped>
.lb-account-role {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.search-card {
  margin-bottom: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.permission-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.permission-item {
  margin: 0;
  white-space: nowrap;
}

.text-muted {
  color: #999;
  font-style: italic;
}

@media (max-width: 768px) {
  .lb-account-role {
    padding: 10px;
  }

  .table-operate {
    flex-direction: column;
  }

  .pagination-section {
    text-align: center;
  }

  .permission-group {
    flex-direction: column;
  }
}
</style>
