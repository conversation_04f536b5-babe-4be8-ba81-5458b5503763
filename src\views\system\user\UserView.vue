<!--
  用户管理模块
   用户管理功能的Vue3重构版本

  功能：
  - 用户列表展示
  - 用户搜索和筛选
  - 用户新增、编辑、删除
  - 用户状态管理
  - 角色分配
-->

<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>用户管理</h1>
      <p class="page-subtitle">管理系统用户账户和权限</p>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :model="searchForm" :inline="true" size="default">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input
            v-model="searchForm.email"
            placeholder="请输入邮箱"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色">
          <el-select
            v-model="searchForm.role"
            placeholder="请选择角色"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="role in roleOptions"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
        <el-button
          type="danger"
          :disabled="!selectedUsers.length"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-tooltip content="刷新数据">
          <el-button circle @click="loadUserList">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="列设置">
          <el-button circle @click="showColumnSettings = true">
            <el-icon><Setting /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 用户列表表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          sortable="custom"
        />
        <el-table-column
          prop="avatar"
          label="头像"
          width="80"
          align="center"
        >
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column
          prop="username"
          label="用户名"
          min-width="120"
          sortable="custom"
          show-overflow-tooltip
        />
        <el-table-column
          prop="nickname"
          label="昵称"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="email"
          label="邮箱"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="phone"
          label="手机号"
          width="130"
          show-overflow-tooltip
        />
        <el-table-column
          prop="roles"
          label="角色"
          width="150"
        >
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role"
              size="small"
              style="margin-right: 4px"
            >
              {{ getRoleLabel(role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="状态"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="lastLoginTime"
          label="最后登录"
          width="160"
          sortable="custom"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="160"
          sortable="custom"
        />
        <el-table-column
          label="操作"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleResetPassword(row)"
            >
              重置密码
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          共 {{ total }} 条记录，第 {{ currentPage }} / {{ Math.ceil(total / pageSize) }} 页
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="userForm.username"
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickname">
              <el-input
                v-model="userForm.nickname"
                placeholder="请输入昵称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="userForm.email"
                placeholder="请输入邮箱"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="userForm.phone"
                placeholder="请输入手机号"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="!isEdit">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="userForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="userForm.confirmPassword"
                type="password"
                placeholder="请确认密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="角色" prop="roles">
          <el-select
            v-model="userForm.roles"
            multiple
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in roleOptions"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="userForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Delete,
  Download,
  Setting,
  User
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const showColumnSettings = ref(false)
const userFormRef = ref()

// 搜索表单
const searchForm = reactive({
  username: '',
  email: '',
  status: '',
  role: ''
})

// 用户表单
const userForm = reactive({
  id: null,
  username: '',
  nickname: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  roles: [],
  status: 1,
  remark: ''
})

// 表格数据
const userList = ref([])
const selectedUsers = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 角色选项
const roleOptions = ref([
  { label: '超级管理员', value: 'super_admin' },
  { label: '管理员', value: 'admin' },
  { label: '编辑员', value: 'editor' },
  { label: '普通用户', value: 'user' }
])

// 计算属性
const isEdit = computed(() => !!userForm.id)
const dialogTitle = computed(() => isEdit.value ? '编辑用户' : '新增用户')

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  roles: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// Mock数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    nickname: '超级管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
    roles: ['super_admin'],
    status: 1,
    lastLoginTime: '2025-01-11 10:30:00',
    createTime: '2025-01-01 00:00:00',
    remark: '系统超级管理员'
  },
  {
    id: 2,
    username: 'editor',
    nickname: '编辑员',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
    roles: ['editor'],
    status: 1,
    lastLoginTime: '2025-01-11 09:15:00',
    createTime: '2025-01-02 00:00:00',
    remark: '内容编辑员'
  },
  {
    id: 3,
    username: 'user001',
    nickname: '普通用户',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
    roles: ['user'],
    status: 0,
    lastLoginTime: '2025-01-10 16:20:00',
    createTime: '2025-01-03 00:00:00',
    remark: '普通用户账户'
  }
]

// 方法
const loadUserList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 应用搜索过滤
    let filteredUsers = [...mockUsers]

    if (searchForm.username) {
      filteredUsers = filteredUsers.filter(user =>
        user.username.includes(searchForm.username)
      )
    }

    if (searchForm.email) {
      filteredUsers = filteredUsers.filter(user =>
        user.email.includes(searchForm.email)
      )
    }

    if (searchForm.status !== '') {
      filteredUsers = filteredUsers.filter(user =>
        user.status === searchForm.status
      )
    }

    if (searchForm.role) {
      filteredUsers = filteredUsers.filter(user =>
        user.roles.includes(searchForm.role)
      )
    }

    total.value = filteredUsers.length

    // 分页处理
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    userList.value = filteredUsers.slice(start, end)

  } catch (error) {
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadUserList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    email: '',
    status: '',
    role: ''
  })
  handleSearch()
}

const handleAdd = () => {
  resetUserForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  Object.assign(userForm, {
    ...row,
    password: '',
    confirmPassword: ''
  })
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟删除API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    ElMessage.success('删除成功')
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (!selectedUsers.value.length) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟批量删除API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('批量删除成功')
    selectedUsers.value = []
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleStatusChange = async (row) => {
  try {
    // 模拟状态更新API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    ElMessage.success(`用户状态已${row.status ? '启用' : '禁用'}`)
  } catch (error) {
    // 恢复原状态
    row.status = row.status ? 0 : 1
    ElMessage.error('状态更新失败')
  }
}

const handleResetPassword = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 "${row.username}" 的密码吗？`,
      '重置密码确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟重置密码API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('密码重置失败')
    }
  }
}

const handleSubmit = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()

    submitLoading.value = true

    // 模拟提交API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    loadUserList()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleSortChange = ({ prop, order }) => {
  // 实现排序逻辑
  console.log('排序:', prop, order)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadUserList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadUserList()
}

const getRoleLabel = (roleValue) => {
  const role = roleOptions.value.find(r => r.value === roleValue)
  return role ? role.label : roleValue
}

const resetUserForm = () => {
  Object.assign(userForm, {
    id: null,
    username: '',
    nickname: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    roles: [],
    status: 1,
    remark: ''
  })

  if (userFormRef.value) {
    userFormRef.value.clearValidate()
  }
}

// 生命周期
onMounted(() => {
  loadUserList()
})
</script>

<style scoped>
.user-management {
  padding: var(--spacing-lg);
}

.page-title {
  margin-bottom: var(--spacing-xl);
}

.page-title h1 {
  font-size: var(--font-size-xxl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.search-form {
  background: var(--bg-color-base);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  margin-bottom: var(--spacing-lg);
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-sm);
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
}

.table-container {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
}

.pagination-info {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Element Plus 表格样式覆盖 */
:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background-color: var(--table-header-bg);
  color: var(--color-text-primary);
  font-weight: 500;
}

:deep(.el-table tr:hover > td) {
  background-color: var(--table-row-hover-bg);
}

:deep(.el-table .el-table__cell) {
  border-bottom: 1px solid var(--table-border-color);
}

/* 对话框样式 */
:deep(.el-dialog__header) {
  background-color: var(--bg-color-column);
  border-bottom: 1px solid var(--border-color-light);
}

:deep(.el-dialog__body) {
  padding: var(--spacing-xl);
}

:deep(.el-dialog__footer) {
  background-color: var(--bg-color-column);
  border-top: 1px solid var(--border-color-light);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .user-management {
    padding: var(--spacing-md);
  }

  .search-form :deep(.el-form--inline .el-form-item) {
    display: block;
    margin-bottom: var(--spacing-md);
  }

  .toolbar {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .pagination-container {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  :deep(.el-table .el-table__cell) {
    padding: var(--spacing-sm);
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .page-title h1 {
    font-size: var(--font-size-xl);
  }

  .toolbar-left,
  .toolbar-right {
    flex-direction: column;
  }

  :deep(.el-table .el-table__cell:nth-child(n+6)) {
    display: none;
  }
}
</style>
