/**
 * 用户管理模块Mock数据
 */

import Mock from 'mockjs'
import { successResponse, createCrudMock } from '../utils.js'

const customList = Mock.mock({
  'list|50-150': [{
    'id|+1': 1,
    'name': '@cname',
    'phone': '@phone',
    'avatar': '@image("100x100")',
    'gender|1': [1, 2],
    'balance|0-1000.2': 1,
    'orderCount|0-50': 1,
    'status|1': [0, 1],
    'createTime': '@datetime'
  }]
}).list

createCrudMock('/api/custom', customList)

console.log('用户管理模块Mock数据已加载')
export { customList }
