/**
 * 今师傅 - 样式入口文件

 * 
 * 导入顺序：
 * 1. CSS变量系统
 * 2. 重置样式
 * 3. 基础样式
 * 4. 组件样式
 * 5. 工具样式
 * 6. Element Plus样式覆盖
 */

/* ===== CSS变量系统 ===== */
@import './variables.css';

/* ===== 重置样式 ===== */
@import './reset.css';

/* ===== 基础样式 ===== */
@import './base.css';

/* ===== 组件样式 ===== */
@import './components.css';

/* ===== 工具样式 ===== */
@import './utilities.css';

/* ===== Element Plus 样式覆盖 ===== */
@import './element-plus-overrides.css';

/* ===== 全局样式补充 ===== */

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color-base);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-color-dark);
}

/* Firefox滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color-base) var(--bg-color-base);
}

/* 清除浮动 */
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

/* 图片适配 */
.img-responsive {
  max-width: 100%;
  height: auto;
}

.img-cover {
  object-fit: cover;
  object-position: center;
}

.img-contain {
  object-fit: contain;
  object-position: center;
}

/* 统计卡片样式 */
.stat-card {
  background-color: var(--bg-color-base);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-lg);
  padding: var(--stat-card-padding);
  text-align: center;
  transition: var(--transition-box-shadow);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  box-shadow: var(--box-shadow-base);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light-2));
}

.stat-value {
  font-size: var(--stat-value-font-size);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1;
}

.stat-label {
  font-size: var(--stat-label-font-size);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.stat-change {
  font-size: var(--stat-change-font-size);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.stat-change.positive {
  color: var(--color-success);
}

.stat-change.negative {
  color: var(--color-danger);
}

.stat-icon {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  font-size: var(--stat-icon-size);
  color: var(--color-primary);
  opacity: 0.1;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
}

.pagination-info {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Element Plus 样式覆盖已移至 element-plus-overrides.css */

.el-button--primary:hover {
  background-color: var(--color-primary-dark-1);
  border-color: var(--color-primary-dark-1);
}

.el-input__wrapper {
  border-radius: var(--input-border-radius);
  transition: var(--transition-border);
}

.el-input__wrapper:hover {
  border-color: var(--color-primary-light-1);
}

.el-input__wrapper.is-focus {
  border-color: var(--input-focus-border-color);
  box-shadow: 0 0 0 2px rgba(96, 155, 235, 0.2);
}

.el-card {
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  transition: var(--transition-box-shadow);
}

.el-card:hover {
  box-shadow: var(--box-shadow-base);
}

.el-table {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.el-table th {
  background-color: var(--table-header-bg);
  color: var(--color-text-primary);
  font-weight: 500;
}

.el-table tr:hover > td {
  background-color: var(--table-row-hover-bg);
}

.el-pagination {
  justify-content: center;
}

.el-pagination .el-pager li {
  border-radius: var(--pagination-border-radius);
  transition: var(--transition-base);
}

.el-pagination .el-pager li:hover {
  background-color: var(--pagination-hover-bg);
}

.el-pagination .el-pager li.is-active {
  background-color: var(--pagination-active-bg);
  color: #ffffff;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .stat-card {
    padding: var(--spacing-md);
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .stat-icon {
    font-size: 32px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
  
  .el-button {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }
  
  .el-input {
    margin-bottom: var(--spacing-sm);
  }
}
