<!--
  LayoutHeader 组件

  
  功能：
  - 顶部导航栏，固定高度60px
  - 侧边栏折叠控制
  - 用户信息显示
  - 消息通知
  - 账户管理菜单
-->

<template>
  <header class="layout-header">
    <div class="header-left">
      <!-- 侧边栏折叠按钮 -->
      <div class="sidebar-toggle" @click="toggleSidebar">
        <el-icon :size="18">
          <Fold v-if="!sidebarCollapsed" />
          <Expand v-else />
        </el-icon>
      </div>
      
      <!-- Logo和标题 -->
      <div class="header-logo">
         <el-avatar :size="32" :src="'/logo.jpg'"></el-avatar>
        <!-- <div class="logo-placeholder">
          <el-icon :size="32"><Setting /></el-icon>
        </div> -->
        <span class="logo-text">今师傅管理后台</span>
      </div>
    </div>

    <div class="header-center">
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="header-breadcrumb">
        <el-breadcrumb-item 
          v-for="item in breadcrumbList" 
          :key="item.path"
          :to="item.path"
        >
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="header-right">
      <!-- 搜索框 -->
      <!-- <div class="header-search">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索菜单..."
          :prefix-icon="Search"
          size="small"
          style="width: 200px"
          @keyup.enter="handleSearch"
        />
      </div> -->

      <!-- 全屏切换 -->
      <div class="header-action" @click="toggleFullscreen">
        <el-icon :size="18">
          <FullScreen v-if="!isFullscreen" />
          <Aim v-else />
        </el-icon>
      </div>

      <!-- 消息通知 -->
      <!-- <div class="header-action">
        <el-badge :value="messageCount" :hidden="messageCount === 0">
          <el-icon :size="18">
            <Bell />
          </el-icon>
        </el-badge>
      </div> -->

      <!-- 主题切换 -->
      <!-- <div class="header-action" @click="toggleTheme">
        <el-icon :size="18">
          <Sunny v-if="currentTheme === 'light'" />
          <Moon v-else />
        </el-icon>
      </div> -->

      <!-- 用户信息 -->
      <el-dropdown class="user-dropdown" @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="32" src="/logo.jpg">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="user-name">{{ displayUserName }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="editUser">
              <el-icon><Edit /></el-icon>
              编辑用户
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>

  <!-- 编辑用户对话框 -->
  <el-dialog
    v-model="editUserDialogVisible"
    title="编辑用户"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="editUserFormRef"
      :model="editUserForm"
      :rules="editUserRules"
      label-width="100px"
    >
      <el-form-item label="用户名">
        <el-input
          v-model="editUserForm.username"
          disabled
          placeholder="管理员账号名不能修改"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model="editUserForm.newPassword"
          type="password"
          placeholder="请输入新密码"
          show-password
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="editUserForm.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          show-password
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="editUserDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpdatePassword" :loading="updatePasswordLoading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import CryptoJS from 'crypto-js'
import api from '@/api-v2'
import {
  Fold,
  Expand,
  Search,
  FullScreen,
  Aim,
  Bell,
  Sunny,
  Moon,
  User,
  ArrowDown,
  Edit,
  SwitchButton
} from '@element-plus/icons-vue'

const store = useStore()
const route = useRoute()
const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const isFullscreen = ref(false)

// 编辑用户相关
const editUserDialogVisible = ref(false)
const editUserFormRef = ref(null)
const updatePasswordLoading = ref(false)
const editUserForm = ref({
  username: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const editUserRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== editUserForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}
const messageCount = ref(5)

// 计算属性
const sidebarCollapsed = computed(() => store.getters['ui/sidebarCollapsed'])
const currentTheme = computed(() => store.getters['ui/theme'])
const userInfo = computed(() => store.getters['auth/userInfo'])
const breadcrumbList = computed(() => store.getters['ui/breadcrumbList'])

// 显示用户名 - 优先使用角色接口返回的用户名
const displayUserName = computed(() => {
  const user = userInfo.value
  if (!user) return '用户'

  // 优先使用角色接口返回的用户名
  if (user.roleUserName) {
    return user.roleUserName
  }

  // 其次使用显示名称
  if (user.displayName) {
    return user.displayName
  }

  // 最后使用本地存储的用户名
  const storedUserName = localStorage.getItem('user_name')
  if (storedUserName) {
    return storedUserName
  }

  // 默认使用用户信息中的名称
  return user.name || user.username || '管理员'
})

// 方法
const toggleSidebar = () => {
  store.dispatch('ui/toggleSidebar')
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    // 实现搜索逻辑
    console.log('搜索:', searchKeyword.value)
  }
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const toggleTheme = () => {
  const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
  store.dispatch('ui/setTheme', newTheme)
}

// 显示编辑用户对话框
const showEditUserDialog = () => {
  // 重置表单
  editUserForm.value = {
    username: userInfo.value.name || 'admin',
    newPassword: '',
    confirmPassword: ''
  }
  editUserDialogVisible.value = true

  // 清除表单验证
  if (editUserFormRef.value) {
    editUserFormRef.value.clearValidate()
  }
}

// 处理密码更新
const handleUpdatePassword = async () => {
  if (!editUserFormRef.value) return

  try {
    // 表单验证
    await editUserFormRef.value.validate()

    updatePasswordLoading.value = true

    // MD5 加密密码
    const encryptedNewPassword = CryptoJS.MD5(editUserForm.value.newPassword).toString().toUpperCase()
    const encryptedConfirmPassword = CryptoJS.MD5(editUserForm.value.confirmPassword).toString().toUpperCase()

    console.log('🔐 开始更新密码:', {
      原始密码: editUserForm.value.newPassword,
      加密后密码: encryptedNewPassword
    })

    // 调用更新密码接口
    const result = await api.account.updatePassword({
      newPassword: encryptedNewPassword,
      confirmPassword: encryptedConfirmPassword
    })

    if (result.code === '200' || result.code === 200) {
      ElMessage.success('密码修改成功')
      editUserDialogVisible.value = false

      // 密码修改成功后，提示用户重新登录
      ElMessage.info('密码已修改，请重新登录')
      setTimeout(() => {
        handleUserCommand('logout')
      }, 1500)
    } else {
      ElMessage.error(result.msg || '密码修改失败')
    }
  } catch (error) {
    console.error('❌ 密码修改失败:', error)
    ElMessage.error('密码修改失败')
  } finally {
    updatePasswordLoading.value = false
  }
}

const handleUserCommand = async (command) => {
  switch (command) {
    case 'editUser':
      showEditUserDialog()
      break
    case 'logout':
      try {
        // 等待退出登录完成
        await store.dispatch('auth/logout')
        console.log('🚪 退出登录成功，跳转到登录页')
        // 确保跳转到登录页
        router.replace('/login')
      } catch (error) {
        console.error('🚪 退出登录失败:', error)
        // 即使失败也要跳转到登录页
        router.replace('/login')
      }
      break
    default:
      console.log('未知命令:', command)
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
.layout-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  padding: 0 var(--spacing-lg);
  background-color: var(--bg-color-base);
  border-bottom: 1px solid var(--border-color-light);
  box-shadow: var(--box-shadow-light);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-top);
  transition: var(--transition-base);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
  color: var(--color-text-secondary);
}

.sidebar-toggle:hover {
  background-color: var(--bg-color-column);
  color: var(--color-primary);
}

.header-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-placeholder {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--border-radius-sm);
}

.logo-text {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: #F60100;
}

.header-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 600px;
}

.header-breadcrumb {
  font-size: var(--font-size-sm);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-search {
  margin-right: var(--spacing-sm);
}

.header-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
  color: var(--color-text-secondary);
}

.header-action:hover {
  background-color: var(--bg-color-column);
  color: var(--color-primary);
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
}

.user-info:hover {
  background-color: var(--bg-color-column);
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-primary);
}

.dropdown-icon {
  font-size: 12px;
  color: var(--color-text-secondary);
  transition: var(--transition-base);
}

.user-dropdown:hover .dropdown-icon {
  color: var(--color-primary);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .header-center {
    display: none;
  }
  
  .header-search {
    display: none;
  }
  
  .logo-text {
    display: none;
  }
  
  .user-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 var(--spacing-md);
  }
  
  .header-right {
    gap: var(--spacing-sm);
  }
  
  .header-action {
    width: 32px;
    height: 32px;
  }
}
</style>
