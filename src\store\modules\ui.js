/**
 * UI状态管理模块
 * 
 * 功能：
 * - 侧边栏状态
 * - 主题切换
 * - 面包屑导航
 * - 访问历史
 * - 全局加载状态
 */

const state = {
  // 侧边栏是否折叠
  sidebarCollapsed: false,
  // 侧边栏子菜单是否展开
  sidebarSubmenuOpen: false,
  // 是否移动端
  isMobile: false,
  // 当前主题
  theme: 'light',
  // 语言设置
  language: 'zh-CN',
  // 面包屑导航
  breadcrumbList: [],
  // 访问历史
  visitedViews: [],
  // 缓存的视图
  cachedViews: [],
  // 全局加载状态
  globalLoading: false,
  // 加载文本
  loadingText: '加载中...',
  // 页面标题
  pageTitle: '',
  // 是否显示设置面板
  showSettings: false,
  // 布局设置
  layoutSettings: {
    showHeader: true,
    showSidebar: true,
    showFooter: true,
    showBreadcrumb: true,
    showTabs: false,
    fixedHeader: true,
    sidebarLogo: true
  }
}

const mutations = {
  // 切换侧边栏
  TOGGLE_SIDEBAR(state) {
    state.sidebarCollapsed = !state.sidebarCollapsed
  },
  
  // 设置侧边栏状态
  SET_SIDEBAR_COLLAPSED(state, collapsed) {
    state.sidebarCollapsed = collapsed
  },
  
  // 关闭侧边栏
  CLOSE_SIDEBAR(state) {
    state.sidebarCollapsed = true
  },

  // 设置侧边栏子菜单状态
  SET_SIDEBAR_SUBMENU_OPEN(state, isOpen) {
    state.sidebarSubmenuOpen = isOpen
  },
  
  // 设置移动端状态
  SET_MOBILE(state, isMobile) {
    state.isMobile = isMobile
  },
  
  // 设置主题
  SET_THEME(state, theme) {
    state.theme = theme
    document.documentElement.setAttribute('data-theme', theme)
  },
  
  // 设置语言
  SET_LANGUAGE(state, language) {
    state.language = language
  },
  
  // 设置面包屑
  SET_BREADCRUMB(state, breadcrumbList) {
    state.breadcrumbList = breadcrumbList
  },
  
  // 添加访问历史
  ADD_VISITED_VIEW(state, view) {
    if (state.visitedViews.some(v => v.path === view.path)) return

    state.visitedViews.push({
      name: view.name,
      path: view.path,
      title: view.meta?.title || 'No Title',
      affix: view.meta?.affix || false
    })
  },
  
  // 删除访问历史
  DEL_VISITED_VIEW(state, view) {
    const index = state.visitedViews.findIndex(v => v.path === view.path)
    if (index > -1) {
      state.visitedViews.splice(index, 1)
    }
  },
  
  // 删除其他访问历史
  DEL_OTHERS_VISITED_VIEWS(state, view) {
    state.visitedViews = state.visitedViews.filter(v => {
      return v.affix || v.path === view.path
    })
  },
  
  // 删除所有访问历史
  DEL_ALL_VISITED_VIEWS(state) {
    state.visitedViews = state.visitedViews.filter(v => v.affix)
  },
  
  // 添加缓存视图
  ADD_CACHED_VIEW(state, view) {
    if (state.cachedViews.includes(view.name)) return
    // 默认缓存所有页面，除非明确设置noCache
    if (!view.meta?.noCache) {
      state.cachedViews.push(view.name)
      console.log(`📦 添加页面缓存: ${view.name}`)
    }
  },
  
  // 删除缓存视图
  DEL_CACHED_VIEW(state, view) {
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews.splice(index, 1)
    }
  },
  
  // 删除其他缓存视图
  DEL_OTHERS_CACHED_VIEWS(state, view) {
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews = state.cachedViews.slice(index, index + 1)
    } else {
      state.cachedViews = []
    }
  },
  
  // 删除所有缓存视图
  DEL_ALL_CACHED_VIEWS(state) {
    state.cachedViews = []
  },
  
  // 设置全局加载状态
  SET_GLOBAL_LOADING(state, { loading, text = '加载中...' }) {
    state.globalLoading = loading
    state.loadingText = text
  },
  
  // 设置页面标题
  SET_PAGE_TITLE(state, title) {
    state.pageTitle = title
  },
  
  // 切换设置面板
  TOGGLE_SETTINGS(state) {
    state.showSettings = !state.showSettings
  },
  
  // 设置布局配置
  SET_LAYOUT_SETTING(state, { key, value }) {
    if (state.layoutSettings.hasOwnProperty(key)) {
      state.layoutSettings[key] = value
    }
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.sidebarCollapsed = false
    state.breadcrumbList = []
    state.visitedViews = []
    state.cachedViews = []
    state.globalLoading = false
    state.loadingText = '加载中...'
    state.pageTitle = ''
    state.showSettings = false
  }
}

const actions = {
  // 切换侧边栏
  toggleSidebar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  
  // 关闭侧边栏
  closeSidebar({ commit }) {
    commit('CLOSE_SIDEBAR')
  },
  
  // 设置移动端状态
  setMobile({ commit }, isMobile) {
    commit('SET_MOBILE', isMobile)
  },
  
  // 设置主题
  setTheme({ commit }, theme) {
    commit('SET_THEME', theme)
  },
  
  // 设置语言
  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
  },
  
  // 生成面包屑
  generateBreadcrumb({ commit }, route) {
    const breadcrumbList = []
    const matched = route.matched.filter(item => item.meta && item.meta.title)
    
    matched.forEach(item => {
      breadcrumbList.push({
        path: item.path,
        title: item.meta.title,
        redirect: item.redirect
      })
    })
    
    commit('SET_BREADCRUMB', breadcrumbList)
  },
  
  // 添加访问历史
  addVisitedView({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
    commit('ADD_CACHED_VIEW', view)
  },

  // 添加缓存视图
  addCachedView({ commit }, view) {
    commit('ADD_CACHED_VIEW', view)
  },
  
  // 删除访问历史
  delVisitedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      commit('DEL_CACHED_VIEW', view)
      resolve([...state.visitedViews])
    })
  },
  
  // 删除其他访问历史
  delOthersVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_VISITED_VIEWS', view)
      commit('DEL_OTHERS_CACHED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  
  // 删除所有访问历史
  delAllVisitedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      commit('DEL_ALL_CACHED_VIEWS')
      resolve([...state.visitedViews])
    })
  },
  
  // 设置全局加载状态
  setGlobalLoading({ commit }, payload) {
    commit('SET_GLOBAL_LOADING', payload)
  },
  
  // 设置页面标题
  setPageTitle({ commit }, title) {
    commit('SET_PAGE_TITLE', title)
  },
  
  // 切换设置面板
  toggleSettings({ commit }) {
    commit('TOGGLE_SETTINGS')
  },
  
  // 设置布局配置
  setLayoutSetting({ commit }, payload) {
    commit('SET_LAYOUT_SETTING', payload)
  },
  
  // 初始化应用
  initializeApp({ commit, dispatch }) {
    return new Promise(resolve => {
      // 检测移动端
      const isMobile = window.innerWidth < 768
      commit('SET_MOBILE', isMobile)
      
      // 移动端默认折叠侧边栏
      if (isMobile) {
        commit('SET_SIDEBAR_COLLAPSED', true)
      }
      
      // 设置主题
      const savedTheme = localStorage.getItem('admin-theme') || 'light'
      dispatch('setTheme', savedTheme)
      
      resolve()
    })
  },
  
  // 重置状态
  resetState({ commit }) {
    commit('RESET_STATE')
  }
}

const getters = {
  // 侧边栏状态
  sidebarCollapsed: state => state.sidebarCollapsed,

  // 侧边栏子菜单状态
  sidebarSubmenuOpen: state => state.sidebarSubmenuOpen,
  
  // 移动端状态
  isMobile: state => state.isMobile,
  
  // 当前主题
  theme: state => state.theme,
  
  // 当前语言
  language: state => state.language,
  
  // 面包屑导航
  breadcrumbList: state => state.breadcrumbList,
  
  // 访问历史
  visitedViews: state => state.visitedViews,
  
  // 缓存视图
  cachedViews: state => state.cachedViews,
  
  // 全局加载状态
  globalLoading: state => state.globalLoading,
  
  // 加载文本
  loadingText: state => state.loadingText,
  
  // 页面标题
  pageTitle: state => state.pageTitle,
  
  // 设置面板状态
  showSettings: state => state.showSettings,
  
  // 布局设置
  layoutSettings: state => state.layoutSettings
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
