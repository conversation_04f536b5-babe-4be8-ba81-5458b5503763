<!--
  微信审核管理页面
 /src/view/system/examine.vue重构
-->

<template>
  <div class="lb-system-examine">
    <TopNav />
    <div class="page-main">
      <!-- 上传微信审核 -->
      <el-card class="upload-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>微信小程序审核上传</span>
          </div>
        </template>
        
        <el-form 
          :model="uploadForm" 
          :rules="uploadRules" 
          ref="uploadFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="代码上传密钥" prop="key">
            <div class="upload-file-wrap">
              <div class="upload-file-content">
                {{ uploadForm.key || '未选择文件' }}
              </div>
              <LbButton
                size="small"
                type="primary"
                @click="handleUploadKey"
              >
                上传密钥
              </LbButton>
            </div>
            <input
              type="file"
              accept=".txt,.key"
              style="display: none"
              @change="onKeyFileChange"
              ref="keyFileInput"
            />
          </el-form-item>
          
          <el-form-item label="版本号" prop="version">
            <el-input
              v-model="uploadForm.version"
              placeholder="请输入版本号"
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="版本描述" prop="content">
            <el-input
              v-model="uploadForm.content"
              type="textarea"
              :rows="4"
              placeholder="请输入版本描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="submitUpload" :loading="uploadLoading">
              提交审核
            </LbButton>
            <LbButton @click="resetUploadForm" style="margin-left: 10px;">
              重置
            </LbButton>
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 审核记录 -->
      <el-card class="record-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>审核记录</span>
            <LbButton type="primary" @click="getRecordList">刷新</LbButton>
          </div>
        </template>
        
        <el-table 
          v-loading="recordLoading" 
          :data="recordData" 
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="version" label="版本号" width="120" />
          <el-table-column prop="content" label="版本描述" min-width="200" />
          <el-table-column prop="status" label="审核状态" width="120">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submit_time" label="提交时间" width="170">
            <template #default="scope">
              <div>{{ formatDate(scope.row.submit_time, 1) }}</div>
              <div>{{ formatDate(scope.row.submit_time, 2) }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="audit_time" label="审核时间" width="170">
            <template #default="scope">
              <div v-if="scope.row.audit_time">
                <div>{{ formatDate(scope.row.audit_time, 1) }}</div>
                <div>{{ formatDate(scope.row.audit_time, 2) }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="audit_reason" label="审核意见" min-width="200">
            <template #default="scope">
              <span>{{ scope.row.audit_reason || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="mini"
                  type="primary"
                  @click="viewDetail(scope.row)"
                >
                  查看详情
                </LbButton>
                <LbButton
                  v-if="scope.row.status === 3"
                  size="mini"
                  type="success"
                  @click="resubmit(scope.row)"
                >
                  重新提交
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    
    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="审核详情" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="版本号">{{ examineDetail.version }}</el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="getStatusType(examineDetail.status)" size="small">
            {{ getStatusText(examineDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">{{ examineDetail.submit_time }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ examineDetail.audit_time || '未审核' }}</el-descriptions-item>
        <el-descriptions-item label="版本描述" span="2">{{ examineDetail.content }}</el-descriptions-item>
        <el-descriptions-item label="审核意见" span="2">{{ examineDetail.audit_reason || '无' }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <LbButton @click="detailVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const uploadLoading = ref(false)
const recordLoading = ref(false)
const recordData = ref([])
const detailVisible = ref(false)
const uploadFormRef = ref()
const keyFileInput = ref()

// 上传表单
const uploadForm = reactive({
  key: '',
  version: '',
  content: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 审核详情
const examineDetail = reactive({
  id: '',
  version: '',
  content: '',
  status: 1,
  submit_time: '',
  audit_time: '',
  audit_reason: ''
})

// 表单验证规则
const uploadRules = {
  key: [
    { required: true, message: '请上传代码密钥文件', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入版本描述', trigger: 'blur' }
  ]
}

// 方法
const getRecordList = async (page = 1) => {
  recordLoading.value = true
  pagination.page = page
  
  try {
    const params = new URLSearchParams({
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    
    const response = await fetch(`/api/system/examine/list?${params}`)
    const result = await response.json()

    if (result.code === 200) {
      recordData.value = result.data.list || []
      pagination.total = result.data.total || 0
    } else {
      ElMessage.error(result.meg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取审核记录失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    recordLoading.value = false
  }
}

const getStatusType = (status) => {
  const statusMap = {
    1: 'warning',  // 审核中
    2: 'success',  // 审核通过
    3: 'danger'    // 审核拒绝
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '审核中',
    2: '审核通过',
    3: '审核拒绝'
  }
  return statusMap[status] || '未知'
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const handleUploadKey = () => {
  keyFileInput.value.click()
}

const onKeyFileChange = (event) => {
  const file = event.target.files[0]
  if (file) {
    uploadForm.key = file.name
    // 这里可以添加文件上传逻辑
  }
}

const submitUpload = async () => {
  try {
    await uploadFormRef.value.validate()
    
    uploadLoading.value = true
    
    const response = await fetch('/api/system/examine/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(uploadForm)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('提交审核成功')
      resetUploadForm()
      getRecordList()
    } else {
      ElMessage.error(result.meg || '提交失败')
    }
  } catch (error) {
    console.error('提交审核失败:', error)
    ElMessage.error('提交失败')
  } finally {
    uploadLoading.value = false
  }
}

const resetUploadForm = () => {
  Object.assign(uploadForm, {
    key: '',
    version: '',
    content: ''
  })
  if (uploadFormRef.value) {
    uploadFormRef.value.clearValidate()
  }
  if (keyFileInput.value) {
    keyFileInput.value.value = ''
  }
}

const viewDetail = async (row) => {
  try {
    const response = await fetch(`/api/system/examine/detail/${row.id}`)
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(examineDetail, result.data)
      detailVisible.value = true
    } else {
      ElMessage.error(result.meg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取审核详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const resubmit = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新提交版本 "${row.version}" 的审核吗？`,
      '重新提交确认',
      {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/system/examine/resubmit/${row.id}`, {
      method: 'PUT'
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('重新提交成功')
      getRecordList()
    } else {
      ElMessage.error(result.meg || '提交失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getRecordList(1)
}

const handleCurrentChange = (page) => {
  getRecordList(page)
}

// 生命周期
onMounted(() => {
  getRecordList()
})
</script>

<style scoped>
.lb-system-examine {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.upload-card {
  margin-bottom: 20px;
}

.record-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}

.upload-file-wrap {
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-file-content {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  color: #606266;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .lb-system-examine {
    padding: 10px;
  }
  
  .upload-file-wrap {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-operate {
    flex-direction: column;
  }
  
  .pagination-section {
    text-align: center;
  }
}
</style>
