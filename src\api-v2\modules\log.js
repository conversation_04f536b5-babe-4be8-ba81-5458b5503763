/**
 * 日志管理模块 API
 * 包含操作日志相关接口
 */

import { get } from '../index'
import { API_PATHS } from '@/config/api'

/**
 * 操作日志相关接口
 */
export const operationLog = {
  /**
   * 获取操作日志列表
   * @param {Object} params - 查询参数
   * @param {number} params.adminUserId - 操作人ID，非必填
   * @param {string} params.method - 请求类型，非必填
   * @param {string} params.uri - 请求路径，非必填，支持模糊搜索
   * @param {string} params.clientIp - 请求IP，非必填，支持模糊搜索
   * @param {string} params.resultCode - 返回结果code，非必填
   * @param {number} params.pageNum - 当前页，非必填，默认1
   * @param {number} params.pageSize - 每页数量，非必填，默认10
   * @returns {Promise} 操作日志列表数据
   */
  list(params = {}) {
    return get(API_PATHS.OPERATION_LOG.LIST, {
      adminUserId: params.adminUserId || undefined,
      method: params.method || undefined,
      uri: params.uri || undefined,
      clientIp: params.clientIp || undefined,
      resultCode: params.resultCode || undefined,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10
    })
  }
}

// 默认导出整个日志管理模块
export default {
  operationLog
}
