<template>
  <div class="technician-city">
    <TopNav title="城市管理" />

    <div class="content-container">
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAddCity"
                >
                  添加开放城市
                </LbButton>
                <LbButton
                  size="default"
                  type="success"
                  icon="Plus"
                  @click="handleBatchAdd"
                >
                  批量添加城市
                </LbButton>
                <LbButton
                  size="default"
                  type="danger"
                  icon="Delete"
                  @click="handleBatchDelete"
                  :disabled="selectedCities.length === 0"
                >
                  批量删除 ({{ selectedCities.length }})
                </LbButton>
                <LbButton
                  size="default"
                  type="info"
                  icon="Refresh"
                  @click="handleRefresh"
                >
                  刷新列表
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="table-container">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="flattenedCityList"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" :selectable="isRowSelectable" />

          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column prop="name" label="省份/城市" min-width="200">
            <template #default="scope">
              <div class="city-name-cell">
                <span v-if="!scope.row.leaf" class="province-name" @click="toggleProvince(scope.row)">
                  <el-icon class="expand-icon" :class="{ 'expanded': scope.row.isExpanded }">
                    <ArrowRight />
                  </el-icon>
                  <el-icon><Location /></el-icon>
                  {{ scope.row.name }}
                  <span class="city-count">({{ getCityCount(scope.row) }}个城市)</span>
                </span>
                <span v-else class="city-name">
                  <span class="city-indent">└─</span>
                  <el-tag type="success" size="default">{{ scope.row.name }}</el-tag>
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="cityId" label="城市ID" width="100" align="center">
            <template #default="scope">
              <span v-if="scope.row.leaf">{{ scope.row.cityId }}</span>
              <span v-else class="text-muted">—</span>
            </template>
          </el-table-column>

          <el-table-column prop="leaf" label="类型" width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.leaf" type="success" size="default">城市</el-tag>
              <el-tag v-else type="primary" size="default">省份</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <div class="table-operate" v-if="scope.row.leaf">
                <LbButton
                  size="default"
                  type="danger"
                  @click="handleDelete(scope.row)"
                >
                  移除
                </LbButton>
              </div>
              <span v-else class="text-muted">—</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog
      title="添加开放城市"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="选择城市" prop="selectedCity">
          <el-cascader
            v-model="form.selectedCity"
            :options="cityTreeData"
            :props="cascaderProps"
            placeholder="请选择省份和城市"
            style="width: 100%"
            @change="handleCityChange"
          />
        </el-form-item>

        <el-form-item label="城市信息" v-if="form.cityId">
          <div class="city-info">
            <p><strong>省份：</strong>{{ form.provinceName }}</p>
            <p><strong>城市：</strong>{{ form.cityName }}</p>
            <p><strong>城市ID：</strong>{{ form.cityId }}</p>
            <p><strong>城市字符串：</strong>{{ form.cityStr }}</p>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      title="批量添加开放城市"
      v-model="batchDialogVisible"
      width="800px"
      @close="handleBatchDialogClose"
    >
      <div class="batch-add-container">
        <div class="batch-header">
          <LbButton
            size="default"
            type="primary"
            icon="Plus"
            @click="addBatchItem"
          >
            添加城市
          </LbButton>
          <LbButton
            size="default"
            type="danger"
            icon="Delete"
            @click="clearBatchItems"
            :disabled="batchCities.length === 0"
          >
            清空列表
          </LbButton>
        </div>

        <div class="batch-list" v-if="batchCities.length > 0">
          <el-table
            :data="batchCities"
            style="width: 100%"
            max-height="300"
          >
            <el-table-column prop="provinceName" label="省份" width="120" />
            <el-table-column prop="cityName" label="城市" width="120" />
            <el-table-column prop="cityId" label="城市ID" width="100" />
            <el-table-column prop="cityStr" label="城市字符串" width="120" />
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <LbButton
                  size="default"
                  type="danger"
                  @click="removeBatchItem(scope.$index)"
                >
                  删除
                </LbButton>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="batch-selector">
          <el-form :inline="true">
            <el-form-item label="选择城市">
              <el-cascader
                v-model="batchSelectedCity"
                :options="cityTreeData"
                :props="cascaderProps"
                placeholder="请选择省份和城市"
                style="width: 300px"
                @change="handleBatchCityChange"
              />
            </el-form-item>
            <el-form-item>
              <LbButton
                size="default"
                type="success"
                @click="addToBatchList"
                :disabled="!batchSelectedCity || batchSelectedCity.length !== 2"
              >
                添加到列表
              </LbButton>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="batchDialogVisible = false">取消</LbButton>
          <LbButton 
            type="primary" 
            @click="handleBatchSubmit" 
            :loading="batchSubmitLoading"
            :disabled="batchCities.length === 0"
          >
            批量添加 ({{ batchCities.length }})
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Location, ArrowRight } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 直接导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const batchSubmitLoading = ref(false)
const openCityList = ref([])
const cityTreeData = ref([])
const selectedCities = ref([])
const searchFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false)
const batchDialogVisible = ref(false)
const batchSelectedCity = ref([])
const batchCities = ref([])

// 新增：展开状态管理
const expandedProvinces = ref(new Set()) // 存储已展开的省份ID

// 计算属性：将树形数据扁平化用于表格显示，支持展开/收起
const flattenedCityList = computed(() => {
  const result = []
  openCityList.value.forEach(province => {
    // 添加省份行
    result.push({
      ...province,
      isProvince: true,
      isExpanded: expandedProvinces.value.has(province.cityId)
    })

    // 只有当省份展开时才添加城市行
    if (expandedProvinces.value.has(province.cityId) && province.children && province.children.length > 0) {
      province.children.forEach(city => {
        result.push({
          ...city,
          provinceName: province.name,
          isProvince: false
        })
      })
    }
  })
  return result
})

// 表单数据
const searchForm = reactive({})

const form = reactive({
  selectedCity: [],
  cityId: '',
  cityStr: '',
  provinceName: '',
  cityName: ''
})

// 级联选择器配置
const cascaderProps = {
  value: 'cityId',   // 使用cityId作为值字段
  label: 'name',
  children: 'children',
  leaf: 'leaf',
  checkStrictly: false, // 只能选择叶子节点
  emitPath: true // 返回完整路径
}

// 表单验证规则
const rules = {
  selectedCity: [
    { required: true, message: '请选择城市', trigger: 'change' }
  ]
}

// 方法

// 新增：树形结构相关方法
const toggleProvince = (province) => {
  if (expandedProvinces.value.has(province.cityId)) {
    expandedProvinces.value.delete(province.cityId)
  } else {
    expandedProvinces.value.add(province.cityId)
  }
  console.log('🔄 切换省份展开状态:', province.name, '展开:', expandedProvinces.value.has(province.cityId))
}

const getCityCount = (province) => {
  return province.children ? province.children.length : 0
}

const isRowSelectable = () => {
  // 省份和城市都可以选择
  return true
}

// 表格引用
const tableRef = ref()

// 处理选择变化，实现全选功能
const handleSelectionChange = (selection) => {
  console.log('🔄 选择变化:', selection)

  // 过滤出实际的城市（有ID的叶子节点）
  selectedCities.value = selection.filter(item => item.leaf && item.id)

  // 找出新选中的省份（非叶子节点）
  const selectedProvinces = selection.filter(item => !item.leaf)

  // 为选中的省份自动选中其下所有城市
  selectedProvinces.forEach(province => {
    if (!expandedProvinces.value.has(province.cityId)) {
      // 如果省份未展开，先展开它
      expandedProvinces.value.add(province.cityId)
    }

    // 在下一个tick中选中城市，确保DOM已更新
    setTimeout(() => {
      const provinceCities = openCityList.value.find(p => p.cityId === province.cityId)?.children || []
      provinceCities.forEach(city => {
        const cityRow = flattenedCityList.value.find(item =>
          item.cityId === city.cityId && item.leaf
        )
        if (cityRow && tableRef.value) {
          tableRef.value.toggleRowSelection(cityRow, true)
        }
      })
    }, 100)
  })
}

const getCityTree = async () => {
  try {
    const result = await api.technician.cityTree()
    console.log('🏙️ 城市树形数据 (API-V2):', result)

    if (result.code === 200 || result.code === '200') {
      cityTreeData.value = result.data || []
    } else {
      console.error('❌ 获取城市树形数据失败:', result)
      ElMessage.error(result.meg || result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('获取城市树形数据失败:', error)
    ElMessage.error('获取城市数据失败')
  }
}

const getOpenCityList = async () => {
  loading.value = true
  try {
    const result = await api.technician.openCityList()
    console.log('📋 开放城市列表数据 (API-V2):', result)

    if (result.code === 200 || result.code === '200') {
      openCityList.value = result.data || []
    } else {
      console.error('❌ 获取开放城市列表失败:', result)
      ElMessage.error(result.meg || result.msg || '获取开放城市列表失败')
    }
  } catch (error) {
    console.error('获取开放城市列表失败:', error)
    ElMessage.error('获取开放城市列表失败')
  } finally {
    loading.value = false
  }
}

const handleAddCity = () => {
  resetForm()
  dialogVisible.value = true
}

const handleBatchAdd = () => {
  batchCities.value = []
  batchSelectedCity.value = []
  batchDialogVisible.value = true
}

const handleRefresh = () => {
  getOpenCityList()
}

const handleCityChange = (value) => {
  console.log('🔄 级联选择器值变化:', value)
  console.log('🏙️ 当前城市树数据:', cityTreeData.value)

  if (value && value.length === 2) {
    const [provinceCityId, cityId] = value
    console.log('📍 选中的省份cityId:', provinceCityId, '城市cityId:', cityId)

    // 查找省份和城市信息
    const province = cityTreeData.value.find(p => {
      console.log('🔍 比较省份:', p.cityId, '===', provinceCityId, '结果:', p.cityId === provinceCityId)
      return p.cityId === provinceCityId
    })
    console.log('🏛️ 找到的省份:', province)

    const city = province?.children?.find(c => {
      console.log('🔍 比较城市:', c.cityId, '===', cityId, '结果:', c.cityId === cityId)
      return c.cityId === cityId
    })
    console.log('🏙️ 找到的城市:', city)

    if (province && city && cityId !== null && cityId !== undefined) {
      form.cityId = String(cityId)
      form.cityStr = `${provinceCityId},${cityId}`
      form.provinceName = province.name
      form.cityName = city.name
      console.log('✅ 表单数据已更新:')
      console.log('   cityId:', form.cityId)
      console.log('   cityStr:', form.cityStr)
      console.log('   provinceName:', form.provinceName)
      console.log('   cityName:', form.cityName)
    } else {
      console.log('❌ 未找到对应的省份或城市，清空表单')
      // 如果找不到对应的省份或城市，清空表单
      form.cityId = ''
      form.cityStr = ''
      form.provinceName = ''
      form.cityName = ''
    }
  } else {
    console.log('❌ 选择值无效，清空表单')
    form.cityId = ''
    form.cityStr = ''
    form.provinceName = ''
    form.cityName = ''
  }
}

const handleBatchCityChange = () => {
  // 批量选择时的处理逻辑与单个选择相同
  // 这里只是更新选中的值，实际添加在addToBatchList中处理
}

const addToBatchList = () => {
  if (!batchSelectedCity.value || batchSelectedCity.value.length !== 2) {
    ElMessage.warning('请先选择省份和城市')
    return
  }

  const [provinceCityId, cityId] = batchSelectedCity.value

  // 检查cityId是否有效
  if (cityId === null || cityId === undefined) {
    ElMessage.warning('选择的城市数据无效')
    return
  }

  // 检查是否已经添加过
  const exists = batchCities.value.some(item => item.cityId === String(cityId))
  if (exists) {
    ElMessage.warning('该城市已在列表中')
    return
  }

  // 查找省份和城市信息
  const province = cityTreeData.value.find(p => p.cityId === provinceCityId)
  const city = province?.children?.find(c => c.cityId === cityId)

  if (province && city) {
    batchCities.value.push({
      cityId: String(cityId),
      cityStr: `${provinceCityId},${cityId}`,
      provinceName: province.name,
      cityName: city.name
    })

    // 清空选择
    batchSelectedCity.value = []
    ElMessage.success('城市已添加到列表')
  } else {
    ElMessage.error('找不到对应的省份或城市信息')
  }
}

const addBatchItem = () => {
  // 这个方法可以用于其他添加逻辑，目前主要通过级联选择器添加
  ElMessage.info('请使用下方的城市选择器添加城市')
}

const removeBatchItem = (index) => {
  batchCities.value.splice(index, 1)
  ElMessage.success('已从列表中移除')
}

const clearBatchItems = () => {
  batchCities.value = []
  ElMessage.success('列表已清空')
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    console.log('提交表单数据:', form.cityId, form.cityStr)

    if (!form.cityId || !form.cityStr) {
      ElMessage.error('请选择有效的城市')
      return
    }

    submitLoading.value = true

    const result = await api.technician.addOpenCity({
      cityId: String(form.cityId),
      cityStr: String(form.cityStr)
    })

    console.log('📤 添加开放城市API响应:', result)

    if (result.code === '200' || result.code === 200) {
      ElMessage.success('添加开放城市成功')
      dialogVisible.value = false
      getOpenCityList()
    } else if (result.code === '-1') {
      // 处理城市已存在的情况
      ElMessage.warning(result.msg || '该城市已开放')
    } else {
      ElMessage.error(result.meg || result.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加开放城市失败:', error)
    ElMessage.error('添加失败')
  } finally {
    submitLoading.value = false
  }
}

const handleBatchSubmit = async () => {
  if (batchCities.value.length === 0) {
    ElMessage.warning('请先添加要开放的城市')
    return
  }

  try {
    batchSubmitLoading.value = true

    const result = await api.technician.batchAddOpenCity(batchCities.value)

    console.log('📤 批量添加开放城市API响应:', result)

    if (result.code === '200' || result.code === 200) {
      ElMessage.success(`批量添加 ${batchCities.value.length} 个城市成功`)
      batchDialogVisible.value = false
      getOpenCityList()
    } else if (result.code === '-1') {
      // 处理部分城市已存在的情况
      ElMessage.warning(result.msg || '部分城市已开放，请检查列表')
      // 即使有重复，也刷新列表显示最新状态
      getOpenCityList()
    } else {
      ElMessage.error(result.meg || result.msg || '批量添加失败')
    }
  } catch (error) {
    console.error('批量添加开放城市失败:', error)
    ElMessage.error('批量添加失败')
  } finally {
    batchSubmitLoading.value = false
  }
}



// 单个删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除开放城市"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await api.technician.deleteOpenCity({
      ids: [row.id]
    })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getOpenCityList()
    } else {
      ElMessage.error(result.msg || '删除失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除开放城市失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedCities.value.length === 0) {
    ElMessage.warning('请先选择要删除的城市')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCities.value.length} 个开放城市吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedCities.value.map(city => city.id)
    const result = await api.technician.deleteOpenCity({ ids })

    if (result.code === '200') {
      ElMessage.success(`批量删除 ${selectedCities.value.length} 个城市成功`)
      selectedCities.value = []
      getOpenCityList()
    } else {
      ElMessage.error(result.msg || '批量删除失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除开放城市失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleDialogClose = () => {
  resetForm()
}

const handleBatchDialogClose = () => {
  batchCities.value = []
  batchSelectedCity.value = []
}

const resetForm = () => {
  form.selectedCity = []
  form.cityId = ''
  form.cityStr = ''
  form.provinceName = ''
  form.cityName = ''
}



// Lifecycle hook
onMounted(() => {
  getCityTree()
  getOpenCityList()
})
</script>

<style scoped>
/* Page main styles */
.technician-city {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Search form styles */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* Table styles */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* Time column styles */
.time-column p {
  margin: 0;
  line-height: 1.6;
  font-size: 14px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

/* Operation button styles */
.table-operate {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Dialog styles */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* City info styles */
.city-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.city-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #333;
}

/* Batch add styles */
.batch-add-container {
  max-height: 500px;
  overflow-y: auto;
}

.batch-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.batch-header .el-button {
  margin-right: 10px;
}

.batch-list {
  margin-bottom: 20px;
}

.batch-selector {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.batch-selector .el-form-item {
  margin-bottom: 0;
}

/* Button styles */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* Tag styles */
.el-tag {
  font-size: 12px;
}

/* Cascader styles */
.el-cascader {
  width: 100%;
}

/* City name cell styles */
.city-name-cell {
  display: flex;
  align-items: center;
}

.province-name {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #409eff;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* --- 修改开始 --- */
/* 移除鼠标经过省份名称时的悬停效果 */
.province-name:hover {
  color: #409eff; /* 保持原色 */
  background-color: transparent; /* 移除背景色 */
  padding: 0; /* 移除内边距 */
  border-radius: 0; /* 移除圆角 */
}
/* --- 修改结束 --- */


.province-name .el-icon {
  margin-right: 6px;
}

/* 展开图标样式 */
.expand-icon {
  transition: transform 0.3s ease;
  margin-right: 4px;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

/* 城市数量显示 */
.city-count {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
  font-weight: normal;
}

.city-name {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.city-indent {
  color: #c0c4cc;
  margin-right: 8px;
  font-family: monospace;
}

.text-muted {
  color: #c0c4cc;
}

/* Responsive design */
@media (max-width: 768px) {
  .technician-city {
    padding: 10px;
  }

  .search-form {
    padding: 15px;
  }

  .table-operate {
    flex-direction: column;
    gap: 4px;
  }

  .batch-selector .el-form {
    display: block;
  }

  .batch-selector .el-form-item {
    margin-bottom: 15px;
  }
}
</style>