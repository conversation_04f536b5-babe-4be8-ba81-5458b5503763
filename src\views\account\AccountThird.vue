<!--
  第三方管理页面
 /src/view/account/acountThree/index.vue重构
-->

<template>
  <div class="lb-account-third">
    <TopNav />
    <div class="page-main">
      <!-- 操作按钮 -->
      <el-row class="page-header">
        <LbButton type="success" @click="handleAdd">新增第三方</LbButton>
      </el-row>
      
      <!-- 数据表格 -->
      <el-card class="table-card" shadow="never">
        <el-table 
          v-loading="loading" 
          :data="tableData" 
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
          border
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="第三方名称" width="200" />
          <el-table-column prop="city_name" label="服务地址" width="200" />
          <el-table-column prop="tel" label="联系电话" width="150" />
          <el-table-column prop="api_key" label="API密钥" width="200">
            <template #default="scope">
              <span>{{ scope.row.api_key ? '已配置' : '未配置' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="170">
            <template #default="scope">
              <div>{{ formatDate(scope.row.create_time, 1) }}</div>
              <div>{{ formatDate(scope.row.create_time, 2) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="mini"
                  type="primary"
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </LbButton>
                <LbButton
                  size="mini"
                  type="warning"
                  @click="handleConfig(scope.row)"
                >
                  API配置
                </LbButton>
                <LbButton
                  size="mini"
                  :type="scope.row.status === 1 ? 'danger' : 'success'"
                  @click="toggleStatus(scope.row)"
                >
                  {{ scope.row.status === 1 ? '禁用' : '启用' }}
                </LbButton>
                <LbButton
                  size="mini"
                  type="danger"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="type === 'add' ? '新增第三方' : '编辑第三方'" 
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="第三方名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入第三方名称" />
        </el-form-item>
        <el-form-item label="联系电话" prop="tel">
          <el-input v-model="form.tel" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-select v-model="form.province" placeholder="请选择省份" style="width: 100%;" @change="onProvinceChange">
            <el-option 
              v-for="item in provinceList" 
              :key="item.id" 
              :label="item.title" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-select v-model="form.city" placeholder="请选择城市" style="width: 100%;">
            <el-option 
              v-for="item in cityList" 
              :key="item.id" 
              :label="item.title" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-form-item label="服务类型" prop="service_type">
          <el-checkbox-group v-model="form.service_type">
            <el-checkbox label="维修服务">维修服务</el-checkbox>
            <el-checkbox label="清洁服务">清洁服务</el-checkbox>
            <el-checkbox label="搬家服务">搬家服务</el-checkbox>
            <el-checkbox label="装修服务">装修服务</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="form.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <LbButton @click="handleClose">取消</LbButton>
        <LbButton type="primary" @click="handleConfirm" :loading="submitLoading">确定</LbButton>
      </template>
    </el-dialog>
    
    <!-- API配置对话框 -->
    <el-dialog v-model="configVisible" title="API配置" width="60%">
      <el-form :model="configForm" :rules="configRules" ref="configFormRef" label-width="120px">
        <el-form-item label="API地址" prop="api_url">
          <el-input v-model="configForm.api_url" placeholder="请输入API地址" />
        </el-form-item>
        <el-form-item label="API密钥" prop="api_key">
          <el-input v-model="configForm.api_key" placeholder="请输入API密钥" show-password />
        </el-form-item>
        <el-form-item label="API秘钥" prop="api_secret">
          <el-input v-model="configForm.api_secret" placeholder="请输入API秘钥" show-password />
        </el-form-item>
        <el-form-item label="超时时间" prop="timeout">
          <el-input-number v-model="configForm.timeout" :min="1" :max="60" />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
        <el-form-item label="重试次数" prop="retry_count">
          <el-input-number v-model="configForm.retry_count" :min="0" :max="10" />
        </el-form-item>
        <el-form-item label="启用状态" prop="enabled">
          <el-radio-group v-model="configForm.enabled">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <LbButton @click="configVisible = false">取消</LbButton>
        <LbButton type="warning" @click="testConnection" :loading="testLoading">测试连接</LbButton>
        <LbButton type="primary" @click="submitConfig" :loading="configLoading">保存配置</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const configVisible = ref(false)
const type = ref('add')
const formRef = ref()
const configFormRef = ref()
const submitLoading = ref(false)
const configLoading = ref(false)
const testLoading = ref(false)
const provinceList = ref([])
const cityList = ref([])

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const form = reactive({
  id: '',
  name: '',
  tel: '',
  province: '',
  city: '',
  address: '',
  service_type: [],
  remark: ''
})

// API配置表单
const configForm = reactive({
  id: '',
  api_url: '',
  api_key: '',
  api_secret: '',
  timeout: 30,
  retry_count: 3,
  enabled: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入第三方名称', trigger: 'blur' }
  ],
  tel: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  province: [
    { required: true, message: '请选择省份', trigger: 'change' }
  ],
  city: [
    { required: true, message: '请选择城市', trigger: 'change' }
  ]
}

const configRules = {
  api_url: [
    { required: true, message: '请输入API地址', trigger: 'blur' }
  ],
  api_key: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ],
  api_secret: [
    { required: true, message: '请输入API秘钥', trigger: 'blur' }
  ]
}

// 方法
const getTableDataList = async (page = 1) => {
  loading.value = true
  pagination.page = page
  
  try {
    const params = new URLSearchParams({
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    
    const response = await fetch(`/api/account/third/list?${params}`)
    const result = await response.json()

    if (result.code === 200) {
      tableData.value = result.data.list || []
      pagination.total = result.data.total || 0
    } else {
      ElMessage.error(result.meg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取第三方列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const getProvinceList = async () => {
  try {
    const response = await fetch('/api/common/province')
    const result = await response.json()
    
    if (result.code === 200) {
      provinceList.value = result.data || []
    }
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

const getCityList = async (provinceId) => {
  try {
    const response = await fetch(`/api/common/city?province_id=${provinceId}`)
    const result = await response.json()
    
    if (result.code === 200) {
      cityList.value = result.data || []
    }
  } catch (error) {
    console.error('获取城市列表失败:', error)
  }
}

const getStatusType = (status) => {
  return status === 1 ? 'success' : 'danger'
}

const getStatusText = (status) => {
  return status === 1 ? '启用' : '禁用'
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const onProvinceChange = (provinceId) => {
  form.city = ''
  cityList.value = []
  if (provinceId) {
    getCityList(provinceId)
  }
}

const handleAdd = () => {
  type.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  type.value = 'edit'
  Object.assign(form, {
    id: row.id,
    name: row.name,
    tel: row.tel,
    province: row.province,
    city: row.city,
    address: row.address || '',
    service_type: row.service_type || [],
    remark: row.remark || ''
  })
  
  // 加载城市列表
  if (form.province) {
    getCityList(form.province)
  }
  
  dialogVisible.value = true
}

const handleConfig = async (row) => {
  try {
    const response = await fetch(`/api/account/third/config/${row.id}`)
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(configForm, {
        id: row.id,
        ...result.data
      })
      configVisible.value = true
    } else {
      ElMessage.error(result.meg || '获取配置失败')
    }
  } catch (error) {
    console.error('获取API配置失败:', error)
    ElMessage.error('获取配置失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除第三方 "${row.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/account/third/delete/${row.id}`, {
      method: 'DELETE'
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除第三方失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const toggleStatus = async (row) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}第三方 "${row.name}" 吗？`,
      `${action}确认`,
      {
        confirmButtonText: `确定${action}`,
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/account/third/status/${row.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        status: row.status === 1 ? 0 : 1 
      })
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success(`${action}成功`)
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改第三方状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const testConnection = async () => {
  try {
    await configFormRef.value.validate()
    
    testLoading.value = true
    
    const response = await fetch('/api/account/third/test-connection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(result.meg || '连接测试失败')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    ElMessage.error('连接测试失败')
  } finally {
    testLoading.value = false
  }
}

const submitConfig = async () => {
  try {
    await configFormRef.value.validate()
    
    configLoading.value = true
    
    const response = await fetch(`/api/account/third/config/${configForm.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
      configVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '保存失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    configLoading.value = false
  }
}

const handleConfirm = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const url = type.value === 'add' 
      ? '/api/account/third/add' 
      : `/api/account/third/update/${form.id}`
    
    const method = type.value === 'add' ? 'POST' : 'PUT'
    
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(form)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success(type.value === 'add' ? '新增成功' : '编辑成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    id: '',
    name: '',
    tel: '',
    province: '',
    city: '',
    address: '',
    service_type: [],
    remark: ''
  })
  cityList.value = []
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getTableDataList(1)
}

const handleCurrentChange = (page) => {
  getTableDataList(page)
}

// 生命周期
onMounted(() => {
  getTableDataList()
  getProvinceList()
})
</script>

<style scoped>
.lb-account-third {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .lb-account-third {
    padding: 10px;
  }

  .table-operate {
    flex-direction: column;
  }

  .pagination-section {
    text-align: center;
  }
}
</style>
