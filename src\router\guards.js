/**
 * 路由守卫配置

 * 
 * 功能：
 * - 登录状态验证
 * - 权限控制
 * - 动态路由生成
 * - 页面标题设置
 * - 进度条控制
 */

import NProgress from 'nprogress'
import { ElMessage } from 'element-plus'
import store from '@/store'
import { getToken } from '@/utils/auth'
import { refreshUtils } from '@/utils/eventBus'

// 白名单路由（不需要登录）
const whiteList = ['/login', '/register', '/forgot-password', '/404', '/403', '/500']

// 设置路由守卫
export function setupRouterGuards(router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    console.log(`🚀 路由守卫执行: ${from.path} -> ${to.path}`)
    console.log('🔍 目标路由信息:', { path: to.path, name: to.name, matched: to.matched.length })

    // 开始进度条
    NProgress.start()

    // 设置页面标题
    document.title = getPageTitle(to.meta.title)

    // 获取token
    const hasToken = getToken()
    console.log('🔑 Token状态:', hasToken ? '存在' : '不存在')

    if (hasToken) {
      // 已登录
      if (to.path === '/login') {
        // 如果已登录，重定向到动态首页
        const dynamicHomePage = store.getters['menu/getDynamicHomePage']
        console.log('📍 已登录用户访问登录页，重定向到动态首页:', dynamicHomePage)
        next({ path: dynamicHomePage })
        NProgress.done()
      } else if (to.path === '/') {
        // 如果访问根路径，重定向到动态首页
        const dynamicHomePage = store.getters['menu/getDynamicHomePage']
        console.log('📍 访问根路径，重定向到动态首页:', dynamicHomePage)
        next({ path: dynamicHomePage })
        NProgress.done()
      } else {
        // 登录成功后直接进入后台，跳过用户信息获取
        console.log('🔐 用户已登录，直接进入后台')

        // 设置默认用户信息（避免其他地方的依赖问题）
        const hasUserInfo = store.getters['auth/userInfo']?.id
        if (!hasUserInfo) {
          // 设置默认用户信息
          store.commit('auth/SET_USER_INFO', {
            id: 1,
            username: 'admin',
            name: '管理员',
            avatar: '',
            email: '<EMAIL>',
            phone: '13800138000'
          })

          // 设置默认角色和权限
          store.commit('auth/SET_ROLES', ['admin'])
          store.commit('auth/SET_PERMISSIONS', ['*:*:*'])
        }

        // 获取用户角色（使用默认角色）
        const userRoles = store.getters['auth/roles'] || ['admin']

        // 检查菜单数据是否已加载，如果未加载则尝试恢复或重新获取
        const menuLoaded = store.getters['menu/menuLoaded']
        if (!menuLoaded) {
          console.log('⚠️ 菜单数据未加载，尝试恢复或重新获取...')
          try {
            // 使用新的fetchUserMenus方法，支持缓存恢复和重新获取
            await store.dispatch('menu/fetchUserMenus')
            console.log('🌲 菜单数据加载成功')
          } catch (error) {
            console.warn('⚠️ 菜单数据加载失败，使用降级菜单:', error)
            try {
              await store.dispatch('menu/useFallbackMenus')
              console.log('🌲 降级菜单加载成功')
            } catch (fallbackError) {
              console.warn('⚠️ 降级菜单加载失败:', fallbackError)
            }
          }
        } else {
          console.log('✅ 菜单数据已存在，跳过加载')
        }

        // 生成动态路由（每次都重新生成，确保路由正确添加）
        console.log('🔄 开始生成动态路由，用户角色:', userRoles)
        const accessRoutes = await store.dispatch('routes/generateRoutes', userRoles)
        console.log('🛣️ 生成的动态路由数量:', accessRoutes.length)
        console.log('🛣️ 生成的动态路由:', accessRoutes.map(r => ({ path: r.path, name: r.name })))

        // 动态添加路由
        accessRoutes.forEach(route => {
          try {
            router.addRoute(route)
            console.log(`✅ 成功添加路由: ${route.path} (${route.name})`)
          } catch (error) {
            console.error(`❌ 添加路由失败: ${route.path} (${route.name})`, error)
          }
        })

        // 验证路由是否添加成功
        console.log('🔍 当前所有路由:', router.getRoutes().map(r => ({ path: r.path, name: r.name })))

        // 直接放行，进入后台
        next()
      }
    } else {
      // 未登录
      if (whiteList.includes(to.path)) {
        // 在白名单中，直接放行
        next()
      } else {
        // 不在白名单中，重定向到登录页
        next(`/login?redirect=${to.path}`)
        NProgress.done()
      }
    }
  })

  // 全局后置守卫
  router.afterEach((to, from) => {
    // 结束进度条
    NProgress.done()

    // 更新面包屑
    store.dispatch('ui/generateBreadcrumb', to)

    // 添加到访问历史
    store.dispatch('ui/addVisitedView', to)

    // 添加到缓存视图（确保页面被缓存）
    store.dispatch('ui/addCachedView', to)

    // 处理页面返回时的数据刷新
    handleRouteBackRefresh(to, from)
  })

  // 路由错误处理
  router.onError((error) => {
    console.error('路由错误:', error)
    NProgress.done()
    
    // 可以在这里添加错误上报逻辑
    if (import.meta.env.PROD) {
      // 生产环境错误上报
      // errorMonitor.captureException(error)
    }
  })
}

// 获取页面标题
function getPageTitle(pageTitle) {
  const title = '今师傅'
  if (pageTitle) {
    return `${pageTitle} - ${title}`
  }
  return title
}

/**
 * 处理路由返回时的数据刷新
 * @param {Object} to 目标路由
 * @param {Object} from 来源路由
 */
function handleRouteBackRefresh(to, from) {
  // 只有从编辑页面返回到列表页面时才触发数据刷新
  // 侧边栏子菜单之间的跳转不应该触发刷新
  const isFromEditPage = from.path && (
    from.path.includes('/edit') ||
    from.path.includes('/add') ||
    from.name?.includes('Edit') ||
    from.name?.includes('Add')
  )

  const isToListPage = to.path && (
    to.path.includes('/list') ||
    to.name?.includes('List')
  )

  // 检查是否是侧边栏子菜单之间的跳转
  const isSidebarNavigation = !isFromEditPage && (
    to.path.includes('/service') ||
    to.path.includes('/technician') ||
    to.path.includes('/market') ||
    to.path.includes('/user') ||
    to.path.includes('/finance') ||
    to.path.includes('/shop')
  )

  // 如果是侧边栏导航，不触发数据刷新，保持缓存
  if (isSidebarNavigation && !isFromEditPage) {
    console.log(`📋 侧边栏导航: ${from.path} -> ${to.path}，保持页面缓存`)
    return
  }

  if (isFromEditPage && isToListPage) {
    console.log(`🔄 从编辑页面 ${from.path} 返回到列表页面 ${to.path}，触发数据刷新`)

    // 延迟执行，确保页面已经完全加载
    setTimeout(() => {
      // 根据目标路由确定刷新的模块
      if (to.path.includes('/technician')) {
        refreshUtils.refreshTechnicianList({ source: 'route_back', from: from.path })
      } else if (to.path.includes('/service')) {
        refreshUtils.refreshServiceList({ source: 'route_back', from: from.path })
      } else if (to.path.includes('/market')) {
        refreshUtils.refreshMarketList({ source: 'route_back', from: from.path })
      } else if (to.path.includes('/user')) {
        refreshUtils.refreshUserList({ source: 'route_back', from: from.path })
      } else if (to.path.includes('/finance')) {
        refreshUtils.refreshFinanceList({ source: 'route_back', from: from.path })
      } else if (to.path.includes('/shop')) {
        refreshUtils.refreshShopOrderList({ source: 'route_back', from: from.path })
      } else if (to.path.includes('/distribution')) {
        refreshUtils.refreshDistributionList({ source: 'route_back', from: from.path })
      } else {
        // 通用刷新
        refreshUtils.refreshAll({ source: 'route_back', from: from.path })
      }
    }, 100)
  }
}

// 权限验证函数
export function hasPermission(roles, permissionRoles) {
  if (!permissionRoles) return true
  if (!roles || roles.length === 0) return false
  
  return roles.some(role => permissionRoles.includes(role))
}

// 检查路由权限
export function checkRoutePermission(route, userRoles) {
  if (!route.meta || !route.meta.roles) {
    return true
  }
  
  return hasPermission(userRoles, route.meta.roles)
}

// 过滤异步路由
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    
    if (checkRoutePermission(tmp, roles)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}
