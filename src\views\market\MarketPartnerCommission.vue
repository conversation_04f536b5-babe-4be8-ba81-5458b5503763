<!--
  合伙人佣金统计页面
  参考MarketList.vue的实现模式，按照快速开发指南重构
  对应API: /api/admin/partner/commission
-->

<template>
  <div class="market-partner-commission">
    <!-- 顶部导航 -->
    <TopNav title="合伙人佣金统计" />

    <div class="content-container">
      <!-- 返回按钮 -->
      <div class="back-button-container">
        <LbButton
          size="default"
          icon="ArrowLeft"
          @click="handleBack"
        >
          返回合伙人管理
        </LbButton>
      </div>

      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户ID" prop="userId">
                <el-input
                  size="default"
                  v-model="searchForm.userId"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item>
                <LbButton type="primary" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton @click="handleReset">
                  重置
                </LbButton>
                <LbButton type="success" @click="handleExport" :loading="exportLoading">
                  导出
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="id" label="佣金ID" width="100" align="center" />
          <el-table-column prop="userId" label="用户ID" width="100" align="center" />
          <el-table-column prop="type" label="佣金类型" width="120" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.type === 1 ? 'primary' : 'success'">
                {{ scope.row.type === 1 ? '一级佣金' : '二级佣金' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="佣金金额" width="120" align="center">
            <template #default="scope">
              <span class="commission-amount">¥{{ scope.row.price.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="coachId" label="师傅ID" width="100" align="center">
            <template #default="scope">
              <span>{{ scope.row.coachId || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="agentId" label="代理ID" width="100" align="center">
            <template #default="scope">
              <span>{{ scope.row.agentId || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" align="center" show-overflow-tooltip />
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="scope">
              <LbButton size="small" type="primary" @click="handleView(scope.row)">
                查看详情
              </LbButton>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      title="佣金详情"
      v-model="detailDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="currentDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="佣金ID">{{ currentDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ currentDetail.userId }}</el-descriptions-item>
          <el-descriptions-item label="佣金类型">
            <el-tag :type="currentDetail.type === 1 ? 'primary' : 'success'">
              {{ currentDetail.type === 1 ? '一级佣金' : '二级佣金' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="佣金金额">
            <span class="commission-amount">¥{{ currentDetail.price.toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="师傅ID">{{ currentDetail.coachId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="代理ID">{{ currentDetail.agentId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ currentDetail.createTime }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="detailDialogVisible = false">关闭</LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()

// 响应式数据
const searchFormRef = ref()
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const detailDialogVisible = ref(false)
const currentDetail = ref(null)

// 搜索表单
const searchForm = reactive({
  userId: '',
  pageNum: 1,
  pageSize: 10
})

// 获取合伙人佣金统计列表
const getPartnerCommissionList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      userId: searchForm.userId || undefined
    }

    const response = await proxy.$api.market.partnerCommissionList(params)
    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0
    } else {
      ElMessage.error(response.msg || '获取佣金统计列表失败')
    }
  } catch (error) {
    console.error('获取佣金统计列表失败:', error)
    ElMessage.error('获取佣金统计列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchForm.pageNum = 1
  getPartnerCommissionList()
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    userId: '',
    pageNum: 1,
    pageSize: 10
  })
  getPartnerCommissionList()
}

// 查看详情
const handleView = (row) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

// 导出功能
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出合伙人佣金统计')

    // 构建导出参数（排除分页参数）
    const exportParams = {}
    if (searchForm.userId) exportParams.userId = searchForm.userId

    // 获取当前时间作为文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '')
    const filename = `合伙人佣金统计_${timestamp}.xlsx`

    // 构建下载URL - 使用环境变量
    const downloadUrl = `${import.meta.env.VITE_API_BASE_URL || ''}/api/admin/partner/commission/export`

    // 创建下载链接
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    link.target = '_blank'

    // 添加token到请求头（如果需要）
    const token = sessionStorage.getItem('minitk')
    if (token) {
      // 对于文件下载，我们需要在URL中添加token参数
      const params = new URLSearchParams(exportParams)
      params.append('token', encodeURIComponent(token))
      link.href = `${downloadUrl}?${params.toString()}`
    } else if (Object.keys(exportParams).length > 0) {
      // 如果没有token但有其他参数
      const params = new URLSearchParams(exportParams)
      link.href = `${downloadUrl}?${params.toString()}`
    }

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('导出成功')
    console.log('✅ 合伙人佣金统计导出成功')
  } catch (error) {
    console.error('❌ 导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 返回合伙人管理
const handleBack = () => {
  router.push('/market/partner')
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getPartnerCommissionList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getPartnerCommissionList()
}

// 页面初始化
onMounted(() => {
  // 从URL参数中获取userId
  const userId = route.query.userId
  if (userId) {
    searchForm.userId = userId
    console.log('🔗 从URL参数获取userId:', userId)
  }
  getPartnerCommissionList()
})
</script>

<style scoped>
.market-partner-commission {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button-container {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.search-form-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.commission-amount {
  font-weight: 600;
  color: #e6a23c;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
