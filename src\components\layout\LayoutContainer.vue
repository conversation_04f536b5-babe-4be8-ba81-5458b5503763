<!--
  LayoutContainer 主布局容器组件

  
  功能：
  - 整体布局结构
  - 响应式布局逻辑
  - 侧边栏折叠控制
  - 移动端适配
-->

<template>
  <div class="layout-container" :class="layoutClasses">
    <!-- 顶部导航栏 -->
    <LayoutHeader />

    <!-- 侧边栏 -->
    <LayoutSidebar />

    <!-- 主内容区域 -->
    <main class="layout-main" :class="mainClasses">
      <div class="main-content">
        <!-- 路由视图 -->
        <router-view v-slot="{ Component, route }">
          <transition
            :name="transitionName"
            mode="out-in"
            @before-enter="onBeforeEnter"
            @after-enter="onAfterEnter"
          >
            <keep-alive :include="cachedViews" :max="10">
              <component :is="Component" :key="route.name" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </main>

    <!-- 底部信息栏 -->
    <LayoutFooter :class="footerClasses" />

    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && !sidebarCollapsed"
      class="mobile-overlay"
      @click="closeSidebar"
    />

    <!-- 回到顶部按钮 -->
    <transition name="fade">
      <div
        v-show="showBackTop"
        class="back-top"
        @click="scrollToTop"
      >
        <el-icon><Top /></el-icon>
      </div>
    </transition>

    <!-- 菜单调试组件（仅开发环境显示） -->
    <!-- <MenuDebug v-if="isDev" /> -->
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { Top } from '@element-plus/icons-vue'
import LayoutHeader from './LayoutHeader.vue'
import LayoutSidebar from './LayoutSidebar.vue'
import LayoutFooter from './LayoutFooter.vue'
import MenuDebug from '../debug/MenuDebug.vue'

const store = useStore()
const route = useRoute()

// 响应式数据
const transitionName = ref('fade')
const showBackTop = ref(false)
const scrollTop = ref(0)

// 计算属性
const sidebarCollapsed = computed(() => store.getters['ui/sidebarCollapsed'])
const cachedViews = computed(() => store.getters['ui/cachedViews'])
const isMobile = computed(() => store.getters['ui/isMobile'])
const currentTheme = computed(() => store.getters['ui/theme'])
const isDev = computed(() => import.meta.env.DEV)

// 布局样式类
const layoutClasses = computed(() => ({
  'is-mobile': isMobile.value,
  'sidebar-collapsed': sidebarCollapsed.value,
  [`theme-${currentTheme.value}`]: true
}))

const mainClasses = computed(() => ({
  'sidebar-collapsed': sidebarCollapsed.value,
  'sidebar-submenu-open': sidebarSubmenuOpen.value,
  'is-mobile': isMobile.value
}))

const footerClasses = computed(() => ({
  'sidebar-collapsed': sidebarCollapsed.value,
  'sidebar-submenu-open': sidebarSubmenuOpen.value
}))

// 侧边栏子菜单状态
const sidebarSubmenuOpen = computed(() => store.getters['ui/sidebarSubmenuOpen'])

// 方法
const closeSidebar = () => {
  store.dispatch('ui/closeSidebar')
}

const scrollToTop = () => {
  const mainElement = document.querySelector('.layout-main')
  if (mainElement) {
    mainElement.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }
}

const handleScroll = (e) => {
  scrollTop.value = e.target.scrollTop
  showBackTop.value = scrollTop.value > 300
}

// 路由过渡动画控制
const onBeforeEnter = () => {
  // 路由切换开始
}

const onAfterEnter = () => {
  // 路由切换完成
  nextTick(() => {
    // 滚动到顶部
    const mainElement = document.querySelector('.layout-main')
    if (mainElement) {
      mainElement.scrollTop = 0
    }
  })
}

// 监听路由变化，设置过渡动画
watch(
  () => route.path,
  (to, from) => {
    if (!from) {
      transitionName.value = 'fade'
      return
    }
    
    // 根据路由层级设置不同的过渡动画
    const toDepth = to.split('/').length
    const fromDepth = from.split('/').length
    
    if (toDepth > fromDepth) {
      transitionName.value = 'slide-left'
    } else if (toDepth < fromDepth) {
      transitionName.value = 'slide-right'
    } else {
      transitionName.value = 'fade'
    }
  }
)

// 监听窗口大小变化
const handleResize = () => {
  const width = window.innerWidth
  const mobile = width < 768
  
  store.dispatch('ui/setMobile', mobile)
  
  // 移动端自动折叠侧边栏
  if (mobile && !sidebarCollapsed.value) {
    store.dispatch('ui/toggleSidebar')
  }
}

// 生命周期
onMounted(() => {
  // 初始化响应式状态
  handleResize()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  
  // 监听主内容区滚动
  const mainElement = document.querySelector('.layout-main')
  if (mainElement) {
    mainElement.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  const mainElement = document.querySelector('.layout-main')
  if (mainElement) {
    mainElement.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  overflow: hidden;
  background-color: var(--bg-color-page);
  transition: var(--transition-base);
}

.layout-main {
  position: fixed;
  top: var(--header-height);
  left: 120px; /* 主菜单宽度 */
  right: 0;
  bottom: var(--footer-height);
  overflow-y: auto;
  overflow-x: hidden;
  background-color: var(--bg-color-page);
  transition: left var(--transition-duration-base) ease;
}

/* 当子菜单展开时，主内容区左边距增加 */
.layout-main.sidebar-submenu-open {
  left: 279px; /* 主菜单120px + 子菜单159px */
}

/* 移动端时主内容区占满屏幕 */
.layout-main.is-mobile {
  left: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .layout-main {
    left: 0;
  }

  .layout-main.sidebar-submenu-open {
    left: 0;
  }
}

.main-content {
  min-height: calc(100vh - var(--header-height) - var(--footer-height));
  padding: var(--spacing-lg);
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-popper);
  backdrop-filter: blur(2px);
}

/* 回到顶部按钮 */
.back-top {
  position: fixed;
  right: var(--spacing-xl);
  bottom: calc(var(--footer-height) + var(--spacing-xl));
  width: 48px;
  height: 48px;
  background-color: var(--color-primary);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--box-shadow-base);
  transition: var(--transition-base);
  z-index: var(--z-index-top);
}

.back-top:hover {
  background-color: var(--color-primary-dark-1);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-dark);
}

.back-top .el-icon {
  font-size: 20px;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-duration-base) var(--transition-function-ease-in-out);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all var(--transition-duration-base) var(--transition-function-ease-in-out);
}

.slide-left-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right-enter-from {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(30px);
  opacity: 0;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-md);
  }
  
  .back-top {
    right: var(--spacing-lg);
    bottom: var(--spacing-lg);
    width: 40px;
    height: 40px;
  }
  
  .back-top .el-icon {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-sm);
  }
}

/* 主题适配 */
.layout-container.theme-dark {
  background-color: var(--bg-color-page);
}

.layout-container.theme-dark .mobile-overlay {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
