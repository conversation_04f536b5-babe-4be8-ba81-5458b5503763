/**
 * Element Plus 组件样式覆盖
 * 解决组件默认样式与项目设计系统的冲突问题
 *
 * 主要解决：
 * - 输入框双重边框问题
 * - 组件样式与全局样式冲突
 * - 统一的视觉风格
 */

/* ===== 全局 Element Plus 内部元素重置 ===== */

/* 强制重置所有 Element Plus 内部的 input 元素 */
[class*="el-"] input,
[class*="el-"] input[type="text"],
[class*="el-"] input[type="password"],
[class*="el-"] input[type="email"],
[class*="el-"] input[type="number"],
[class*="el-"] input[type="tel"],
[class*="el-"] input[type="url"],
[class*="el-"] input[type="search"],
[class*="el-"] input[type="date"],
[class*="el-"] input[type="time"],
[class*="el-"] input[type="datetime-local"] {
  /* 完全移除所有可能影响的样式 */
  border: none !important;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
  /* 重置尺寸和间距 */
  padding: 0 !important;
  margin: 0 !important;
  /* 重置边框相关属性 */
  border-style: none !important;
  border-width: 0 !important;
  border-color: transparent !important;
  border-radius: 0 !important;
}

/* 焦点状态也要重置 */
[class*="el-"] input:focus,
[class*="el-"] input[type="text"]:focus,
[class*="el-"] input[type="password"]:focus,
[class*="el-"] input[type="email"]:focus,
[class*="el-"] input[type="number"]:focus,
[class*="el-"] input[type="tel"]:focus,
[class*="el-"] input[type="url"]:focus,
[class*="el-"] input[type="search"]:focus,
[class*="el-"] input[type="date"]:focus,
[class*="el-"] input[type="time"]:focus,
[class*="el-"] input[type="datetime-local"]:focus {
  border: none !important;
  background: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

/* ===== 输入框组件样式覆盖 ===== */

/* el-input 组件 */
.el-input__wrapper {
  /* 移除默认阴影，避免与自定义边框冲突 */
  box-shadow: none !important;
  /* 统一过渡效果 */
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  /* 确保背景色 */
  background-color: #ffffff;
  /* 确保所有输入框都有边框 - 全局默认样式 */
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

/* 输入框内部的原生 input 元素 - 关键修复 */
.el-input__inner {
  /* 完全重置所有可能的样式属性 */
  border: none !important;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
  /* 移除内边距和边距，由外层容器控制 */
  padding: 0 !important;
  margin: 0 !important;
  /* 确保宽度和高度由外层控制 */
  width: 100% !important;
  height: auto !important;
  /* 移除任何可能的边框样式 */
  border-style: none !important;
  border-width: 0 !important;
  border-color: transparent !important;
}

/* 焦点状态 */
.el-input__wrapper.is-focus {
  /* 只在外层容器显示焦点效果 */
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 悬停状态 */
.el-input__wrapper:hover {
  border-color: #c0c4cc;
}

/* 确保焦点状态下内部 input 仍然无样式 */
.el-input__wrapper.is-focus .el-input__inner,
.el-input__wrapper:focus-within .el-input__inner {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 禁用状态 */
.el-input__wrapper.is-disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.el-input__wrapper.is-disabled .el-input__inner {
  background-color: transparent !important;
  color: #c0c4cc;
}

/* ===== 数字输入框组件 ===== */

.el-input-number .el-input__wrapper {
  box-shadow: none !important;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  /* 确保数字输入框有边框 */
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background-color: #ffffff;
}

.el-input-number .el-input__inner {
  /* 完全重置数字输入框内部样式 */
  border: none !important;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  height: auto !important;
  border-style: none !important;
  border-width: 0 !important;
  border-color: transparent !important;
}

.el-input-number .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 数字输入框悬停状态 */
.el-input-number .el-input__wrapper:hover {
  border-color: #c0c4cc;
}

/* ===== 文本域组件 ===== */

.el-textarea__inner {
  /* 移除默认阴影 */
  box-shadow: none !important;
  /* 统一过渡效果 */
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  /* 确保背景色 */
  background-color: #ffffff;
  /* 确保所有文本域都有边框 - 全局默认样式 */
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

.el-textarea__inner:focus {
  /* 焦点状态的边框和阴影效果 */
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
  outline: none;
}

/* 文本域悬停状态 */
.el-textarea__inner:hover {
  border-color: #c0c4cc;
}

/* ===== 选择器组件 ===== */

.el-select .el-input__wrapper {
  box-shadow: none !important;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  /* 确保选择器有边框 */
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background-color: #ffffff;
}

.el-select .el-input__inner {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

.el-select .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 选择器悬停状态 */
.el-select .el-input__wrapper:hover {
  border-color: #c0c4cc;
}

/* ===== 级联选择器组件 ===== */

.el-cascader .el-input__wrapper {
  box-shadow: none !important;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.el-cascader .el-input__inner {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

.el-cascader .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 2px rgba(96, 155, 235, 0.2) !important;
}

/* ===== 按钮组件样式统一 ===== */

.el-button {
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.el-button--primary {
  background-color: var(--color-primary, #609beb);
  border-color: var(--color-primary, #609beb);
}

.el-button--primary:hover {
  background-color: var(--color-primary-light-1, #73a9ee);
  border-color: var(--color-primary-light-1, #73a9ee);
}

/* ===== 表单项样式优化 ===== */

.el-form-item__label {
  font-weight: 500;
  color: #333333;
}

.el-form-item__content {
  position: relative;
}

/* ===== 卡片组件样式 ===== */

.el-card {
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.el-card.is-never-shadow {
  box-shadow: none;
}

.el-card.is-never-shadow .el-card__body {
  padding: 0;
}

/* ===== 响应式适配 ===== */

@media (max-width: 768px) {
  .el-input__wrapper,
  .el-textarea__inner {
    font-size: 16px; /* 防止 iOS 缩放 */
  }
  
  .el-button {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .el-form-item__label {
    text-align: left;
    padding-right: 0;
  }
}

/* ===== 打印样式 ===== */

@media print {
  .el-input__wrapper,
  .el-textarea__inner {
    border: 1px solid #000000 !important;
    box-shadow: none !important;
  }
  
  .el-button {
    display: none;
  }
}
