<!--
  分销佣金页面
  根据开发文档重新开发，实现佣金明细列表展示功能
  API: POST /api/admin/commission/list
-->
<template>
  <div class="shop-commission">
    <!-- 顶部导航 -->
    <TopNav title="分销佣金" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户ID" prop="userId">
                <el-input
                  size="default"
                  v-model="searchForm.userId"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <el-form-item label="上级ID" prop="topId">
                <el-input
                  size="default"
                  v-model="searchForm.topId"
                  placeholder="请输入上级ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <el-form-item label="订单ID" prop="orderId">
                <el-input
                  size="default"
                  v-model="searchForm.orderId"
                  placeholder="请输入订单ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <el-form-item label="交易单号" prop="transactionId">
                <el-input
                  size="default"
                  v-model="searchForm.transactionId"
                  placeholder="请输入交易单号"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="选择城市" prop="cityId">
                <el-cascader
                  size="default"
                  v-model="searchForm.cityId"
                  :options="cityOptions"
                  :props="cascaderProps"
                  placeholder="请选择城市"
                  clearable
                  style="width: 200px"
                  @change="handleCityChange"
                />
              </el-form-item>

              <el-form-item>
                <LbButton type="primary" @click="handleSearch" :loading="loading">
                  <el-icon><Search /></el-icon>
                  搜索
                </LbButton>
                <LbButton @click="handleReset">
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 统计信息 -->
      <!-- <div class="stats-container">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-title">总佣金记录</div>
              <div class="stat-value">{{ total }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-title">总支付金额</div>
              <div class="stat-value">¥{{ totalPayPrice.toFixed(2) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-title">总佣金金额</div>
              <div class="stat-value">¥{{ totalCash.toFixed(2) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-title">平均佣金率</div>
              <div class="stat-value">{{ averageRate }}%</div>
            </div>
          </el-col>
        </el-row>
      </div> -->

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="userId" label="用户ID" width="100" align="center" />
          <el-table-column prop="topId" label="上级ID" width="100" align="center" />
          <el-table-column prop="orderId" label="订单ID" width="100" align="center" />

          <el-table-column prop="transactionId" label="交易单号" width="250" align="center">
            <template #default="{ row }">
              <span style="font-family: monospace; font-size: 12px;">{{ row.transactionId }}</span>
            </template>
          </el-table-column>

          <el-table-column label="支付金额" width="120" align="center">
            <template #default="{ row }">
              <span style="color: #409eff; font-weight: bold;">¥{{ row.payPrice.toFixed(2) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="佣金金额" width="120" align="center">
            <template #default="{ row }">
              <span style="color: #67c23a; font-weight: bold;">¥{{ row.cash.toFixed(2) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="佣金率" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="info">{{ ((row.cash / row.payPrice) * 100).toFixed(2) }}%</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="创建时间" width="180" align="center" />

          <el-table-column label="操作" min-width="120" align="center" fixed="right">
            <template #default="{ row }">
              <LbButton type="primary" size="small" @click="handleDetail(row)">
                详情
              </LbButton>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <LbPage
            :page="searchForm.pageNum"
            :page-size="searchForm.pageSize"
            :total="total"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="佣金详情"
      width="600px"
      :before-close="handleDetailClose"
    >
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="佣金ID">{{ detailData.id }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ detailData.userId }}</el-descriptions-item>
          <el-descriptions-item label="上级ID">{{ detailData.topId }}</el-descriptions-item>
          <el-descriptions-item label="订单ID">{{ detailData.orderId }}</el-descriptions-item>
          <el-descriptions-item label="交易单号" :span="2">
            <span style="font-family: monospace;">{{ detailData.transactionId }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付金额">
            <span style="color: #409eff; font-weight: bold;">¥{{ detailData.payPrice.toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="佣金金额">
            <span style="color: #67c23a; font-weight: bold;">¥{{ detailData.cash.toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="佣金率">
            <el-tag type="info">{{ ((detailData.cash / detailData.payPrice) * 100).toFixed(2) }}%</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ detailData.createTime }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="detailDialogVisible = false">关闭</LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const detailDialogVisible = ref(false)
const detailData = ref(null)

// 城市选择相关
const cityOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true,
  checkStrictly: false,
  expandTrigger: 'hover'
}

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  userId: '',
  topId: '',
  orderId: '',
  transactionId: '',
  cityId: [],
  pageNum: 1,
  pageSize: 10
})

// 计算统计数据
const totalPayPrice = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.payPrice, 0)
})

const totalCash = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.cash, 0)
})

const averageRate = computed(() => {
  if (totalPayPrice.value === 0) return '0.00'
  return ((totalCash.value / totalPayPrice.value) * 100).toFixed(2)
})

// 获取佣金列表
const getTableDataList = async () => {
  try {
    loading.value = true
    console.log('🔍 获取佣金列表，参数:', searchForm)

    // 构建请求参数，只传递有值的参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 只有在搜索时才添加其他参数
    if (searchForm.userId) params.userId = searchForm.userId
    if (searchForm.topId) params.topId = searchForm.topId
    if (searchForm.orderId) params.orderId = searchForm.orderId
    if (searchForm.transactionId) params.transactionId = searchForm.transactionId
    if (searchForm.cityId && searchForm.cityId.length > 0) {
      params.cityIdStr = searchForm.cityId.join(',')
    }

    const response = await proxy.$api.shop.commissionList(params)
    console.log('💰 佣金列表响应:', response)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0
      console.log(`✅ 佣金列表加载成功，共 ${total.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取佣金列表失败')
    }
  } catch (error) {
    console.error('❌ 获取佣金列表失败:', error)
    ElMessage.error('获取佣金列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  searchForm.pageNum = 1
  getTableDataList()
}

// 城市变更处理
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)
  if (value && value.length > 0) {
    const selectedCityId = value[value.length - 1]
    console.log('🏙️ 选中的城市ID:', selectedCityId)
  } else {
    console.log('🏙️ 清空城市选择')
  }
}

// 获取城市数据
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await proxy.$api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

// 重置搜索
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    userId: '',
    topId: '',
    orderId: '',
    transactionId: '',
    cityId: [],
    pageNum: 1,
    pageSize: 10
  })
  getTableDataList()
}

// 分页大小变化
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getTableDataList()
}

// 当前页变化
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 查看详情
const handleDetail = (row) => {
  detailData.value = row
  detailDialogVisible.value = true
}

// 关闭详情弹窗
const handleDetailClose = () => {
  detailDialogVisible.value = false
  detailData.value = null
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 分销佣金页面初始化')
  getCityData()
  getTableDataList()
})
</script>

<style scoped>
.shop-commission {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
}

.stats-container {
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-title {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
}

.table-container {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.detail-content {
  padding: 10px 0;
}

.detail-content .el-descriptions {
  margin-top: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
