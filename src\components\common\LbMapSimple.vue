<!--
  简化版地图组件 - 基于测试页面的成功实现
-->

<template>
  <el-dialog 
    v-model="centerDialogVisible" 
    title="获取经纬度" 
    width="650px" 
    :center="true"
    @close="handleClose"
    @opened="onDialogOpened"
  >
    <div class="dialog-inner">
      <div class="map-search">
        <el-input 
          v-model="searchAddress" 
          placeholder="输入地址" 
          @keydown.enter="searchMapAddr"
        />
        <LbButton 
          size="small" 
          type="primary" 
          @click="searchMapAddr"
        >
          搜 索
        </LbButton>
      </div>
      <div :id="mapContainerId" class="map-container">
        <div v-if="!mapReady" class="map-loading">
          地图加载中...
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmLatLng">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import LbButton from './LbButton.vue'

// Props
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  address: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:dialogVisible', 'selectedLatLng'])

// 响应式数据
const centerDialogVisible = ref(false)
const searchAddress = ref('')
const mapReady = ref(false)
const mapContainerId = ref(`map-container-${Date.now()}`)
const map = ref(null)
const marker = ref(null)
const geocoder = ref(null)
const latLng = ref({
  lat: 39.916527,
  lng: 116.397128
})

// 对话框打开后初始化地图
const onDialogOpened = () => {
  console.log('对话框已打开，开始初始化地图')
  setTimeout(() => {
    initMap()
  }, 100)
}

// 初始化地图
const initMap = () => {
  console.log('开始初始化地图...')

  // 检查API
  if (typeof qq === 'undefined' || !qq.maps) {
    console.error('地图API未加载，请检查腾讯地图API是否正确引入')
    ElMessage.error('地图API未加载，请检查网络连接')
    return
  }

  // 检查LatLng构造函数
  if (typeof qq.maps.LatLng !== 'function') {
    console.error('qq.maps.LatLng构造函数不可用，API可能未完全加载')
    ElMessage.error('地图API加载不完整，请刷新页面重试')
    return
  }

  // 获取容器
  const container = document.getElementById(mapContainerId.value)
  if (!container) {
    console.error('地图容器未找到:', mapContainerId.value)
    ElMessage.error('地图容器初始化失败')
    return
  }

  console.log('容器尺寸:', container.offsetWidth, 'x', container.offsetHeight)

  // 确保容器有尺寸
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    console.warn('地图容器尺寸为0，延迟重试')
    setTimeout(() => initMap(), 200)
    return
  }
  
  try {
    // 检查qq.maps.LatLng构造函数是否可用
    if (typeof qq.maps.LatLng !== 'function') {
      console.error('qq.maps.LatLng构造函数不可用')
      ElMessage.error('地图API未正确加载')
      return
    }

    // 创建地图 - 使用和测试页面完全相同的逻辑
    const center = new qq.maps.LatLng(latLng.value.lat, latLng.value.lng)
    const mapInstance = new qq.maps.Map(container, {
      center: center,
      zoom: 13
    })

    map.value = mapInstance

    // 添加默认标记
    const defaultMarker = new qq.maps.Marker({
      position: center,
      map: mapInstance
    })
    marker.value = defaultMarker
    
    // 添加点击事件
    qq.maps.event.addListener(mapInstance, 'click', (event) => {
      // 清除之前的标记
      if (marker.value) {
        marker.value.setMap(null)
      }
      
      const { lat, lng } = event.latLng
      latLng.value = { lat, lng }
      
      // 创建新标记
      marker.value = new qq.maps.Marker({
        position: new qq.maps.LatLng(lat, lng),
        map: mapInstance
      })
      
      // 显示信息窗口
      const info = new qq.maps.InfoWindow({
        map: mapInstance
      })
      info.open()
      info.setContent(`
        <div style="margin:10px;">
          <p>纬度：${lat}</p>
          <p>经度：${lng}</p>
        </div>
      `)
      info.setPosition(new qq.maps.LatLng(lat, lng))
    })
    
    mapReady.value = true
    console.log('地图初始化成功')
    
  } catch (error) {
    console.error('地图初始化失败:', error)
    ElMessage.error('地图初始化失败')
  }
}

// 搜索地址
const searchMapAddr = () => {
  if (!searchAddress.value.trim()) {
    ElMessage.warning('请输入搜索地址')
    return
  }
  
  if (!map.value) {
    ElMessage.error('地图未初始化')
    return
  }
  
  try {
    if (!geocoder.value) {
      geocoder.value = new qq.maps.Geocoder()
    }
    
    geocoder.value.setComplete((result) => {
      const { lat, lng } = result.detail.location
      latLng.value = { lat, lng }
      
      map.value.setCenter(result.detail.location)
      
      // 清除之前的标记
      if (marker.value) {
        marker.value.setMap(null)
      }
      
      // 创建新标记
      marker.value = new qq.maps.Marker({
        map: map.value,
        position: result.detail.location
      })
      
      // 显示信息窗口
      const info = new qq.maps.InfoWindow({
        map: map.value
      })
      info.open()
      info.setContent(`
        <div style="margin:10px;">
          <p>纬度：${lat}</p>
          <p>经度：${lng}</p>
        </div>
      `)
      info.setPosition(new qq.maps.LatLng(lat, lng))
    })
    
    geocoder.value.setError(() => {
      ElMessage.error('请输入包含市级的地址！')
    })
    
    geocoder.value.getLocation(searchAddress.value)
    
  } catch (error) {
    console.error('地址搜索失败:', error)
    ElMessage.error('地址搜索失败')
  }
}

// 确认选择
const confirmLatLng = () => {
  centerDialogVisible.value = false
  emit('selectedLatLng', latLng.value)
}

// 关闭对话框
const handleClose = () => {
  centerDialogVisible.value = false
  emit('update:dialogVisible', false)
}

// 监听器
watch(() => props.dialogVisible, (newValue) => {
  if (newValue) {
    centerDialogVisible.value = true
    searchAddress.value = props.address || ''
    mapReady.value = false
  }
})

watch(centerDialogVisible, (val) => {
  if (!val) {
    emit('update:dialogVisible', false)
  }
})
</script>

<style scoped>
.map-container {
  width: 500px;
  height: 400px;
  margin: 0 auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f5f5f5;
  position: relative;
}

.map-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  font-size: 14px;
}

.dialog-inner {
  padding: 10px 0;
}

.map-search {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  gap: 10px;
}

.map-search .el-input {
  width: 300px;
}

.dialog-footer {
  text-align: right;
}

/* 确保地图API样式不被覆盖 */
.map-container :deep(img) {
  max-width: none !important;
}

.map-container :deep(div) {
  box-sizing: content-box !important;
}
</style>
