/**
 * 弹窗层级修复工具
 * 专门解决页面缩放时弹窗被侧边栏压住的问题
 */

// 高优先级z-index值
const HIGH_Z_INDEX = {
  overlay: 10001,
  dialog: 10002,
  message: 10003,
  messageBox: 10004,
  notification: 10005,
  drawer: 10006,
  popover: 10007,
  tooltip: 10008
}

/**
 * 检查是否处于缩放状态
 */
function isZoomed() {
  const layoutContainer = document.querySelector('.layout-container')
  if (!layoutContainer) return false
  
  return layoutContainer.classList.contains('zoom-small') ||
         layoutContainer.classList.contains('zoom-large') ||
         layoutContainer.classList.contains('zoom-extra-large')
}

/**
 * 强制设置元素的z-index
 */
function forceZIndex(element, zIndex) {
  if (element) {
    element.style.setProperty('z-index', zIndex, 'important')
  }
}

/**
 * 修复所有现有的弹窗元素
 */
function fixExistingDialogs() {
  if (!isZoomed()) return
  
  // 修复遮罩层
  const overlays = document.querySelectorAll('.el-overlay')
  overlays.forEach(overlay => {
    forceZIndex(overlay, HIGH_Z_INDEX.overlay)
  })
  
  // 修复对话框
  const dialogs = document.querySelectorAll('.el-dialog')
  dialogs.forEach(dialog => {
    forceZIndex(dialog, HIGH_Z_INDEX.dialog)
  })
  
  // 修复消息框
  const messages = document.querySelectorAll('.el-message')
  messages.forEach(message => {
    forceZIndex(message, HIGH_Z_INDEX.message)
  })
  
  // 修复确认框
  const messageBoxes = document.querySelectorAll('.el-message-box')
  messageBoxes.forEach(box => {
    forceZIndex(box, HIGH_Z_INDEX.messageBox)
  })
  
  // 修复通知
  const notifications = document.querySelectorAll('.el-notification')
  notifications.forEach(notification => {
    forceZIndex(notification, HIGH_Z_INDEX.notification)
  })
  
  // 修复抽屉
  const drawers = document.querySelectorAll('.el-drawer')
  drawers.forEach(drawer => {
    forceZIndex(drawer, HIGH_Z_INDEX.drawer)
  })
  
  console.log('🔧 已修复现有弹窗的z-index')
}

/**
 * 监听DOM变化，自动修复新添加的弹窗
 */
function observeDialogs() {
  const observer = new MutationObserver((mutations) => {
    if (!isZoomed()) return
    
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // 检查是否是弹窗相关元素
          if (node.classList) {
            if (node.classList.contains('el-overlay')) {
              forceZIndex(node, HIGH_Z_INDEX.overlay)
            }
            if (node.classList.contains('el-dialog')) {
              forceZIndex(node, HIGH_Z_INDEX.dialog)
            }
            if (node.classList.contains('el-message')) {
              forceZIndex(node, HIGH_Z_INDEX.message)
            }
            if (node.classList.contains('el-message-box')) {
              forceZIndex(node, HIGH_Z_INDEX.messageBox)
            }
            if (node.classList.contains('el-notification')) {
              forceZIndex(node, HIGH_Z_INDEX.notification)
            }
            if (node.classList.contains('el-drawer')) {
              forceZIndex(node, HIGH_Z_INDEX.drawer)
            }
          }
          
          // 检查子元素
          const childOverlays = node.querySelectorAll && node.querySelectorAll('.el-overlay')
          if (childOverlays) {
            childOverlays.forEach(overlay => forceZIndex(overlay, HIGH_Z_INDEX.overlay))
          }
          
          const childDialogs = node.querySelectorAll && node.querySelectorAll('.el-dialog')
          if (childDialogs) {
            childDialogs.forEach(dialog => forceZIndex(dialog, HIGH_Z_INDEX.dialog))
          }
        }
      })
    })
  })
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
  
  return observer
}

/**
 * 监听缩放状态变化
 */
function observeZoomChanges() {
  let currentZoomState = isZoomed()
  
  const checkZoomState = () => {
    const newZoomState = isZoomed()
    if (newZoomState !== currentZoomState) {
      currentZoomState = newZoomState
      if (currentZoomState) {
        // 进入缩放状态，修复所有弹窗
        setTimeout(fixExistingDialogs, 100)
      }
      console.log(`🔄 缩放状态变化: ${currentZoomState ? '缩放模式' : '正常模式'}`)
    }
  }
  
  // 定期检查
  const intervalId = setInterval(checkZoomState, 500)
  
  // 监听类名变化
  const layoutContainer = document.querySelector('.layout-container')
  if (layoutContainer) {
    const observer = new MutationObserver(checkZoomState)
    observer.observe(layoutContainer, {
      attributes: true,
      attributeFilter: ['class']
    })
    
    return () => {
      clearInterval(intervalId)
      observer.disconnect()
    }
  }
  
  return () => clearInterval(intervalId)
}

/**
 * 初始化弹窗修复
 */
function initDialogFix() {
  console.log('🔧 初始化弹窗层级修复')
  
  // 立即修复现有弹窗
  fixExistingDialogs()
  
  // 开始监听
  const dialogObserver = observeDialogs()
  const zoomObserver = observeZoomChanges()
  
  // 定期强制修复（保险措施）
  const forceFixInterval = setInterval(() => {
    if (isZoomed()) {
      fixExistingDialogs()
    }
  }, 2000)
  
  // 返回清理函数
  return () => {
    if (dialogObserver) {
      dialogObserver.disconnect()
    }
    if (zoomObserver) {
      zoomObserver()
    }
    clearInterval(forceFixInterval)
    console.log('🧹 弹窗修复已清理')
  }
}

/**
 * 手动修复弹窗（可以在打开弹窗时调用）
 */
function manualFixDialog() {
  setTimeout(() => {
    fixExistingDialogs()
  }, 100)
}

// 导出函数
export {
  initDialogFix,
  manualFixDialog,
  fixExistingDialogs,
  isZoomed,
  HIGH_Z_INDEX
}

// 默认导出
export default initDialogFix
