/**
 * 账号设置模块Mock数据
 */

import Mock from 'mockjs'
import { successResponse, createCrudMock } from '../utils.js'

const accountList = Mock.mock({
  'list|10-30': [{
    'id|+1': 1,
    'username': '@word(5, 10)',
    'nickname': '@cname',
    'email': '@email',
    'role': '@pick(["admin", "manager", "operator"])',
    'status|1': [0, 1],
    'createTime': '@datetime'
  }]
}).list

createCrudMock('/api/account', accountList)

console.log('账号设置模块Mock数据已加载')
export { accountList }
