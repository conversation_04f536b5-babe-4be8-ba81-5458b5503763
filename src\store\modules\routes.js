/**
 * 路由状态管理模块
 * 
 * 功能：
 * - 动态路由生成
 * - 权限路由过滤
 * - 菜单路由管理
 */

import { asyncRoutes } from '@/router/routes/async'
import { constantRoutes } from '@/router/routes/constant'
import { filterAsyncRoutes } from '@/router/guards'

const state = {
  // 所有路由
  routes: [],
  // 动态添加的路由
  addRoutes: [],
  // 菜单路由
  menuRoutes: []
}

const mutations = {
  // 设置路由
  SET_ROUTES(state, routes) {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  
  // 设置菜单路由
  SET_MENU_ROUTES(state, routes) {
    state.menuRoutes = routes
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.routes = []
    state.addRoutes = []
    state.menuRoutes = []
  }
}

const actions = {
  // 生成路由
  generateRoutes({ commit }, roles) {
    return new Promise(resolve => {
      let accessedRoutes

      // 确保roles是数组类型
      const userRoles = Array.isArray(roles) ? roles : []

      if (userRoles.includes('super_admin')) {
        // 超级管理员拥有所有权限
        accessedRoutes = asyncRoutes || []
      } else {
        // 根据角色过滤路由
        accessedRoutes = filterAsyncRoutes(asyncRoutes, userRoles)
      }

      commit('SET_ROUTES', accessedRoutes)
      commit('SET_MENU_ROUTES', generateMenuRoutes(accessedRoutes))

      resolve(accessedRoutes)
    })
  },
  
  // 重置状态
  resetState({ commit }) {
    commit('RESET_STATE')
  }
}

const getters = {
  // 所有路由
  routes: state => state.routes,
  
  // 动态路由
  addRoutes: state => state.addRoutes,
  
  // 菜单路由
  menuRoutes: state => state.menuRoutes
}

// 生成菜单路由
function generateMenuRoutes(routes) {
  const menuRoutes = []
  
  routes.forEach(route => {
    // 跳过隐藏的路由
    if (route.meta && route.meta.hidden) {
      return
    }
    
    const menuRoute = {
      path: route.path,
      name: route.name,
      meta: route.meta,
      children: []
    }
    
    // 处理子路由
    if (route.children && route.children.length > 0) {
      const childrenRoutes = generateMenuRoutes(route.children)
      if (childrenRoutes.length > 0) {
        menuRoute.children = childrenRoutes
      }
    }
    
    menuRoutes.push(menuRoute)
  })
  
  return menuRoutes
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
