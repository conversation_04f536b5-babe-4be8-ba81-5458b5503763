/**
 * 统计模块 - V2版本
 * 按照API封装规范文档实现统计相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取首页统计概览
   * @param {Object} querys 查询参数
   * @param {string} querys.date 查询日期，格式：YYYY-MM-DD
   * @returns {Promise} 返回统计概览数据
   */
  dashboardOverview(querys) {
    console.log('📊 首页统计概览API-V2请求参数:', querys)
    return get('/api/admin/statistics/dashboard/overview', querys)
  },

  /**
   * 获取订单统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.type 统计类型，1日统计，2月统计，3年统计
   * @returns {Promise} 返回订单统计数据
   */
  orderStatistics(querys) {
    console.log('📈 订单统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/order', querys)
  },

  /**
   * 获取用户统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.type 统计类型，1日统计，2月统计，3年统计
   * @returns {Promise} 返回用户统计数据
   */
  userStatistics(querys) {
    console.log('👥 用户统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/user', querys)
  },

  /**
   * 获取收入统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.type 统计类型，1日统计，2月统计，3年统计
   * @returns {Promise} 返回收入统计数据
   */
  revenueStatistics(querys) {
    console.log('💰 收入统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/revenue', querys)
  },

  /**
   * 获取师傅统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.type 统计类型，1日统计，2月统计，3年统计
   * @returns {Promise} 返回师傅统计数据
   */
  technicianStatistics(querys) {
    console.log('👨‍🔧 师傅统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/technician', querys)
  },

  /**
   * 获取服务统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.categoryId 分类ID，非必填
   * @returns {Promise} 返回服务统计数据
   */
  serviceStatistics(querys) {
    console.log('🛠️ 服务统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/service', querys)
  },

  /**
   * 获取地区统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.level 地区级别，1省份，2城市，3区县
   * @returns {Promise} 返回地区统计数据
   */
  regionStatistics(querys) {
    console.log('🗺️ 地区统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/region', querys)
  },

  /**
   * 获取分销统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.distributorId 分销商ID，非必填
   * @returns {Promise} 返回分销统计数据
   */
  distributionStatistics(querys) {
    console.log('📊 分销统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/distribution', querys)
  },

  /**
   * 获取营销统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.type 营销类型，1优惠券，2代金券
   * @returns {Promise} 返回营销统计数据
   */
  marketingStatistics(querys) {
    console.log('🎯 营销统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/marketing', querys)
  },

  /**
   * 获取实时统计
   * @returns {Promise} 返回实时统计数据
   */
  realtimeStatistics() {
    console.log('⚡ 实时统计API-V2请求')
    return get('/api/admin/statistics/realtime')
  },

  /**
   * 获取排行榜统计
   * @param {Object} querys 查询参数
   * @param {string} querys.type 排行类型，user/technician/service/region
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.limit 返回数量，默认10
   * @returns {Promise} 返回排行榜数据
   */
  rankingStatistics(querys) {
    console.log('🏆 排行榜统计API-V2请求参数:', querys)
    return get('/api/admin/statistics/ranking', querys)
  },

  /**
   * 获取趋势分析
   * @param {Object} querys 查询参数
   * @param {string} querys.metric 指标类型，order/revenue/user/technician
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {string} querys.period 时间周期，day/week/month
   * @returns {Promise} 返回趋势分析数据
   */
  trendAnalysis(querys) {
    console.log('📈 趋势分析API-V2请求参数:', querys)
    return get('/api/admin/statistics/trend', querys)
  },

  /**
   * 获取对比分析
   * @param {Object} querys 查询参数
   * @param {string} querys.metric 指标类型
   * @param {string} querys.currentStart 当前期间开始时间
   * @param {string} querys.currentEnd 当前期间结束时间
   * @param {string} querys.compareStart 对比期间开始时间
   * @param {string} querys.compareEnd 对比期间结束时间
   * @returns {Promise} 返回对比分析数据
   */
  compareAnalysis(querys) {
    console.log('⚖️ 对比分析API-V2请求参数:', querys)
    return get('/api/admin/statistics/compare', querys)
  },

  /**
   * 导出统计报表
   * @param {Object} querys 导出参数
   * @param {string} querys.type 报表类型
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {string} querys.format 导出格式，excel/pdf/csv
   * @returns {Promise} 返回导出结果
   */
  exportReport(querys) {
    console.log('📤 导出统计报表API-V2请求参数:', querys)
    return post('/api/admin/statistics/export', querys)
  },

  /**
   * 获取自定义统计
   * @param {Object} querys 查询参数
   * @param {Array} querys.metrics 指标列表
   * @param {Array} querys.dimensions 维度列表
   * @param {Array} querys.filters 过滤条件
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @returns {Promise} 返回自定义统计数据
   */
  customStatistics(querys) {
    console.log('🎛️ 自定义统计API-V2请求参数:', querys)
    return post('/api/admin/statistics/custom', querys)
  },

  /**
   * 保存统计配置
   * @param {Object} querys 配置参数
   * @param {string} querys.name 配置名称
   * @param {Object} querys.config 统计配置
   * @returns {Promise} 返回保存结果
   */
  saveStatisticsConfig(querys) {
    console.log('💾 保存统计配置API-V2请求:', querys)
    return post('/api/admin/statistics/config/save', querys)
  },

  /**
   * 获取统计配置列表
   * @returns {Promise} 返回配置列表
   */
  statisticsConfigList() {
    console.log('📋 统计配置列表API-V2请求')
    return get('/api/admin/statistics/config/list')
  },

  /**
   * 删除统计配置
   * @param {Object} querys 删除参数
   * @param {number} querys.id 配置ID
   * @returns {Promise} 返回删除结果
   */
  deleteStatisticsConfig(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('配置ID不能为空'))
    }

    console.log('🗑️ 删除统计配置API-V2请求:', querys)
    return post(`/api/admin/statistics/config/delete/${querys.id}`)
  }
}
