<template>
  <div class="input-border-test">
    <div class="test-container">
      <h2>输入框边框测试页面</h2>
      
      <div class="test-section">
        <h3>Element Plus 输入框组件</h3>
        
        <div class="test-row">
          <label>普通输入框：</label>
          <el-input v-model="testData.text" placeholder="请输入文本" />
        </div>
        
        <div class="test-row">
          <label>密码输入框：</label>
          <el-input v-model="testData.password" type="password" placeholder="请输入密码" />
        </div>
        
        <div class="test-row">
          <label>数字输入框：</label>
          <el-input-number v-model="testData.number" :min="0" :max="100" />
        </div>
        
        <div class="test-row">
          <label>选择器：</label>
          <el-select v-model="testData.select" placeholder="请选择">
            <el-option label="选项1" value="1" />
            <el-option label="选项2" value="2" />
          </el-select>
        </div>
        
        <div class="test-row">
          <label>文本域：</label>
          <el-input v-model="testData.textarea" type="textarea" placeholder="请输入多行文本" />
        </div>
      </div>
      
      <div class="test-section">
        <h3>原生 HTML 输入框</h3>
        
        <div class="test-row">
          <label>原生文本输入框：</label>
          <input type="text" v-model="testData.nativeText" placeholder="原生输入框" />
        </div>
        
        <div class="test-row">
          <label>原生数字输入框：</label>
          <input type="number" v-model="testData.nativeNumber" placeholder="原生数字输入框" />
        </div>
        
        <div class="test-row">
          <label>原生文本域：</label>
          <textarea v-model="testData.nativeTextarea" placeholder="原生文本域"></textarea>
        </div>
      </div>
      
      <div class="test-section">
        <h3>状态测试</h3>
        
        <div class="test-row">
          <label>禁用状态：</label>
          <el-input v-model="testData.disabled" placeholder="禁用输入框" disabled />
        </div>
        
        <div class="test-row">
          <label>只读状态：</label>
          <el-input v-model="testData.readonly" placeholder="只读输入框" readonly />
        </div>
      </div>
      
      <div class="test-instructions">
        <h3>测试说明</h3>
        <ul>
          <li>检查所有输入框是否只有单一边框</li>
          <li>点击输入框获得焦点时，应该只有外层显示焦点效果</li>
          <li>悬停时应该有统一的悬停效果</li>
          <li>禁用和只读状态应该有正确的视觉反馈</li>
          <li>原生输入框应该保持原有样式，不受 Element Plus 影响</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'

const testData = reactive({
  text: '',
  password: '',
  number: 0,
  select: '',
  textarea: '',
  nativeText: '',
  nativeNumber: 0,
  nativeTextarea: '',
  disabled: '禁用状态文本',
  readonly: '只读状态文本'
})
</script>

<style scoped>
.input-border-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-container {
  background: #ffffff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section {
  margin-bottom: 32px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.test-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.test-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.test-row:last-child {
  margin-bottom: 0;
}

.test-row label {
  min-width: 120px;
  font-weight: 500;
  color: #606266;
}

.test-row .el-input,
.test-row .el-input-number,
.test-row .el-select {
  flex: 1;
  max-width: 300px;
}

.test-row input,
.test-row textarea {
  flex: 1;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

.test-row input:focus,
.test-row textarea:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.test-instructions {
  background: #e8f4fd;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  padding: 16px;
}

.test-instructions h3 {
  margin: 0 0 12px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
}

.test-instructions ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  font-size: 13px;
  line-height: 1.6;
}

.test-instructions li {
  margin-bottom: 4px;
}
</style>
