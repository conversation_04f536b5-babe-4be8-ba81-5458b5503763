<!--
  合伙人推广订单页面
  参考MarketList.vue的实现模式，按照快速开发指南重构
  对应API: /api/admin/partner/orders
-->

<template>
  <div class="market-partner-orders">
    <!-- 顶部导航 -->
    <TopNav title="合伙人推广订单" />

    <div class="content-container">
      <!-- 返回按钮 -->
      <div class="back-button-container">
        <LbButton
          size="default"
          icon="ArrowLeft"
          @click="handleBack"
        >
          返回合伙人管理
        </LbButton>
      </div>

      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户ID" prop="userId">
                <el-input
                  size="default"
                  v-model="searchForm.userId"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="订单类型" prop="type">
                <el-select
                  size="default"
                  v-model="searchForm.type"
                  placeholder="请选择订单类型"
                  clearable
                  style="width: 140px"
                >
                  <el-option label="全部" :value="0" />
                  <el-option label="用户订单" :value="1" />
                  <el-option label="师傅订单" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton type="primary" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton @click="handleReset">
                  重置
                </LbButton>
                <LbButton type="success" @click="handleExport" :loading="exportLoading">
                  导出
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="id" label="订单ID" width="80" align="center" />
          <el-table-column prop="orderCode" label="订单号" width="200" align="center" show-overflow-tooltip />
          <el-table-column prop="goodsName" label="商品名称" width="150" align="center" show-overflow-tooltip />
          <el-table-column prop="goodsCover" label="商品图片" width="80" align="center">
            <template #default="scope">
              <el-image
                style="width: 40px; height: 40px; border-radius: 4px;"
                :src="scope.row.goodsCover"
                fit="cover"
                :preview-src-list="[scope.row.goodsCover]"
              />
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="用户昵称" width="120" align="center" />
          <el-table-column prop="phone" label="用户手机" width="130" align="center" />
          <el-table-column prop="payPrice" label="支付金额" width="100" align="center">
            <template #default="scope">
              <span class="pay-amount">¥{{ scope.row.payPrice.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="coachServicePrice" label="服务费" width="100" align="center">
            <template #default="scope">
              <span class="service-price">¥{{ scope.row.coachServicePrice.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payType" label="支付方式" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getPayTypeTag(scope.row.payType)">
                {{ getPayTypeName(scope.row.payType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="refundStatus" label="退款状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.refundStatus === 0 ? 'success' : 'warning'">
                {{ scope.row.refundStatus === 0 ? '未退款' : '已退款' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isComment" label="评价状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getCommentTag(scope.row.isComment)">
                {{ getCommentText(scope.row.isComment) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" align="center" show-overflow-tooltip />
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="scope">
              <LbButton size="small" type="primary" @click="handleView(scope.row)">
                查看详情
              </LbButton>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      title="订单详情"
      v-model="detailDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单ID">{{ currentDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ currentDetail.orderCode }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ currentDetail.goodsName }}</el-descriptions-item>
          <el-descriptions-item label="商品ID">{{ currentDetail.goodsId }}</el-descriptions-item>
          <el-descriptions-item label="用户昵称">{{ currentDetail.nickName }}</el-descriptions-item>
          <el-descriptions-item label="用户手机">{{ currentDetail.phone }}</el-descriptions-item>
          <el-descriptions-item label="支付金额">
            <span class="pay-amount">¥{{ currentDetail.payPrice.toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="服务费">
            <span class="service-price">¥{{ currentDetail.coachServicePrice.toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <el-tag :type="getPayTypeTag(currentDetail.payType)">
              {{ getPayTypeName(currentDetail.payType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="退款状态">
            <el-tag :type="currentDetail.refundStatus === 0 ? 'success' : 'warning'">
              {{ currentDetail.refundStatus === 0 ? '未退款' : '已退款' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="评价状态">
            <el-tag :type="getCommentTag(currentDetail.isComment)">
              {{ getCommentText(currentDetail.isComment) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentDetail.createTime }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;">
          <h4>商品图片</h4>
          <el-image
            style="width: 100px; height: 100px; border-radius: 8px; margin-top: 10px;"
            :src="currentDetail.goodsCover"
            fit="cover"
            :preview-src-list="[currentDetail.goodsCover]"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="detailDialogVisible = false">关闭</LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()

// 响应式数据
const searchFormRef = ref()
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const detailDialogVisible = ref(false)
const currentDetail = ref(null)

// 搜索表单
const searchForm = reactive({
  userId: '',
  type: '',
  pageNum: 1,
  pageSize: 10
})

// 获取合伙人推广订单列表
const getPartnerOrdersList = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      userId: searchForm.userId || undefined,
      type: searchForm.type !== '' ? searchForm.type : undefined
    }

    const response = await proxy.$api.market.partnerOrdersList(params)
    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0
    } else {
      ElMessage.error(response.msg || '获取推广订单列表失败')
    }
  } catch (error) {
    console.error('获取推广订单列表失败:', error)
    ElMessage.error('获取推广订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchForm.pageNum = 1
  getPartnerOrdersList()
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    userId: '',
    type: '',
    pageNum: 1,
    pageSize: 10
  })
  getPartnerOrdersList()
}

// 查看详情
const handleView = (row) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

// 导出功能
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出合伙人推广订单')

    // 构建导出参数（排除分页参数）
    const exportParams = {}
    if (searchForm.userId) exportParams.userId = searchForm.userId
    if (searchForm.type !== '') exportParams.type = searchForm.type

    // 获取当前时间作为文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '')
    const filename = `合伙人推广订单_${timestamp}.xlsx`

    // 构建下载URL - 使用环境变量
    const downloadUrl = `${import.meta.env.VITE_API_BASE_URL || ''}/api/admin/partner/orders/export`

    // 创建下载链接
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    link.target = '_blank'

    // 添加token到请求头（如果需要）
    const token = sessionStorage.getItem('minitk')
    if (token) {
      // 对于文件下载，我们需要在URL中添加token参数
      const params = new URLSearchParams(exportParams)
      params.append('token', encodeURIComponent(token))
      link.href = `${downloadUrl}?${params.toString()}`
    } else if (Object.keys(exportParams).length > 0) {
      // 如果没有token但有其他参数
      const params = new URLSearchParams(exportParams)
      link.href = `${downloadUrl}?${params.toString()}`
    }

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('导出成功')
    console.log('✅ 合伙人推广订单导出成功')
  } catch (error) {
    console.error('❌ 导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 返回合伙人管理
const handleBack = () => {
  router.push('/market/partner')
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getPartnerOrdersList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getPartnerOrdersList()
}

// 获取支付方式名称
const getPayTypeName = (payType) => {
  const payTypes = {
    '1': '微信支付',
    '2': '支付宝',
    '7': '余额支付'
  }
  return payTypes[payType] || '未知'
}

// 获取支付方式标签类型
const getPayTypeTag = (payType) => {
  const tagTypes = {
    '1': 'success',
    '2': 'primary',
    '7': 'warning'
  }
  return tagTypes[payType] || 'info'
}

// 获取评价状态文本
const getCommentText = (isComment) => {
  if (isComment === null) return '未知'
  return isComment === 1 ? '已评价' : '未评价'
}

// 获取评价状态标签类型
const getCommentTag = (isComment) => {
  if (isComment === null) return 'info'
  return isComment === 1 ? 'success' : 'warning'
}

// 页面初始化
onMounted(() => {
  // 从URL参数中获取userId
  const userId = route.query.userId
  if (userId) {
    searchForm.userId = userId
    console.log('🔗 从URL参数获取userId:', userId)
  }
  getPartnerOrdersList()
})
</script>

<style scoped>
.market-partner-orders {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button-container {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.search-form-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.pay-amount {
  font-weight: 600;
  color: #67c23a;
  font-size: 14px;
}

.service-price {
  font-weight: 600;
  color: #e6a23c;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
