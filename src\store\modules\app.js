/**
 * 应用状态管理模块
 * 
 * 功能：
 * - 应用配置管理
 * - 系统设置
 * - 全局状态
 */

const state = {
  // 应用设置
  settings: {
    // 应用名称
    appName: '今师傅',
    // 应用描述
    appDescription: 'Vue3后台管理系统',
    // 版权信息
    copyright: '© 2025 今师傅. All rights reserved.',
    // 是否显示Logo
    showLogo: true,
    // 是否显示版权
    showCopyright: true,
    // 默认语言
    defaultLanguage: 'zh-CN',
    // 支持的语言
    supportedLanguages: [
      { code: 'zh-CN', name: '简体中文' },
      { code: 'en-US', name: 'English' }
    ]
  },
  
  // 系统信息
  systemInfo: {
    // 版本号
    version: '3.0.0',
    // 构建时间
    buildTime: '',
    // 环境
    environment: import.meta.env.MODE,
    // API地址
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api'
  },
  
  // 全局配置
  config: {
    // 请求超时时间
    requestTimeout: 10000,
    // 上传文件大小限制（MB）
    uploadSizeLimit: 10,
    // 支持的文件类型
    supportedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
    // 分页配置
    pagination: {
      pageSizes: [10, 20, 50, 100],
      defaultPageSize: 20
    }
  },
  
  // 功能开关
  features: {
    // 是否启用多语言
    enableI18n: true,
    // 是否启用主题切换
    enableThemeSwitch: true,
    // 是否启用全屏
    enableFullscreen: true,
    // 是否启用搜索
    enableSearch: true,
    // 是否启用通知
    enableNotification: true
  }
}

const mutations = {
  // 设置应用设置
  SET_APP_SETTING(state, { key, value }) {
    if (state.settings.hasOwnProperty(key)) {
      state.settings[key] = value
    }
  },
  
  // 设置系统信息
  SET_SYSTEM_INFO(state, info) {
    state.systemInfo = { ...state.systemInfo, ...info }
  },
  
  // 设置全局配置
  SET_CONFIG(state, { key, value }) {
    if (state.config.hasOwnProperty(key)) {
      state.config[key] = value
    }
  },
  
  // 设置功能开关
  SET_FEATURE(state, { key, value }) {
    if (state.features.hasOwnProperty(key)) {
      state.features[key] = value
    }
  },
  
  // 重置状态
  RESET_STATE(state) {
    // 保留默认设置，不重置
  }
}

const actions = {
  // 加载应用设置
  loadSettings({ commit }) {
    return new Promise(resolve => {
      // 从本地存储或API加载设置
      const savedSettings = localStorage.getItem('app-settings')
      if (savedSettings) {
        try {
          const settings = JSON.parse(savedSettings)
          Object.keys(settings).forEach(key => {
            commit('SET_APP_SETTING', { key, value: settings[key] })
          })
        } catch (error) {
          console.error('加载应用设置失败:', error)
        }
      }
      resolve()
    })
  },
  
  // 保存应用设置
  saveSettings({ state }) {
    return new Promise(resolve => {
      try {
        localStorage.setItem('app-settings', JSON.stringify(state.settings))
        resolve()
      } catch (error) {
        console.error('保存应用设置失败:', error)
        resolve()
      }
    })
  },
  
  // 设置应用设置
  setAppSetting({ commit, dispatch }, payload) {
    commit('SET_APP_SETTING', payload)
    dispatch('saveSettings')
  },
  
  // 设置系统信息
  setSystemInfo({ commit }, info) {
    commit('SET_SYSTEM_INFO', info)
  },
  
  // 设置配置
  setConfig({ commit }, payload) {
    commit('SET_CONFIG', payload)
  },
  
  // 设置功能开关
  setFeature({ commit }, payload) {
    commit('SET_FEATURE', payload)
  },
  
  // 重置状态
  resetState({ commit }) {
    commit('RESET_STATE')
  }
}

const getters = {
  // 应用设置
  settings: state => state.settings,
  
  // 系统信息
  systemInfo: state => state.systemInfo,
  
  // 全局配置
  config: state => state.config,
  
  // 功能开关
  features: state => state.features,
  
  // 应用名称
  appName: state => state.settings.appName,
  
  // 应用版本
  version: state => state.systemInfo.version,
  
  // 构建时间
  buildTime: state => state.systemInfo.buildTime,
  
  // 环境
  environment: state => state.systemInfo.environment,
  
  // API地址
  apiBaseUrl: state => state.systemInfo.apiBaseUrl,
  
  // 是否开发环境
  isDevelopment: state => state.systemInfo.environment === 'development',
  
  // 是否生产环境
  isProduction: state => state.systemInfo.environment === 'production'
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
