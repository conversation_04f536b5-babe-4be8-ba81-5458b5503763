/**
 * 全局事件总线系统
 * 
 * 功能：
 * - 提供全局事件发布订阅机制
 * - 支持数据刷新事件传递
 * - 组件间通信
 * - 自动清理事件监听器
 */

import { ref, onUnmounted } from 'vue'

// 事件存储
const events = new Map()

// 事件类型常量
export const EVENT_TYPES = {
  // 数据刷新事件
  REFRESH_LIST: 'refresh_list',
  REFRESH_TECHNICIAN_LIST: 'refresh_technician_list',
  REFRESH_SERVICE_LIST: 'refresh_service_list',
  REFRESH_MARKET_LIST: 'refresh_market_list',
  REFRESH_USER_LIST: 'refresh_user_list',
  REFRESH_FINANCE_LIST: 'refresh_finance_list',
  REFRESH_SHOP_ORDER_LIST: 'refresh_shop_order_list',
  REFRESH_DISTRIBUTION_LIST: 'refresh_distribution_list',
  
  // 操作成功事件
  OPERATION_SUCCESS: 'operation_success',
  ADD_SUCCESS: 'add_success',
  EDIT_SUCCESS: 'edit_success',
  DELETE_SUCCESS: 'delete_success',
  
  // 状态变更事件
  STATUS_CHANGE: 'status_change',
  
  // 通用刷新事件（用于不确定具体模块的情况）
  GLOBAL_REFRESH: 'global_refresh'
}

/**
 * 事件总线类
 */
class EventBus {
  constructor() {
    this.events = new Map()
  }

  /**
   * 订阅事件
   * @param {string} eventType 事件类型
   * @param {Function} callback 回调函数
   * @param {Object} options 选项
   * @returns {Function} 取消订阅函数
   */
  on(eventType, callback, options = {}) {
    if (!this.events.has(eventType)) {
      this.events.set(eventType, new Set())
    }

    const listener = {
      callback,
      once: options.once || false,
      id: Symbol('listener')
    }

    this.events.get(eventType).add(listener)

    // 返回取消订阅函数
    return () => {
      this.off(eventType, listener.id)
    }
  }

  /**
   * 订阅一次性事件
   * @param {string} eventType 事件类型
   * @param {Function} callback 回调函数
   * @returns {Function} 取消订阅函数
   */
  once(eventType, callback) {
    return this.on(eventType, callback, { once: true })
  }

  /**
   * 取消订阅
   * @param {string} eventType 事件类型
   * @param {Symbol} listenerId 监听器ID
   */
  off(eventType, listenerId) {
    if (!this.events.has(eventType)) return

    const listeners = this.events.get(eventType)
    for (const listener of listeners) {
      if (listener.id === listenerId) {
        listeners.delete(listener)
        break
      }
    }

    // 如果没有监听器了，删除事件类型
    if (listeners.size === 0) {
      this.events.delete(eventType)
    }
  }

  /**
   * 发布事件
   * @param {string} eventType 事件类型
   * @param {*} data 事件数据
   */
  emit(eventType, data = null) {
    console.log(`📡 发布事件: ${eventType}`, data)

    if (!this.events.has(eventType)) {
      console.log(`⚠️ 没有监听器订阅事件: ${eventType}`)
      return
    }

    const listeners = this.events.get(eventType)
    const listenersToRemove = []

    for (const listener of listeners) {
      try {
        listener.callback(data)
        
        // 如果是一次性监听器，标记为删除
        if (listener.once) {
          listenersToRemove.push(listener)
        }
      } catch (error) {
        console.error(`❌ 事件监听器执行失败: ${eventType}`, error)
      }
    }

    // 删除一次性监听器
    listenersToRemove.forEach(listener => {
      listeners.delete(listener)
    })

    // 如果没有监听器了，删除事件类型
    if (listeners.size === 0) {
      this.events.delete(eventType)
    }
  }

  /**
   * 清除所有事件监听器
   */
  clear() {
    this.events.clear()
  }

  /**
   * 获取事件统计信息
   */
  getStats() {
    const stats = {}
    for (const [eventType, listeners] of this.events) {
      stats[eventType] = listeners.size
    }
    return stats
  }
}

// 创建全局事件总线实例
const eventBus = new EventBus()

/**
 * 数据刷新工具函数
 */
export const refreshUtils = {
  /**
   * 刷新技师列表
   */
  refreshTechnicianList(data = null) {
    eventBus.emit(EVENT_TYPES.REFRESH_TECHNICIAN_LIST, data)
    eventBus.emit(EVENT_TYPES.REFRESH_LIST, { module: 'technician', data })
  },

  /**
   * 刷新服务列表
   */
  refreshServiceList(data = null) {
    eventBus.emit(EVENT_TYPES.REFRESH_SERVICE_LIST, data)
    eventBus.emit(EVENT_TYPES.REFRESH_LIST, { module: 'service', data })
  },

  /**
   * 刷新营销列表
   */
  refreshMarketList(data = null) {
    eventBus.emit(EVENT_TYPES.REFRESH_MARKET_LIST, data)
    eventBus.emit(EVENT_TYPES.REFRESH_LIST, { module: 'market', data })
  },

  /**
   * 刷新用户列表
   */
  refreshUserList(data = null) {
    eventBus.emit(EVENT_TYPES.REFRESH_USER_LIST, data)
    eventBus.emit(EVENT_TYPES.REFRESH_LIST, { module: 'user', data })
  },

  /**
   * 刷新财务列表
   */
  refreshFinanceList(data = null) {
    eventBus.emit(EVENT_TYPES.REFRESH_FINANCE_LIST, data)
    eventBus.emit(EVENT_TYPES.REFRESH_LIST, { module: 'finance', data })
  },

  /**
   * 刷新商城订单列表
   */
  refreshShopOrderList(data = null) {
    eventBus.emit(EVENT_TYPES.REFRESH_SHOP_ORDER_LIST, data)
    eventBus.emit(EVENT_TYPES.REFRESH_LIST, { module: 'shop', data })
  },

  /**
   * 刷新分销列表
   */
  refreshDistributionList(data = null) {
    eventBus.emit(EVENT_TYPES.REFRESH_DISTRIBUTION_LIST, data)
    eventBus.emit(EVENT_TYPES.REFRESH_LIST, { module: 'distribution', data })
  },

  /**
   * 全局刷新（刷新所有列表）
   */
  refreshAll(data = null) {
    eventBus.emit(EVENT_TYPES.GLOBAL_REFRESH, data)
  }
}

/**
 * 操作成功通知工具函数
 */
export const operationUtils = {
  /**
   * 新增成功
   */
  addSuccess(module, data = null) {
    eventBus.emit(EVENT_TYPES.ADD_SUCCESS, { module, data })
    eventBus.emit(EVENT_TYPES.OPERATION_SUCCESS, { type: 'add', module, data })
  },

  /**
   * 编辑成功
   */
  editSuccess(module, data = null) {
    eventBus.emit(EVENT_TYPES.EDIT_SUCCESS, { module, data })
    eventBus.emit(EVENT_TYPES.OPERATION_SUCCESS, { type: 'edit', module, data })
  },

  /**
   * 删除成功
   */
  deleteSuccess(module, data = null) {
    eventBus.emit(EVENT_TYPES.DELETE_SUCCESS, { module, data })
    eventBus.emit(EVENT_TYPES.OPERATION_SUCCESS, { type: 'delete', module, data })
  },

  /**
   * 状态变更成功
   */
  statusChangeSuccess(module, data = null) {
    eventBus.emit(EVENT_TYPES.STATUS_CHANGE, { module, data })
    eventBus.emit(EVENT_TYPES.OPERATION_SUCCESS, { type: 'status_change', module, data })
  }
}

// 导出事件总线实例
export default eventBus

// 导出便捷方法
export const { on, once, off, emit, clear } = eventBus
