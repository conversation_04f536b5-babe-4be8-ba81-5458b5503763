/**
 * 系统设置模块Mock数据
 */

import Mock from 'mockjs'
import { successResponse, createCrudMock } from '../utils.js'

const systemConfigList = Mock.mock({
  'list|20-50': [{
    'id|+1': 1,
    'key': '@word(5, 15)',
    'name': '@ctitle(5, 15)',
    'value': '@word(10, 50)',
    'type': '@pick(["string", "number", "boolean"])',
    'description': '@cparagraph(1, 2)',
    'createTime': '@datetime'
  }]
}).list

createCrudMock('/api/system/config', systemConfigList)

console.log('系统设置模块Mock数据已加载')
export { systemConfigList }
