<!--
  系统配置模块
   系统设置功能的Vue3重构版本
  
  功能：
  - 基础配置管理
  - 邮件配置
  - 存储配置
  - 安全配置
  - 系统维护
-->

<template>
  <div class="system-config">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>系统配置</h1>
      <p class="page-subtitle">管理系统的各项配置参数</p>
    </div>

    <!-- 配置分类标签 -->
    <div class="config-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="基础配置" name="basic">
          <div class="config-section">
            <el-form
              ref="basicFormRef"
              :model="basicConfig"
              :rules="basicRules"
              label-width="120px"
              size="default"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="网站名称" prop="siteName">
                    <el-input v-model="basicConfig.siteName" placeholder="请输入网站名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="网站标题" prop="siteTitle">
                    <el-input v-model="basicConfig.siteTitle" placeholder="请输入网站标题" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="网站域名" prop="siteDomain">
                    <el-input v-model="basicConfig.siteDomain" placeholder="请输入网站域名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="ICP备案号" prop="icpNumber">
                    <el-input v-model="basicConfig.icpNumber" placeholder="请输入ICP备案号" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="网站描述" prop="siteDescription">
                <el-input
                  v-model="basicConfig.siteDescription"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入网站描述"
                />
              </el-form-item>
              <el-form-item label="网站关键词" prop="siteKeywords">
                <el-input v-model="basicConfig.siteKeywords" placeholder="请输入网站关键词，用逗号分隔" />
              </el-form-item>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="网站状态" prop="siteStatus">
                    <el-radio-group v-model="basicConfig.siteStatus">
                      <el-radio :value="1">正常运行</el-radio>
                      <el-radio :value="0">维护中</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="用户注册" prop="allowRegister">
                    <el-switch v-model="basicConfig.allowRegister" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="邮件配置" name="email">
          <div class="config-section">
            <el-form
              ref="emailFormRef"
              :model="emailConfig"
              :rules="emailRules"
              label-width="120px"
              size="default"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="SMTP服务器" prop="smtpHost">
                    <el-input v-model="emailConfig.smtpHost" placeholder="请输入SMTP服务器地址" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="SMTP端口" prop="smtpPort">
                    <el-input-number v-model="emailConfig.smtpPort" :min="1" :max="65535" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="发件人邮箱" prop="fromEmail">
                    <el-input v-model="emailConfig.fromEmail" placeholder="请输入发件人邮箱" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发件人名称" prop="fromName">
                    <el-input v-model="emailConfig.fromName" placeholder="请输入发件人名称" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="邮箱用户名" prop="username">
                    <el-input v-model="emailConfig.username" placeholder="请输入邮箱用户名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱密码" prop="password">
                    <el-input
                      v-model="emailConfig.password"
                      type="password"
                      placeholder="请输入邮箱密码"
                      show-password
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="加密方式" prop="encryption">
                    <el-select v-model="emailConfig.encryption" placeholder="请选择加密方式">
                      <el-option label="无加密" value="none" />
                      <el-option label="SSL" value="ssl" />
                      <el-option label="TLS" value="tls" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="启用邮件" prop="enabled">
                    <el-switch v-model="emailConfig.enabled" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item>
                <el-button type="primary" @click="testEmailConnection">
                  <el-icon><Message /></el-icon>
                  测试连接
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="存储配置" name="storage">
          <div class="config-section">
            <el-form
              ref="storageFormRef"
              :model="storageConfig"
              :rules="storageRules"
              label-width="120px"
              size="default"
            >
              <el-form-item label="存储类型" prop="type">
                <el-radio-group v-model="storageConfig.type">
                  <el-radio value="local">本地存储</el-radio>
                  <el-radio value="oss">阿里云OSS</el-radio>
                  <el-radio value="qiniu">七牛云</el-radio>
                  <el-radio value="cos">腾讯云COS</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <template v-if="storageConfig.type === 'local'">
                <el-form-item label="存储路径" prop="localPath">
                  <el-input v-model="storageConfig.localPath" placeholder="请输入本地存储路径" />
                </el-form-item>
                <el-form-item label="访问域名" prop="localDomain">
                  <el-input v-model="storageConfig.localDomain" placeholder="请输入访问域名" />
                </el-form-item>
              </template>
              
              <template v-if="storageConfig.type === 'oss'">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="AccessKey" prop="ossAccessKey">
                      <el-input v-model="storageConfig.ossAccessKey" placeholder="请输入AccessKey" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="SecretKey" prop="ossSecretKey">
                      <el-input
                        v-model="storageConfig.ossSecretKey"
                        type="password"
                        placeholder="请输入SecretKey"
                        show-password
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="Bucket名称" prop="ossBucket">
                      <el-input v-model="storageConfig.ossBucket" placeholder="请输入Bucket名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="地域节点" prop="ossRegion">
                      <el-input v-model="storageConfig.ossRegion" placeholder="请输入地域节点" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="自定义域名" prop="ossDomain">
                  <el-input v-model="storageConfig.ossDomain" placeholder="请输入自定义域名" />
                </el-form-item>
              </template>
              
              <el-form-item label="文件大小限制" prop="maxFileSize">
                <el-input-number
                  v-model="storageConfig.maxFileSize"
                  :min="1"
                  :max="100"
                  placeholder="MB"
                />
                <span style="margin-left: 8px">MB</span>
              </el-form-item>
              
              <el-form-item label="允许的文件类型" prop="allowedTypes">
                <el-checkbox-group v-model="storageConfig.allowedTypes">
                  <el-checkbox label="jpg">JPG</el-checkbox>
                  <el-checkbox label="png">PNG</el-checkbox>
                  <el-checkbox label="gif">GIF</el-checkbox>
                  <el-checkbox label="pdf">PDF</el-checkbox>
                  <el-checkbox label="doc">DOC</el-checkbox>
                  <el-checkbox label="xls">XLS</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="安全配置" name="security">
          <div class="config-section">
            <el-form
              ref="securityFormRef"
              :model="securityConfig"
              :rules="securityRules"
              label-width="120px"
              size="default"
            >
              <el-form-item label="密码强度" prop="passwordStrength">
                <el-radio-group v-model="securityConfig.passwordStrength">
                  <el-radio value="low">低（6位以上）</el-radio>
                  <el-radio value="medium">中（8位以上，包含字母数字）</el-radio>
                  <el-radio value="high">高（8位以上，包含字母数字特殊字符）</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="登录失败限制" prop="loginFailLimit">
                    <el-input-number v-model="securityConfig.loginFailLimit" :min="3" :max="10" />
                    <span style="margin-left: 8px">次</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="锁定时间" prop="lockTime">
                    <el-input-number v-model="securityConfig.lockTime" :min="5" :max="60" />
                    <span style="margin-left: 8px">分钟</span>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="会话超时" prop="sessionTimeout">
                    <el-input-number v-model="securityConfig.sessionTimeout" :min="30" :max="1440" />
                    <span style="margin-left: 8px">分钟</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="强制HTTPS" prop="forceHttps">
                    <el-switch v-model="securityConfig.forceHttps" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="IP白名单" prop="ipWhitelist">
                <el-input
                  v-model="securityConfig.ipWhitelist"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入IP白名单，每行一个IP或IP段"
                />
              </el-form-item>
              
              <el-form-item label="启用验证码" prop="enableCaptcha">
                <el-switch v-model="securityConfig.enableCaptcha" />
              </el-form-item>
              
              <el-form-item label="启用双因子认证" prop="enableTwoFactor">
                <el-switch v-model="securityConfig.enableTwoFactor" />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="系统维护" name="maintenance">
          <div class="config-section">
            <div class="maintenance-actions">
              <div class="action-card">
                <div class="action-icon">
                  <el-icon :size="32"><RefreshRight /></el-icon>
                </div>
                <div class="action-content">
                  <h4>清理系统缓存</h4>
                  <p>清理系统缓存文件，提升系统性能</p>
                  <el-button type="primary" @click="clearCache">
                    清理缓存
                  </el-button>
                </div>
              </div>
              
              <div class="action-card">
                <div class="action-icon">
                  <el-icon :size="32"><Download /></el-icon>
                </div>
                <div class="action-content">
                  <h4>数据库备份</h4>
                  <p>备份系统数据库，确保数据安全</p>
                  <el-button type="success" @click="backupDatabase">
                    立即备份
                  </el-button>
                </div>
              </div>
              
              <div class="action-card">
                <div class="action-icon">
                  <el-icon :size="32"><View /></el-icon>
                </div>
                <div class="action-content">
                  <h4>系统日志</h4>
                  <p>查看系统运行日志和错误信息</p>
                  <el-button type="info" @click="viewLogs">
                    查看日志
                  </el-button>
                </div>
              </div>
              
              <div class="action-card">
                <div class="action-icon">
                  <el-icon :size="32"><Tools /></el-icon>
                </div>
                <div class="action-content">
                  <h4>系统检测</h4>
                  <p>检测系统环境和配置是否正常</p>
                  <el-button type="warning" @click="systemCheck">
                    开始检测
                  </el-button>
                </div>
              </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="system-info">
              <h4>系统信息</h4>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="系统版本">{{ systemInfo.version }}</el-descriptions-item>
                <el-descriptions-item label="PHP版本">{{ systemInfo.phpVersion }}</el-descriptions-item>
                <el-descriptions-item label="数据库版本">{{ systemInfo.dbVersion }}</el-descriptions-item>
                <el-descriptions-item label="服务器系统">{{ systemInfo.serverOs }}</el-descriptions-item>
                <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
                <el-descriptions-item label="内存使用">{{ systemInfo.memoryUsage }}</el-descriptions-item>
                <el-descriptions-item label="磁盘使用">{{ systemInfo.diskUsage }}</el-descriptions-item>
                <el-descriptions-item label="最后更新">{{ systemInfo.lastUpdate }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 保存按钮 -->
    <div class="save-actions" v-if="activeTab !== 'maintenance'">
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleSave" :loading="saveLoading">
        保存配置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Message,
  RefreshRight,
  Download,
  View,
  Tools
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('basic')
const saveLoading = ref(false)
const basicFormRef = ref()
const emailFormRef = ref()
const storageFormRef = ref()
const securityFormRef = ref()

// 基础配置
const basicConfig = reactive({
  siteName: '今师傅',
  siteTitle: '今师傅后台管理系统',
  siteDomain: 'https://admin.example.com',
  icpNumber: '京ICP备12345678号',
  siteDescription: '基于今师傅的现代化后台管理系统',
  siteKeywords: '今师傅管理系统,后台,Element Plus',
  siteStatus: 1,
  allowRegister: true
})

// 邮件配置
const emailConfig = reactive({
  smtpHost: 'smtp.qq.com',
  smtpPort: 587,
  fromEmail: '<EMAIL>',
  fromName: 'Admin System',
  username: '<EMAIL>',
  password: '',
  encryption: 'tls',
  enabled: true
})

// 存储配置
const storageConfig = reactive({
  type: 'local',
  localPath: '/uploads',
  localDomain: 'https://admin.example.com',
  ossAccessKey: '',
  ossSecretKey: '',
  ossBucket: '',
  ossRegion: '',
  ossDomain: '',
  maxFileSize: 10,
  allowedTypes: ['jpg', 'png', 'gif', 'pdf']
})

// 安全配置
const securityConfig = reactive({
  passwordStrength: 'medium',
  loginFailLimit: 5,
  lockTime: 15,
  sessionTimeout: 120,
  forceHttps: false,
  ipWhitelist: '',
  enableCaptcha: true,
  enableTwoFactor: false
})

// 系统信息
const systemInfo = reactive({
  version: '3.0.0',
  phpVersion: 'Node.js 18.17.0',
  dbVersion: 'MySQL 8.0.33',
  serverOs: 'Windows 11',
  uptime: '15天 8小时 32分钟',
  memoryUsage: '2.1GB / 8GB (26%)',
  diskUsage: '45.2GB / 256GB (18%)',
  lastUpdate: '2025-01-11 10:30:00'
})

// 表单验证规则
const basicRules = {
  siteName: [
    { required: true, message: '请输入网站名称', trigger: 'blur' }
  ],
  siteTitle: [
    { required: true, message: '请输入网站标题', trigger: 'blur' }
  ],
  siteDomain: [
    { required: true, message: '请输入网站域名', trigger: 'blur' },
    { type: 'url', message: '请输入正确的域名格式', trigger: 'blur' }
  ]
}

const emailRules = {
  smtpHost: [
    { required: true, message: '请输入SMTP服务器', trigger: 'blur' }
  ],
  smtpPort: [
    { required: true, message: '请输入SMTP端口', trigger: 'blur' }
  ],
  fromEmail: [
    { required: true, message: '请输入发件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入邮箱用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入邮箱密码', trigger: 'blur' }
  ]
}

const storageRules = {
  localPath: [
    { required: true, message: '请输入存储路径', trigger: 'blur' }
  ],
  maxFileSize: [
    { required: true, message: '请设置文件大小限制', trigger: 'blur' }
  ]
}

const securityRules = {
  loginFailLimit: [
    { required: true, message: '请设置登录失败限制', trigger: 'blur' }
  ],
  lockTime: [
    { required: true, message: '请设置锁定时间', trigger: 'blur' }
  ],
  sessionTimeout: [
    { required: true, message: '请设置会话超时时间', trigger: 'blur' }
  ]
}

// 方法
const handleTabChange = (tabName) => {
  activeTab.value = tabName
}

const handleSave = async () => {
  let formRef = null

  switch (activeTab.value) {
    case 'basic':
      formRef = basicFormRef.value
      break
    case 'email':
      formRef = emailFormRef.value
      break
    case 'storage':
      formRef = storageFormRef.value
      break
    case 'security':
      formRef = securityFormRef.value
      break
  }

  if (!formRef) return

  try {
    await formRef.validate()

    saveLoading.value = true

    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('配置保存成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('配置保存失败')
    }
  } finally {
    saveLoading.value = false
  }
}

const handleReset = () => {
  ElMessageBox.confirm(
    '确定要重置当前配置吗？',
    '重置确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 重置对应的配置
    switch (activeTab.value) {
      case 'basic':
        if (basicFormRef.value) {
          basicFormRef.value.resetFields()
        }
        break
      case 'email':
        if (emailFormRef.value) {
          emailFormRef.value.resetFields()
        }
        break
      case 'storage':
        if (storageFormRef.value) {
          storageFormRef.value.resetFields()
        }
        break
      case 'security':
        if (securityFormRef.value) {
          securityFormRef.value.resetFields()
        }
        break
    }
    ElMessage.success('配置已重置')
  }).catch(() => {
    // 取消重置
  })
}

const testEmailConnection = async () => {
  try {
    await emailFormRef.value.validate()

    ElMessage.info('正在测试邮件连接...')

    // 模拟测试连接
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('邮件连接测试成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('邮件连接测试失败')
    }
  }
}

const clearCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理系统缓存吗？',
      '清理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.info('正在清理缓存...')

    // 模拟清理缓存
    await new Promise(resolve => setTimeout(resolve, 3000))

    ElMessage.success('缓存清理完成')
  } catch (error) {
    // 取消操作
  }
}

const backupDatabase = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要备份数据库吗？',
      '备份确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.info('正在备份数据库...')

    // 模拟备份数据库
    await new Promise(resolve => setTimeout(resolve, 5000))

    ElMessage.success('数据库备份完成')
  } catch (error) {
    // 取消操作
  }
}

const viewLogs = () => {
  ElMessage.info('跳转到系统日志页面...')
  // 这里应该跳转到日志页面
}

const systemCheck = async () => {
  ElMessage.info('正在检测系统环境...')

  // 模拟系统检测
  await new Promise(resolve => setTimeout(resolve, 3000))

  ElMessage.success('系统检测完成，一切正常')
}

// 生命周期
onMounted(() => {
  // 加载配置数据
  console.log('系统配置页面已加载')
})
</script>

<style scoped>
.system-config {
  padding: var(--spacing-lg);
}

.page-title {
  margin-bottom: var(--spacing-xl);
}

.page-title h1 {
  font-size: var(--font-size-xxl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.config-tabs {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.config-section {
  padding: var(--spacing-xl);
  min-height: 500px;
}

.save-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
}

.maintenance-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.action-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--bg-color-column);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-box-shadow);
}

.action-card:hover {
  box-shadow: var(--box-shadow-base);
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: var(--color-primary-light-9);
  color: var(--color-primary);
  border-radius: var(--border-radius-lg);
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-content h4 {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.action-content p {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}

.system-info {
  margin-top: var(--spacing-xl);
}

.system-info h4 {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
}

/* Element Plus 标签页样式覆盖 */
:deep(.el-tabs__header) {
  background-color: var(--bg-color-column);
  margin: 0;
  border-bottom: 1px solid var(--border-color-light);
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 var(--spacing-lg);
}

:deep(.el-tabs__item) {
  height: 50px;
  line-height: 50px;
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

:deep(.el-tabs__item.is-active) {
  color: var(--color-primary);
  font-weight: 500;
}

:deep(.el-tabs__active-bar) {
  background-color: var(--color-primary);
}

/* 表单样式 */
:deep(.el-form-item__label) {
  color: var(--color-text-primary);
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: var(--border-radius-sm);
}

:deep(.el-textarea__inner) {
  border-radius: var(--border-radius-sm);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: var(--border-radius-sm);
}

:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

/* 描述列表样式 */
:deep(.el-descriptions) {
  margin-top: var(--spacing-md);
}

:deep(.el-descriptions__header) {
  margin-bottom: var(--spacing-lg);
}

:deep(.el-descriptions__title) {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
}

:deep(.el-descriptions__body .el-descriptions__table) {
  border-radius: var(--border-radius-sm);
}

:deep(.el-descriptions__label) {
  background-color: var(--bg-color-column);
  color: var(--color-text-primary);
  font-weight: 500;
}

:deep(.el-descriptions__content) {
  color: var(--color-text-secondary);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .system-config {
    padding: var(--spacing-md);
  }

  .config-section {
    padding: var(--spacing-lg);
  }

  .maintenance-actions {
    grid-template-columns: 1fr;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .action-icon {
    width: 48px;
    height: 48px;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0 var(--spacing-md);
  }

  :deep(.el-form .el-row) {
    margin: 0;
  }

  :deep(.el-form .el-col) {
    padding: 0;
    margin-bottom: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .page-title h1 {
    font-size: var(--font-size-xl);
  }

  .save-actions {
    flex-direction: column;
  }

  :deep(.el-tabs__item) {
    font-size: var(--font-size-sm);
    padding: 0 var(--spacing-sm);
  }

  :deep(.el-form-item__label) {
    font-size: var(--font-size-sm);
  }
}
</style>
