<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-image">
        <el-icon :size="120"><CircleClose /></el-icon>
      </div>
      <h1 class="error-title">500</h1>
      <p class="error-message">服务器内部错误</p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">返回首页</el-button>
        <el-button @click="goBack">返回上页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { CircleClose } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-color-page);
}

.error-content {
  text-align: center;
  max-width: 500px;
  padding: var(--spacing-xl);
}

.error-image {
  color: var(--color-danger);
  margin-bottom: var(--spacing-lg);
}

.error-title {
  font-size: 72px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.error-message {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
}

.error-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}
</style>
