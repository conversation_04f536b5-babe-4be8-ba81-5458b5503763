/**
 * Mock数据系统主入口

 * 支持所有9个主菜单模块的数据需求
 */

import Mock from 'mockjs'

console.log('📦 Mock.js 版本:', Mock.version || 'unknown')

// 导入工具函数
export { successResponse, pageResponse, errorResponse, createCrudMock } from './utils.js'

// 导入各模块的Mock配置
console.log('🔄 正在导入Mock模块...')
import './modules/service'      // 服务项目
import './modules/technician'   // 师傅管理
import './modules/market'       // 营销管理
import './modules/shop'         // 订单管理
import './modules/distribution' // 分销管理
import './modules/finance'      // 财务管理
import './modules/custom'       // 用户管理
import './modules/account'      // 账号设置
import './modules/system'       // 系统设置
console.log('✅ Mock模块导入完成')

// 响应格式函数已移至utils.js，通过上面的export导出

// 用户登录Mock
Mock.mock('/api/user/login', 'post', (options) => {
  const { username, password } = JSON.parse(options.body)
  
  if (username === 'admin' && password === '123456') {
    return successResponse({
      token: Mock.Random.guid(),
      userInfo: {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        email: '<EMAIL>',
        avatar: Mock.Random.image('100x100', '#409eff', '#fff', 'Avatar'),
        roles: ['admin'],
        permissions: ['*:*:*']
      }
    })
  } else {
    return errorResponse('用户名或密码错误', 401)
  }
})

// 获取用户信息
Mock.mock('/api/user/info', 'get', () => {
  return successResponse({
    id: 1,
    username: 'admin',
    nickname: '管理员',
    email: '<EMAIL>',
    avatar: Mock.Random.image('100x100', '#409eff', '#fff', 'Avatar'),
    roles: ['admin'],
    permissions: ['*:*:*']
  })
})

// 用户退出登录
Mock.mock('/api/user/logout', 'post', () => {
  return successResponse(null, '退出成功')
})

// 获取菜单权限
Mock.mock('/api/menu/permissions', 'get', () => {
  return successResponse({
    menus: [
      'service', 'technician', 'market', 'shop', 
      'distribution', 'finance', 'custom', 'account', 'system'
    ],
    permissions: ['*:*:*']
  })
})

// createCrudMock函数已在文件开头定义，这里不需要重复定义

// 设置Mock拦截延时
Mock.setup({
  timeout: '200-500'
})

console.log('Mock数据系统已启动，支持所有9个主菜单模块')

export default Mock
