<!--
  LayoutFooter 组件

  
  功能：
  - 底部信息栏，固定高度40px
  - 版权信息
  - 系统状态
  - 快速链接
-->

<template>
  <footer class="layout-footer">
    <div class="footer-content">
      <!-- 左侧版权信息 -->
      <!-- <div class="footer-left">
        <span class="copyright">
          © {{ currentYear }} 今师傅. All rights reserved.
        </span>
        <span class="version">
          Version {{ version }}
        </span>
      </div> -->

      <!-- 中间系统状态 -->
      <!-- <div class="footer-center">
        <div class="system-status">
          <el-icon class="status-icon" :class="{ 'online': isOnline }">
            <CircleCheck v-if="isOnline" />
            <CircleClose v-else />
          </el-icon>
          <span class="status-text">
            {{ isOnline ? '系统正常' : '系统异常' }}
          </span>
        </div>
        
        <div class="performance-info">
          <span class="performance-item">
            内存: {{ memoryUsage }}%
          </span>
          <span class="performance-item">
            CPU: {{ cpuUsage }}%
          </span>
        </div>
      </div> -->

      <!-- 右侧快速链接 -->
      <div class="footer-right">
        <!-- <div class="quick-links">
          <a href="#" class="footer-link" @click.prevent="openHelp">
            <el-icon><QuestionFilled /></el-icon>
            帮助
          </a>
          <a href="#" class="footer-link" @click.prevent="openFeedback">
            <el-icon><ChatDotRound /></el-icon>
            反馈
          </a>
          <a href="#" class="footer-link" @click.prevent="openAbout">
            <el-icon><InfoFilled /></el-icon>
            关于
          </a>
        </div> -->
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import {
  CircleCheck,
  CircleClose,
  QuestionFilled,
  ChatDotRound,
  InfoFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const store = useStore()

// 响应式数据
const version = ref('3.0.0')
const isOnline = ref(true)
const memoryUsage = ref(45)
const cpuUsage = ref(23)
const performanceTimer = ref(null)

// 计算属性
const currentYear = computed(() => new Date().getFullYear())

// 方法
const openHelp = () => {
  ElMessage.info('帮助文档功能开发中...')
}

const openFeedback = () => {
  ElMessage.info('意见反馈功能开发中...')
}

const openAbout = () => {
  ElMessage.info('关于我们功能开发中...')
}

// 模拟系统性能监控
const updatePerformance = () => {
  // 模拟内存使用率变化
  memoryUsage.value = Math.floor(Math.random() * 30) + 40 // 40-70%
  
  // 模拟CPU使用率变化
  cpuUsage.value = Math.floor(Math.random() * 40) + 10 // 10-50%
  
  // 模拟网络状态
  isOnline.value = navigator.onLine
}

// 生命周期
onMounted(() => {
  // 初始化性能数据
  updatePerformance()
  
  // 每30秒更新一次性能数据
  performanceTimer.value = setInterval(updatePerformance, 30000)
  
  // 监听网络状态变化
  window.addEventListener('online', () => {
    isOnline.value = true
  })
  
  window.addEventListener('offline', () => {
    isOnline.value = false
  })
})

onUnmounted(() => {
  if (performanceTimer.value) {
    clearInterval(performanceTimer.value)
  }
})
</script>

<style scoped>
.layout-footer {
  position: fixed;
  bottom: 0;
  left: var(--sidebar-width);
  right: 0;
  height: var(--footer-height);
  background-color: var(--bg-color-base);
  border-top: 1px solid var(--border-color-light);
  z-index: var(--z-index-normal);
  transition: left var(--transition-duration-base) ease;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--spacing-lg);
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.footer-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.copyright {
  color: var(--color-text-secondary);
}

.version {
  color: var(--color-text-placeholder);
  font-size: 11px;
}

.footer-center {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.system-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-icon {
  font-size: 14px;
  color: var(--color-danger);
  transition: var(--transition-color);
}

.status-icon.online {
  color: var(--color-success);
}

.status-text {
  font-size: var(--font-size-xs);
}

.performance-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.performance-item {
  font-size: 11px;
  color: var(--color-text-placeholder);
}

.footer-right {
  display: flex;
  align-items: center;
}

.quick-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.footer-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: var(--font-size-xs);
  transition: var(--transition-color);
  cursor: pointer;
}

.footer-link:hover {
  color: var(--color-primary);
}

.footer-link .el-icon {
  font-size: 12px;
}

/* 侧边栏折叠状态适配 */
.layout-footer.sidebar-collapsed {
  left: var(--sidebar-collapsed-width);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .layout-footer {
    left: 0;
    position: relative;
    margin-top: auto;
  }
  
  .footer-content {
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    height: auto;
    min-height: var(--footer-height);
  }
  
  .footer-center {
    order: -1;
  }
  
  .performance-info {
    display: none;
  }
}

@media (max-width: 480px) {
  .footer-content {
    font-size: 10px;
  }
  
  .footer-left {
    flex-direction: column;
    gap: var(--spacing-xs);
    text-align: center;
  }
  
  .quick-links {
    gap: var(--spacing-sm);
  }
  
  .footer-link span {
    display: none;
  }
}
</style>
