/**
 * 订单管理模块Mock数据

 */

import Mock from 'mockjs'
import { successResponse, pageResponse, createCrudMock } from '../utils.js'

// 订单列表数据
const orderList = Mock.mock({
  'list|50-100': [{
    'id|+1': 1,
    'orderNo': '@word(10, 15)',
    'userId|1-1000': 1,
    'userName': '@cname',
    'userPhone': '@phone',
    'technicianId|1-80': 1,
    'technicianName': '@cname',
    'serviceId|1-50': 1,
    'serviceName': '@ctitle(5, 15)',
    'amount|100-2000.2': 1,
    'actualAmount|100-2000.2': 1,
    'status|1': [0, 1, 2, 3, 4, 5], // 0-待支付 1-待接单 2-服务中 3-已完成 4-已取消 5-已退款
    'payMethod': '@pick(["微信", "支付宝", "余额"])',
    'payTime': '@datetime',
    'serviceTime': '@datetime',
    'completeTime': '@datetime',
    'address': '@county(true)',
    'remark': '@cparagraph(1, 2)',
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 退款管理数据
const refundList = Mock.mock({
  'list|20-50': [{
    'id|+1': 1,
    'orderId|1-100': 1,
    'orderNo': '@word(10, 15)',
    'refundAmount|50-1000.2': 1,
    'refundReason': '@pick(["服务质量问题", "师傅未按时到达", "用户主动取消", "其他原因"])',
    'refundType|1': [1, 2], // 1-全额退款 2-部分退款
    'status|1': [0, 1, 2], // 0-待处理 1-已同意 2-已拒绝
    'processRemark': '@cparagraph(1, 2)',
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 评价管理数据
const evaluateList = Mock.mock({
  'list|30-80': [{
    'id|+1': 1,
    'orderId|1-100': 1,
    'userId|1-1000': 1,
    'userName': '@cname',
    'technicianId|1-80': 1,
    'technicianName': '@cname',
    'rating|1-5': 1,
    'content': '@cparagraph(1, 3)',
    'images|0-3': ['@image("200x200", "#409eff", "#fff", "评价")'],
    'reply': '@cparagraph(1, 2)',
    'isAnonymous|1': [true, false],
    'status|1': [0, 1], // 0-待审核 1-已通过
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 售后管理数据
const afterSaleList = Mock.mock({
  'list|15-40': [{
    'id|+1': 1,
    'orderId|1-100': 1,
    'orderNo': '@word(10, 15)',
    'type|1': [1, 2, 3], // 1-投诉 2-建议 3-咨询
    'title': '@ctitle(5, 20)',
    'content': '@cparagraph(2, 5)',
    'images|0-3': ['@image("200x200", "#409eff", "#fff", "售后")'],
    'status|1': [0, 1, 2], // 0-待处理 1-处理中 2-已完成
    'priority|1': [1, 2, 3], // 1-低 2-中 3-高
    'assignTo': '@cname',
    'response': '@cparagraph(1, 3)',
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 新的订单列表数据（符合新接口格式）
const newOrderList = Mock.mock({
  'list|50-100': [{
    'id|+1': 1,
    'orderCode': () => '2025' + Mock.mock('@date("MMddHH")') + Mock.mock('@integer(10, 99)'),
    'goodsName': '@pick(["开锁", "换锁", "修锁", "配钥匙", "安装门锁", "智能锁安装", "保险柜开锁", "汽车开锁"])',
    'userName': '@cname',
    'userMobile': '@phone',
    'coachName': '@cname',
    'coachMobile': '@phone',
    'payPrice|100-2000.2': 1,
    'payType|1': [1, 2, 3], // 1-微信支付 2-支付宝 3-余额支付
    'status|1': [1, 2, 3, 4, 5, 6, 7], // 1-待付款 2-待接单 3-待服务 4-服务中 5-待评价 6-已完成 7-已取消
    'type|1': [1, 2, 3], // 1-普通订单 2-预约订单 3-紧急订单
    'address': '@county(true)' + '@ctitle(5, 10)',
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'updateTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
  }]
}).list

// 师傅收入排行榜数据
const coachIncomeRank = Mock.mock({
  'list|10-20': [{
    'coachId|+1': 1325,
    'coachName': '@cname',
    'coachMobile': '@phone',
    'totalIncome|100-5000.2': 1,
    'orderCount|1-50': 1
  }]
}).list

// 师傅跑单排行榜数据
const coachCancelRank = Mock.mock({
  'list|10-20': [{
    'coachId|+1': 1325,
    'coachName': '@cname',
    'coachMobile': '@phone',
    'cancelCount|1-20': 1
  }]
}).list

// 用户跑单排行榜数据
const userCancelRank = Mock.mock({
  'list|10-20': [{
    'userId|+1': 1001,
    'userName': '@cname',
    'userMobile': '@phone',
    'cancelCount|1-15': 1
  }]
}).list

// 创建CRUD接口
createCrudMock('/api/shop/order', orderList)
createCrudMock('/api/shop/refund', refundList)
createCrudMock('/api/shop/evaluate', evaluateList)
createCrudMock('/api/shop/aftersale', afterSaleList)

// 新的订单管理API接口
Mock.mock('/api/admin/order/list', 'post', (options) => {
  console.log('📋 订单列表API被调用 - 新接口')
  const body = JSON.parse(options.body || '{}')

  let filteredList = [...newOrderList]

  // 根据参数过滤
  if (body.orderCode) {
    filteredList = filteredList.filter(item =>
      item.orderCode.includes(body.orderCode)
    )
  }

  if (body.goodsName) {
    filteredList = filteredList.filter(item =>
      item.goodsName.includes(body.goodsName)
    )
  }

  if (body.coachName) {
    filteredList = filteredList.filter(item =>
      item.coachName.includes(body.coachName)
    )
  }

  if (body.payType) {
    filteredList = filteredList.filter(item =>
      item.payType === body.payType
    )
  }

  if (body.type) {
    filteredList = filteredList.filter(item =>
      item.type === body.type
    )
  }

  if (body.address) {
    filteredList = filteredList.filter(item =>
      item.address.includes(body.address)
    )
  }

  // 分页处理
  const pageNum = body.pageNum || 1
  const pageSize = body.pageSize || 10
  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  const pageData = filteredList.slice(start, end)

  return successResponse({
    list: pageData,
    total: filteredList.length,
    pageNum: pageNum,
    pageSize: pageSize,
    stats: {
      totalOrders: newOrderList.length,
      todayOrders: Mock.mock('@integer(10, 50)'),
      completedOrders: newOrderList.filter(item => item.status === 6).length,
      totalAmount: newOrderList.reduce((sum, item) => sum + item.payPrice, 0).toFixed(2)
    }
  })
})

// 订单导出接口
Mock.mock('/api/admin/order/export', 'post', () => {
  console.log('📤 订单导出API被调用')
  return successResponse({
    downloadUrl: '/api/download/orders_' + Date.now() + '.xlsx',
    message: '导出成功'
  })
})

// 订单详情接口
Mock.mock(RegExp('/api/admin/order/detail/\\d+'), 'get', (options) => {
  console.log('📄 订单详情API被调用')
  const id = options.url.split('/').pop()
  const order = newOrderList.find(item => item.id == id) || newOrderList[0]

  return successResponse({
    ...order,
    orderGoods: [{
      goodsName: order.goodsName,
      goodsPrice: order.payPrice,
      goodsCover: Mock.mock('@image("200x200", "#409eff", "#fff", "商品")'),
      num: 1
    }],
    coachInfo: {
      coachName: order.coachName,
      mobile: order.coachMobile,
      levelName: '@pick(["初级师傅", "中级师傅", "高级师傅", "金牌师傅"])',
      workTime: Mock.mock('@integer(1, 10)')
    },
    serviceLogs: Mock.mock({
      'list|2-5': [{
        title: '@pick(["接单成功", "开始服务", "服务完成", "用户确认"])',
        content: '@cparagraph(1, 2)',
        createTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
        'images|0-2': ['@image("200x200", "#409eff", "#fff", "服务")']
      }]
    }).list
  })
})

// 师傅收入排行榜接口
Mock.mock('/api/admin/order/coach-income', 'get', () => {
  console.log('💰 师傅收入排行榜API被调用')
  return successResponse(coachIncomeRank.slice(0, 10))
})

// 师傅跑单排行榜接口
Mock.mock('/api/admin/order/coach-cancel', 'get', () => {
  console.log('🏃 师傅跑单排行榜API被调用')
  return successResponse(coachCancelRank.slice(0, 10))
})

// 用户跑单排行榜接口
Mock.mock('/api/admin/order/user-cancel', 'get', () => {
  console.log('👤 用户跑单排行榜API被调用')
  return successResponse(userCancelRank.slice(0, 10))
})

// 订单统计
Mock.mock('/api/shop/statistics', 'get', () => {
  return successResponse({
    totalOrders: orderList.length,
    completedOrders: orderList.filter(item => item.status === 3).length,
    cancelledOrders: orderList.filter(item => item.status === 4).length,
    totalRevenue: orderList.reduce((sum, item) => sum + item.actualAmount, 0),
    averageRating: (evaluateList.reduce((sum, item) => sum + item.rating, 0) / evaluateList.length).toFixed(1)
  })
})

console.log('订单管理模块Mock数据已加载')

export { orderList, refundList, evaluateList, afterSaleList, newOrderList, coachIncomeRank, coachCancelRank, userCancelRank }
