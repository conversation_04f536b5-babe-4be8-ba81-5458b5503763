<!--
  富文本编辑器组件

  使用Quill.js替代UEditor，保持相同的接口和功能
-->

<template>
  <div class="lb-ueditor">
    <div ref="editorRef" class="editor-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import { api } from '@/api-v2'
import { ElMessage } from 'element-plus'

// 注册自定义字体大小
const Size = Quill.import('formats/size')
Size.whitelist = ['8px', '9px', '10px', '11px', '12px', '14px', '16px', '18px', '20px', '22px', '24px', '26px', '28px', '30px', '32px', '36px', '42px', '48px', '56px', '64px', '72px']
Quill.register(Size, true)

// 注册自定义字体
const Font = Quill.import('formats/font')
Font.whitelist = ['SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong', 'Arial', 'Times-New-Roman', 'Helvetica', 'sans-serif', 'serif', 'monospace', 'PingFang-SC', 'Hiragino-Sans-GB', 'Source-Han-Sans-CN', 'Noto-Sans-CJK-SC']
Quill.register(Font, true)

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  height: {
    type: String,
    default: '500px'
  },
  destroy: {
    type: Boolean,
    default: false
  },
  config: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const editorRef = ref()
let quillInstance = null

// 编辑器配置 - 增强版工具栏
const defaultConfig = {
  theme: 'snow',
  placeholder: props.placeholder,
  modules: {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'font': ['SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong', 'Arial', 'Times-New-Roman', 'Helvetica', 'sans-serif', 'serif', 'monospace', 'PingFang-SC', 'Hiragino-Sans-GB', 'Source-Han-Sans-CN', 'Noto-Sans-CJK-SC'] }],
      [{ 'size': ['small', false, 'large', 'huge', '8px', '9px', '10px', '11px', '12px', '14px', '16px', '18px', '20px', '22px', '24px', '26px', '28px', '30px', '32px', '36px', '42px', '48px', '56px', '64px', '72px'] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'script': 'sub' }, { 'script': 'super' }],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      [{ 'indent': '-1' }, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'align': [] }],
      ['blockquote', 'code-block'],
      ['link', 'image'], // 移除video
      ['clean']
    ]
  }
}

// 合并配置
const editorConfig = { ...defaultConfig, ...props.config }

// 初始化编辑器
const initEditor = () => {
  if (!editorRef.value) return

  try {
    quillInstance = new Quill(editorRef.value, editorConfig)

    // 设置编辑器高度
    const editorContainer = editorRef.value.querySelector('.ql-editor')
    if (editorContainer) {
      editorContainer.style.minHeight = props.height
    }

    // 设置初始内容
    if (props.modelValue) {
      quillInstance.root.innerHTML = props.modelValue
    }

    // 监听内容变化
    quillInstance.on('text-change', () => {
      const html = quillInstance.root.innerHTML
      const text = quillInstance.getText()

      // 如果内容为空，返回空字符串
      const content = text.trim() === '' ? '' : html

      emit('update:modelValue', content)
      emit('change', content)
    })

    // 添加图片上传功能
    setupImageUpload()

    console.log('UEditor初始化完成')
  } catch (error) {
    console.error('UEditor初始化失败:', error)
  }
}

// 设置图片上传功能
const setupImageUpload = () => {
  if (!quillInstance) return

  // 获取工具栏中的图片按钮
  const toolbar = quillInstance.getModule('toolbar')
  const imageButton = editorRef.value.querySelector('.ql-image')

  if (imageButton) {
    imageButton.addEventListener('click', () => {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.setAttribute('type', 'file')
      input.setAttribute('accept', 'image/*')
      input.style.display = 'none'

      // 监听文件选择
      input.addEventListener('change', async (e) => {
        const file = e.target.files[0]
        if (!file) return

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
          ElMessage.error('请选择图片文件')
          return
        }

        // 验证文件大小（限制为5MB）
        if (file.size > 5 * 1024 * 1024) {
          ElMessage.error('图片大小不能超过5MB')
          return
        }

        try {
          // 创建FormData
          const formData = new FormData()
          formData.append('multipartFile', file)

          // 显示上传进度
          ElMessage.info('正在上传图片...')

          // 调用上传接口
          const result = await api.upload.uploadFile(formData, (progressEvent) => {
            if (progressEvent.total > 0) {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              console.log(`图片上传进度: ${percentCompleted}%`)
            }
          })

          if (result.code === '200' && result.data) {
            // 获取当前光标位置
            const range = quillInstance.getSelection()
            const index = range ? range.index : quillInstance.getLength()

            // 获取图片URL，支持多种返回格式
            const imageUrl = result.data.url || result.data.file_url || result.data.fileUrl || result.data

            if (imageUrl) {
              // 插入图片
              quillInstance.insertEmbed(index, 'image', imageUrl)

              // 移动光标到图片后面
              quillInstance.setSelection(index + 1)

              ElMessage.success('图片上传成功')
              console.log('✅ 图片插入成功:', imageUrl)
            } else {
              ElMessage.error('图片上传成功但未获取到图片地址')
              console.error('❌ 图片上传响应数据异常:', result.data)
            }
          } else {
            ElMessage.error(result.meg || result.msg || '图片上传失败')
            console.error('❌ 图片上传失败:', result)
          }
        } catch (error) {
          console.error('❌ 图片上传异常:', error)

          // 根据错误类型提供更具体的错误信息
          let errorMessage = '图片上传失败，请稍后重试'

          if (error.response) {
            // 服务器响应错误
            if (error.response.status === 413) {
              errorMessage = '图片文件过大，请选择较小的图片'
            } else if (error.response.status === 415) {
              errorMessage = '不支持的图片格式'
            } else if (error.response.status >= 500) {
              errorMessage = '服务器错误，请稍后重试'
            } else {
              errorMessage = error.response.data?.message || error.response.data?.msg || errorMessage
            }
          } else if (error.request) {
            // 网络错误
            errorMessage = '网络连接失败，请检查网络后重试'
          }

          ElMessage.error(errorMessage)
        }

        // 清理文件输入
        document.body.removeChild(input)
      })

      // 添加到DOM并触发点击
      document.body.appendChild(input)
      input.click()
    })
  }
}

// 销毁编辑器
const destroyEditor = () => {
  if (quillInstance) {
    quillInstance = null
  }
}

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (quillInstance && newValue !== quillInstance.root.innerHTML) {
      quillInstance.root.innerHTML = newValue || ''
    }
  }
)

// 监听销毁属性
watch(
  () => props.destroy,
  (shouldDestroy) => {
    if (shouldDestroy) {
      destroyEditor()
    } else {
      nextTick(() => {
        initEditor()
      })
    }
  }
)

// 生命周期
onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

onBeforeUnmount(() => {
  if (props.destroy) {
    destroyEditor()
  }
})

// 暴露方法 - 兼容UEditor接口
defineExpose({
  getContent: () => quillInstance?.root.innerHTML || '',
  setContent: (html) => {
    if (quillInstance) {
      quillInstance.root.innerHTML = html
    }
  },
  getPlainTxt: () => quillInstance?.getText() || '',
  focus: () => quillInstance?.focus(),
  blur: () => quillInstance?.blur(),
  destroy: destroyEditor
})
</script>

<style scoped>
.lb-ueditor {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.editor-container {
  background: white;
}

/* 工具栏样式调整 - 模拟UEditor风格 */
:deep(.ql-toolbar) {
  border-bottom: 1px solid #dcdfe6;
  background: #fafafa;
  padding: 12px;
}

:deep(.ql-container) {
  border: none;
  font-size: 14px;
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

:deep(.ql-editor) {
  padding: 15px;
  color: #606266;
  min-height: 500px;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
  left: 15px;
}

:deep(.ql-snow .ql-tooltip) {
  z-index: 9999;
}

/* 工具栏按钮样式 - UEditor风格 */
:deep(.ql-toolbar .ql-formats) {
  margin-right: 15px;
}

:deep(.ql-toolbar button) {
  padding: 6px;
  margin: 2px;
  border-radius: 4px;
  border: 1px solid transparent;
}

:deep(.ql-toolbar button:hover) {
  background: #e6f7ff;
  color: #409eff;
  border-color: #b3d8ff;
}

:deep(.ql-toolbar button.ql-active) {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

/* 下拉选择器样式 */
:deep(.ql-toolbar .ql-picker) {
  color: #606266;
}

:deep(.ql-toolbar .ql-picker-label) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 4px 8px;
}

:deep(.ql-toolbar .ql-picker-label:hover) {
  border-color: #409eff;
  color: #409eff;
}

:deep(.ql-toolbar .ql-picker-options) {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
}

:deep(.ql-toolbar .ql-picker-item:hover) {
  background: #f5f7fa;
  color: #409eff;
}

/* 编辑器内容样式 */
:deep(.ql-editor h1) {
  font-size: 2em;
  margin: 0.67em 0;
}

:deep(.ql-editor h2) {
  font-size: 1.5em;
  margin: 0.75em 0;
}

:deep(.ql-editor h3) {
  font-size: 1.17em;
  margin: 0.83em 0;
}

:deep(.ql-editor blockquote) {
  border-left: 4px solid #409eff;
  padding-left: 16px;
  margin: 16px 0;
  color: #666;
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 4px;
}

:deep(.ql-editor pre) {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  overflow-x: auto;
}

/* 自定义字体样式 */
:deep(.ql-font-SimSun) { font-family: "SimSun", "宋体"; }
:deep(.ql-font-SimHei) { font-family: "SimHei", "黑体"; }
:deep(.ql-font-Microsoft-YaHei) { font-family: "Microsoft YaHei", "微软雅黑"; }
:deep(.ql-font-KaiTi) { font-family: "KaiTi", "楷体"; }
:deep(.ql-font-FangSong) { font-family: "FangSong", "仿宋"; }
:deep(.ql-font-Arial) { font-family: "Arial"; }
:deep(.ql-font-Times-New-Roman) { font-family: "Times New Roman"; }
:deep(.ql-font-Helvetica) { font-family: "Helvetica"; }
:deep(.ql-font-sans-serif) { font-family: sans-serif; }
:deep(.ql-font-serif) { font-family: serif; }
:deep(.ql-font-monospace) { font-family: monospace; }
:deep(.ql-font-PingFang-SC) { font-family: "PingFang SC", "苹方"; }
:deep(.ql-font-Hiragino-Sans-GB) { font-family: "Hiragino Sans GB", "冬青黑体"; }
:deep(.ql-font-Source-Han-Sans-CN) { font-family: "Source Han Sans CN", "思源黑体"; }
:deep(.ql-font-Noto-Sans-CJK-SC) { font-family: "Noto Sans CJK SC"; }

/* 自定义字体大小样式 */
:deep(.ql-size-8px) { font-size: 8px; }
:deep(.ql-size-9px) { font-size: 9px; }
:deep(.ql-size-10px) { font-size: 10px; }
:deep(.ql-size-11px) { font-size: 11px; }
:deep(.ql-size-12px) { font-size: 12px; }
:deep(.ql-size-14px) { font-size: 14px; }
:deep(.ql-size-16px) { font-size: 16px; }
:deep(.ql-size-18px) { font-size: 18px; }
:deep(.ql-size-20px) { font-size: 20px; }
:deep(.ql-size-22px) { font-size: 22px; }
:deep(.ql-size-24px) { font-size: 24px; }
:deep(.ql-size-26px) { font-size: 26px; }
:deep(.ql-size-28px) { font-size: 28px; }
:deep(.ql-size-30px) { font-size: 30px; }
:deep(.ql-size-32px) { font-size: 32px; }
:deep(.ql-size-36px) { font-size: 36px; }
:deep(.ql-size-42px) { font-size: 42px; }
:deep(.ql-size-48px) { font-size: 48px; }
:deep(.ql-size-56px) { font-size: 56px; }
:deep(.ql-size-64px) { font-size: 64px; }
:deep(.ql-size-72px) { font-size: 72px; }

/* 工具栏样式优化 - 增大图标和按钮 */
:deep(.ql-toolbar) {
  border: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
  padding: 12px;
  background: #fafbfc;
}

:deep(.ql-toolbar .ql-formats) {
  margin-right: 12px;
}

/* 工具栏按钮样式 */
:deep(.ql-toolbar button) {
  width: 32px;
  height: 32px;
  margin: 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

:deep(.ql-toolbar button:hover) {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

:deep(.ql-toolbar button.ql-active) {
  background-color: #1890ff;
  color: white;
}

/* 工具栏图标大小 */
:deep(.ql-toolbar button svg) {
  width: 18px;
  height: 18px;
}

/* 字体选择器样式优化 */
:deep(.ql-picker.ql-font) {
  width: 120px;
}

:deep(.ql-picker.ql-font .ql-picker-label) {
  padding: 6px 10px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: white;
}

:deep(.ql-picker.ql-font .ql-picker-options) {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.ql-picker.ql-font .ql-picker-item) {
  padding: 8px 12px;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

:deep(.ql-picker.ql-font .ql-picker-item:hover) {
  background-color: #f5f5f5;
}

/* 字体大小选择器样式优化 */
:deep(.ql-picker.ql-size) {
  width: 100px;
}

:deep(.ql-picker.ql-size .ql-picker-label) {
  padding: 6px 10px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: white;
}

:deep(.ql-picker.ql-size .ql-picker-options) {
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.ql-picker.ql-size .ql-picker-item) {
  padding: 6px 10px;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

:deep(.ql-picker.ql-size .ql-picker-item:hover) {
  background-color: #f5f5f5;
}

/* 字体大小选择器选项显示优化 */
:deep(.ql-picker.ql-size .ql-picker-item[data-value="8px"]:before) { content: "8px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="9px"]:before) { content: "9px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="10px"]:before) { content: "10px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="11px"]:before) { content: "11px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="12px"]:before) { content: "12px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="14px"]:before) { content: "14px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="16px"]:before) { content: "16px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="18px"]:before) { content: "18px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="20px"]:before) { content: "20px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="22px"]:before) { content: "22px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="24px"]:before) { content: "24px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="26px"]:before) { content: "26px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="28px"]:before) { content: "28px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="30px"]:before) { content: "30px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="32px"]:before) { content: "32px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="36px"]:before) { content: "36px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="42px"]:before) { content: "42px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="48px"]:before) { content: "48px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="56px"]:before) { content: "56px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="64px"]:before) { content: "64px"; }
:deep(.ql-picker.ql-size .ql-picker-item[data-value="72px"]:before) { content: "72px"; }

/* 字体选择器选项显示优化 */
:deep(.ql-picker.ql-font .ql-picker-item[data-value="SimSun"]:before) { content: "宋体"; font-family: "SimSun", "宋体"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="SimHei"]:before) { content: "黑体"; font-family: "SimHei", "黑体"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="Microsoft-YaHei"]:before) { content: "微软雅黑"; font-family: "Microsoft YaHei", "微软雅黑"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="KaiTi"]:before) { content: "楷体"; font-family: "KaiTi", "楷体"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="FangSong"]:before) { content: "仿宋"; font-family: "FangSong", "仿宋"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="Arial"]:before) { content: "Arial"; font-family: "Arial"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="Times-New-Roman"]:before) { content: "Times New Roman"; font-family: "Times New Roman"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="Helvetica"]:before) { content: "Helvetica"; font-family: "Helvetica"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="sans-serif"]:before) { content: "Sans Serif"; font-family: sans-serif; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="serif"]:before) { content: "Serif"; font-family: serif; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="monospace"]:before) { content: "Monospace"; font-family: monospace; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="PingFang-SC"]:before) { content: "苹方"; font-family: "PingFang SC", "苹方"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="Hiragino-Sans-GB"]:before) { content: "冬青黑体"; font-family: "Hiragino Sans GB", "冬青黑体"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="Source-Han-Sans-CN"]:before) { content: "思源黑体"; font-family: "Source Han Sans CN", "思源黑体"; }
:deep(.ql-picker.ql-font .ql-picker-item[data-value="Noto-Sans-CJK-SC"]:before) { content: "Noto Sans"; font-family: "Noto Sans CJK SC"; }

/* 响应式适配 */
@media (max-width: 768px) {
  :deep(.ql-toolbar) {
    padding: 8px;
  }

  :deep(.ql-toolbar .ql-formats) {
    margin-right: 6px;
  }

  :deep(.ql-toolbar button) {
    width: 28px;
    height: 28px;
    margin: 1px;
  }

  :deep(.ql-toolbar button svg) {
    width: 16px;
    height: 16px;
  }

  :deep(.ql-editor) {
    padding: 12px;
    min-height: 300px;
  }

  :deep(.ql-picker.ql-font) {
    width: 100px;
  }

  :deep(.ql-picker.ql-size) {
    width: 80px;
  }

  :deep(.ql-picker.ql-font .ql-picker-label),
  :deep(.ql-picker.ql-size .ql-picker-label) {
    padding: 4px 6px;
    font-size: 12px;
  }
}
</style>
