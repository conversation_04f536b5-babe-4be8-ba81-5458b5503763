<!--
  百度地图组件 - 用于获取经纬度
  作为腾讯地图的备选方案
-->

<template>
  <el-dialog 
    v-model="centerDialogVisible" 
    title="获取经纬度" 
    width="650px" 
    :center="true"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="dialog-inner">
      <div class="map-search">
        <el-input 
          v-model="searchAddress" 
          placeholder="输入地址" 
          @keydown.enter="searchMapAddr"
        />
        <LbButton 
          size="small" 
          type="primary" 
          @click="searchMapAddr"
        >
          搜 索
        </LbButton>
      </div>
      <div id="baidu-map-container" v-loading="mapLoading" element-loading-text="地图加载中...">
        <div v-if="mapError" class="map-error">
          <p>地图加载失败</p>
          <p class="error-detail">{{ errorMessage }}</p>
          <el-button size="small" @click="retryLoadMap">重试</el-button>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmLatLng">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import LbButton from './LbButton.vue'

// 使用 window.BMap 来避免 TypeScript 错误

// Props
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  address: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:dialogVisible', 'selectedLatLng'])

// 响应式数据
const centerDialogVisible = ref(false)
const searchAddress = ref('')
const mapLoading = ref(false)
const mapError = ref(false)
const errorMessage = ref('')
const map = ref(null)
const marker = ref(null)
const geocoder = ref(null)
const latLng = ref({
  lat: 39.916527,
  lng: 116.397128
})

// 动态加载百度地图API
const loadBaiduMapAPI = () => {
  return new Promise((resolve, reject) => {
    if (window.BMap) {
      resolve()
      return
    }
    
    const script = document.createElement('script')
    script.src = 'https://api.map.baidu.com/api?v=3.0&ak=E4805d16520de693a3fe707cdc962045'
    script.onload = () => resolve()
    script.onerror = () => reject(new Error('百度地图API加载失败'))
    document.head.appendChild(script)
  })
}

// 方法
const initMap = async () => {
  mapLoading.value = true
  mapError.value = false
  
  try {
    // 加载百度地图API
    await loadBaiduMapAPI()
    
    const { lat, lng } = latLng.value
    
    // 获取地图容器
    const container = document.getElementById('baidu-map-container')
    if (!container) {
      throw new Error('地图容器未找到')
    }
    
    // 创建地图
    // @ts-ignore
    const BMap = window.BMap
    const mapInstance = new BMap.Map(container)
    const point = new BMap.Point(lng, lat)
    
    mapInstance.centerAndZoom(point, 12)
    mapInstance.enableScrollWheelZoom(true)
    
    map.value = mapInstance
    
    // 添加点击事件
    mapInstance.addEventListener('click', (e) => {
      // 清除之前的标记
      if (marker.value) {
        mapInstance.removeOverlay(marker.value)
      }
      
      const clickPoint = e.point
      latLng.value = {
        lat: clickPoint.lat,
        lng: clickPoint.lng
      }
      
      // 创建新标记
      // @ts-ignore
      marker.value = new window.BMap.Marker(clickPoint)
      mapInstance.addOverlay(marker.value)

      // 显示信息窗口
      // @ts-ignore
      const infoWindow = new window.BMap.InfoWindow(`
        <div style="margin:10px;">
          <p>纬度：${clickPoint.lat}</p>
          <p>经度：${clickPoint.lng}</p>
        </div>
      `)
      mapInstance.openInfoWindow(infoWindow, clickPoint)
    })
    
    mapLoading.value = false
    
  } catch (error) {
    console.error('地图初始化失败:', error)
    mapLoading.value = false
    mapError.value = true
    errorMessage.value = `地图初始化失败: ${error.message}`
    ElMessage.error('地图初始化失败')
  }
}

const searchMapAddr = async () => {
  if (!searchAddress.value.trim()) {
    ElMessage.warning('请输入搜索地址')
    return
  }
  
  if (!map.value) {
    ElMessage.error('地图未初始化')
    return
  }
  
  try {
    if (!geocoder.value) {
      geocoder.value = new BMap.Geocoder()
    }
    
    geocoder.value.getPoint(searchAddress.value, (point) => {
      if (point) {
        latLng.value = {
          lat: point.lat,
          lng: point.lng
        }
        
        map.value.centerAndZoom(point, 15)
        
        // 清除之前的标记
        if (marker.value) {
          map.value.removeOverlay(marker.value)
        }
        
        // 创建新标记
        marker.value = new BMap.Marker(point)
        map.value.addOverlay(marker.value)
        
        // 显示信息窗口
        const infoWindow = new BMap.InfoWindow(`
          <div style="margin:10px;">
            <p>纬度：${point.lat}</p>
            <p>经度：${point.lng}</p>
          </div>
        `)
        map.value.openInfoWindow(infoWindow, point)
      } else {
        ElMessage.error('未找到该地址，请输入更详细的地址')
      }
    })
  } catch (error) {
    console.error('地址搜索失败:', error)
    ElMessage.error('地址搜索失败')
  }
}

const confirmLatLng = () => {
  centerDialogVisible.value = false
  emit('selectedLatLng', latLng.value)
}

const handleClose = () => {
  centerDialogVisible.value = false
  emit('update:dialogVisible', false)
}

const retryLoadMap = () => {
  console.log('重试加载地图...')
  mapError.value = false
  mapLoading.value = true
  
  // 清理之前的地图实例
  if (map.value) {
    map.value = null
  }
  
  // 重新初始化
  setTimeout(() => {
    initMap()
  }, 500)
}

const openMap = () => {
  nextTick(() => {
    setTimeout(() => {
      initMap()
    }, 500)
  })
}

// 监听器
watch(() => props.dialogVisible, (newValue) => {
  if (newValue) {
    centerDialogVisible.value = true
    searchAddress.value = props.address || ''
    openMap()
  }
})

watch(centerDialogVisible, (val) => {
  if (!val) {
    emit('update:dialogVisible', false)
  }
})
</script>

<style scoped>
#baidu-map-container {
  width: 100%;
  max-width: 500px;
  height: 400px;
  margin: 0 auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.dialog-inner {
  padding: 10px 0;
  width: 100%;
}

.map-search {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  gap: 10px;
  padding: 0 20px;
}

.map-search .el-input {
  width: 300px;
}

.dialog-footer {
  text-align: right;
}

.map-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
}

.map-error p {
  margin-bottom: 10px;
  font-size: 14px;
}

.map-error .error-detail {
  font-size: 12px;
  color: #666;
  margin-bottom: 15px;
}
</style>
