<!--
  历史提现管理页面 - FinanceWithdrawHistory.vue
  基于接口文档实现完整的历史提现管理功能
  包含：历史提现列表、搜索筛选、导出等功能
-->

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <TopNav title="历史提现管理" />

    <div class="content-container">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statsData.total || 0 }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statsData.pending || 0 }}</div>
              <div class="stat-label">已提现未领取</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statsData.success || 0 }}</div>
              <div class="stat-label">到账成功</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statsData.failed || 0 }}</div>
              <div class="stat-label">失败/关闭</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" class="search-form">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户ID" prop="userId">
                <el-input size="default" v-model="searchForm.userId" placeholder="请输入用户ID" clearable
                  style="width: 150px" />
              </el-form-item>

              <el-form-item label="提现类型" prop="type">
                <el-select size="default" v-model="searchForm.type" placeholder="请选择提现类型" clearable
                  style="width: 150px">
                  <el-option label="车费" :value="1" />
                  <el-option label="服务费" :value="2" />
                  <el-option label="加盟" :value="3" />
                  <el-option label="用户分销" :value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="状态" prop="status">
                <el-select size="default" v-model="searchForm.status" placeholder="请选择状态" clearable
                  style="width: 150px">
                  <el-option label="内部错误" :value="-1" />
                  <el-option label="已提现，未领取" :value="1" />
                  <el-option label="到账" :value="2" />
                  <el-option label="失败" :value="3" />
                  <el-option label="关闭" :value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="时间范围" prop="timeRange">
                <el-date-picker size="default" v-model="timeRange" type="datetimerange" range-separator="至"
                  start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss" style="width: 350px" @change="handleTimeRangeChange" />
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <LbButton size="default" type="primary" icon="Search" @click="handleSearch" :loading="loading">
                  搜索
                </LbButton>
                <LbButton size="default" icon="RefreshLeft" @click="handleReset">
                  重置
                </LbButton>
                <LbButton size="default" type="success" icon="Download" @click="handleExport" :loading="exportLoading">
                  导出
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table v-loading="loading" :data="tableData" :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontSize: '16px',
          fontWeight: '600'
        }" :cell-style="{
          fontSize: '14px',
          padding: '12px 8px'
        }" style="width: 100%">
          <el-table-column prop="outBillNo" label="订单号" min-width="200" show-overflow-tooltip align="center" />
          <el-table-column prop="userId" label="用户ID" width="100" align="center" />
          <el-table-column prop="type" label="提现类型" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getTypeTagType(scope.row.type)" size="default">
                {{ scope.row.typeText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)" size="default">
                {{ scope.row.statusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="changeTime" label="变更时间" width="180" align="center" />
          <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip align="center" />
          <el-table-column prop="failReason" label="失败原因" min-width="150" show-overflow-tooltip align="center">
            <template #default="scope">
              {{ scope.row.failReason || scope.row.failFriendlyReason || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage :page="searchForm.pageNum" :page-size="searchForm.pageSize" :total="total"
        @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const searchFormRef = ref()
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const timeRange = ref([])

// 统计数据
const statsData = ref({
  total: 0,
  pending: 0,
  success: 0,
  failed: 0
})

// 搜索表单
const searchForm = reactive({
  userId: '',
  status: '',
  type: '',
  beginTime: '',
  endTime: '',
  pageNum: 1,
  pageSize: 10
})

// 方法
const handleTimeRangeChange = (value) => {
  if (value && value.length === 2) {
    searchForm.beginTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.beginTime = ''
    searchForm.endTime = ''
  }
}

const getTypeTagType = (type) => {
  const typeMap = {
    1: 'primary',  // 车费
    2: 'success',  // 服务费
    3: 'warning',  // 加盟
    4: 'info'      // 用户分销
  }
  return typeMap[type] || 'info'
}

const getStatusTagType = (status) => {
  const statusMap = {
    '-1': 'danger',   // 内部错误
    1: 'warning',     // 已提现，未领取
    2: 'success',     // 到账
    3: 'danger',      // 失败
    4: 'info'         // 关闭
  }
  return statusMap[status] || 'info'
}

const handleSearch = () => {
  searchForm.pageNum = 1
  loadHistoryList()
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  timeRange.value = []
  searchForm.beginTime = ''
  searchForm.endTime = ''
  searchForm.pageNum = 1
  loadHistoryList()
}

// 导出Excel - 参考ServicePeizhi.vue的下载模板方法实现
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出历史提现Excel...')

    // 构建导出参数，过滤空值
    const params = {}
    Object.keys(searchForm).forEach(key => {
      if (searchForm[key] !== '' && searchForm[key] !== null && searchForm[key] !== undefined && key !== 'pageNum' && key !== 'pageSize') {
        params[key] = searchForm[key]
      }
    })

    console.log('📤 导出参数:', params)

    // 使用API调用方式导出
    const result = await proxy.$api.finance.withdrawHistoryExport(params)
    console.log('📤 导出API响应:', result)

    // 如果API返回的是文件流或下载链接
    if (result) {
      // 如果返回的是blob数据，直接下载
      if (result instanceof Blob) {
        const url = window.URL.createObjectURL(result)
        const link = document.createElement('a')
        link.href = url
        link.download = `历史提现导出_${new Date().toISOString().slice(0, 10)}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        // 如果API返回的是下载URL或其他格式，使用原来的方式
        const downloadUrl = `${import.meta.env.VITE_API_BASE_URL || ''}/api/admin/wallet/history/export`

        // 构建URL参数
        const urlParams = new URLSearchParams(params)
        const fullUrl = urlParams.toString() ? `${downloadUrl}?${urlParams.toString()}` : downloadUrl

        // 创建一个隐藏的下载链接
        const link = document.createElement('a')
        link.href = fullUrl
        link.download = `历史提现导出_${new Date().toISOString().slice(0, 10)}.xlsx`
        link.target = '_blank'

        // 添加token到请求头（如果需要）
        const token = sessionStorage.getItem('minitk')
        if (token) {
          // 对于文件下载，我们需要在URL中添加token参数
          const separator = fullUrl.includes('?') ? '&' : '?'
          link.href = `${fullUrl}${separator}token=${encodeURIComponent(token)}`
        }

        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      ElMessage.success('导出开始，请查看浏览器下载')
      console.log('✅ 导出历史提现Excel成功')
    }

  } catch (error) {
    console.error('❌ 导出历史提现Excel异常:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  loadHistoryList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  loadHistoryList()
}

// API调用方法
const loadHistoryList = async () => {
  try {
    loading.value = true

    // 准备查询参数
    const queryParams = { ...searchForm }

    // 转换参数类型
    if (queryParams.userId) queryParams.userId = parseInt(queryParams.userId)
    if (queryParams.status) queryParams.status = parseInt(queryParams.status)
    if (queryParams.type) queryParams.type = parseInt(queryParams.type)

    const result = await proxy.$api.finance.withdrawHistoryList(queryParams)
    console.log('📋 历史提现列表响应:', result)

    if (result.code === 200 || result.code === '200') {
      tableData.value = result.data.list || []
      total.value = result.data.totalCount || 0

      // 计算统计数据
      const list = result.data.list || []
      statsData.value = {
        total: result.data.totalCount || 0,
        pending: list.filter(item => item.status === 1).length,
        success: list.filter(item => item.status === 2).length,
        failed: list.filter(item => [3, 4, -1].includes(item.status)).length
      }

      console.log('📊 统计数据更新:', statsData.value)
      console.log('📋 表格数据更新:', tableData.value.length, '条记录')
    } else {
      ElMessage.error(result.msg || '获取历史提现列表失败')
    }
  } catch (error) {
    console.error('❌ 获取历史提现列表失败:', error)
    ElMessage.error('获取历史提现列表失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  console.log('🚀 历史提现管理页面初始化')
  console.log('📊 初始统计数据:', statsData.value)
  console.log('📋 初始表格数据:', tableData.value)
  loadHistoryList()
})
</script>

<style scoped>
/* ===== 页面容器样式 ===== */
.page-container {
  padding: 0;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* ===== 内容容器样式 ===== */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* ===== 统计卡片样式 ===== */
.stats-cards {
  margin-bottom: 20px;
}

/* 统计内容布局 */
.stat-content {
  text-align: center;
  padding: 10px 0;
}

/* ===== 搜索表单样式 ===== */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* ===== 表格容器样式 ===== */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* ===== 表格样式优化 ===== */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-table__body-wrapper) {
  border: none;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa !important;
}

:deep(.el-table th) {
  border-bottom: 2px solid #e4e7ed;
}

/* ===== 按钮样式 ===== */
.el-button+.el-button {
  margin-left: 8px;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .content-container {
    margin: 10px;
    padding: 15px;
  }

  .search-form-container {
    padding: 15px;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .stats-cards {
    margin-bottom: 15px;
  }
}

@media (max-width: 480px) {
  .content-container {
    margin: 5px;
    padding: 10px;
  }

  .search-form-container {
    padding: 10px;
  }

  .stats-cards {
    margin-bottom: 10px;
  }
}
</style>
