<!--
  地图组件 - 用于获取经纬度
  基于原版后台的 lbMap.vue 重构为 Vue 3 Composition API
-->

<template>
  <el-dialog
    v-model="centerDialogVisible"
    title="获取经纬度"
    width="650px"
    :center="true"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="dialog-inner">
      <div class="map-search">
        <el-input 
          v-model="searchAddress" 
          placeholder="输入地址" 
          @keydown.enter="searchMapAddr"
        />
        <LbButton 
          size="small" 
          type="primary" 
          @click="searchMapAddr"
        >
          搜 索
        </LbButton>
      </div>
      <div :id="mapContainerId" v-loading="mapLoading" element-loading-text="地图加载中...">
        <div v-if="mapError" class="map-error">
          <p>地图加载失败</p>
          <p class="error-detail">{{ errorMessage }}</p>
          <el-button size="small" @click="retryLoadMap">重试</el-button>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmLatLng">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import LbButton from './LbButton.vue'
import { loadTencentMapAPI, createTencentMap, handleMapAPIError } from '@/utils/mapLoader'

// Props
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  address: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:dialogVisible', 'selectedLatLng'])

// 响应式数据
const centerDialogVisible = ref(false)
const searchAddress = ref('')
const mapLoading = ref(false)
const mapError = ref(false)
const errorMessage = ref('')
const mapContainerId = ref(`map-container-${Date.now()}`)
const map = ref(null)
const info = ref(null)
const marker = ref(null)
const geocoder = ref(null)
const latLng = ref({
  lat: 30.657535,
  lng: 104.065783
})

// 方法
const initMap = async () => {
  mapLoading.value = true
  mapError.value = false

  try {
    // 使用新的地图加载器
    await loadTencentMapAPI()

    const { lat, lng } = latLng.value
  
  try {
    console.log('创建地图实例...')

    // 获取地图容器
    const container = document.getElementById(mapContainerId.value)
    if (!container) {
      console.error('地图容器未找到:', mapContainerId.value)
      mapError.value = true
      mapLoading.value = false
      return
    }

    console.log('地图容器已找到:', container)
    console.log('容器尺寸:', container.offsetWidth, 'x', container.offsetHeight)
    console.log('容器可见性:', container.offsetParent !== null)

    // 中心坐标 - 使用和测试页面相同的坐标
    const center = new qq.maps.LatLng(lat, lng)
    console.log('设置中心坐标:', lat, lng)

    // 创建地图 - 使用和测试页面相同的配置
    const mapInstance = new qq.maps.Map(container, {
      center: center,
      zoom: 13
    })

    console.log('地图实例创建成功:', mapInstance)
    map.value = mapInstance

    // 添加一个默认标记
    const defaultMarker = new qq.maps.Marker({
      position: center,
      map: mapInstance
    })
    marker.value = defaultMarker

    // 强制重绘地图，确保正确显示
    setTimeout(() => {
      qq.maps.event.trigger(mapInstance, 'resize')
      mapInstance.setCenter(center)
      console.log('地图重绘完成')
    }, 100)

    // 设置加载完成
    mapLoading.value = false
    console.log('地图初始化完成')

    // 创建信息窗口
    info.value = new qq.maps.InfoWindow({
      map: mapInstance
    })
    
    // 地图点击事件
    qq.maps.event.addListener(mapInstance, 'click', (event) => {
      // 清除之前的标记
      if (marker.value) {
        marker.value.setMap(null)
      }
      
      const { lat: clickLat, lng: clickLng } = event.latLng
      latLng.value = event.latLng
      
      // 创建新标记
      marker.value = new qq.maps.Marker({
        position: new qq.maps.LatLng(clickLat, clickLng),
        map: mapInstance
      })
      
      // 显示信息窗口
      info.value.open()
      info.value.setContent(`
        <div style="margin:10px;">
          <p>纬度：${clickLat}</p>
          <p>经度：${clickLng}</p>
        </div>
      `)
      info.value.setPosition(new qq.maps.LatLng(clickLat, clickLng))
    })
  } catch (error) {
    console.error('地图初始化失败:', error)
    mapLoading.value = false
    mapError.value = true
    errorMessage.value = `地图初始化失败: ${error.message}`
    ElMessage.error('地图初始化失败')

    // 使用错误处理器
    handleMapAPIError(error, () => {
      console.log('地图API错误，可以在这里添加降级处理')
    })
  }
}

const openQQMap = () => {
  console.log('开始初始化地图...')

  nextTick(() => {
    // 等待对话框完全打开
    setTimeout(async () => {
      console.log('DOM已准备，开始初始化地图')
      await initMap()
      await initGeocoder()
    }, 1200) // 增加延迟时间，确保对话框和DOM完全渲染
  })
}

const searchMapAddr = () => {
  if (!searchAddress.value.trim()) {
    ElMessage.warning('请输入搜索地址')
    return
  }
  
  if (geocoder.value) {
    geocoder.value.getLocation(searchAddress.value)
  }
}

const initGeocoder = async () => {
  try {
    // 确保地图API已加载
    await loadTencentMapAPI()
  
  try {
    geocoder.value = new qq.maps.Geocoder()
    
    // 设置搜索成功回调
    geocoder.value.setComplete((result) => {
      const { lat, lng } = result.detail.location
      latLng.value = result.detail.location
      
      // 设置地图中心
      map.value.setCenter(result.detail.location)
      
      // 清除之前的标记
      if (marker.value) {
        marker.value.setMap(null)
      }
      
      // 创建新标记
      marker.value = new qq.maps.Marker({
        map: map.value,
        position: result.detail.location
      })
      
      // 显示信息窗口
      info.value.open()
      info.value.setContent(`
        <div style="margin:10px;">
          <p>纬度：${lat}</p>
          <p>经度：${lng}</p>
        </div>
      `)
      info.value.setPosition(new qq.maps.LatLng(lat, lng))
    })
    
    // 设置搜索失败回调
    geocoder.value.setError(() => {
      ElMessage.error('请输入包含市级的地址！')
    })
  } catch (error) {
    console.error('地理编码器初始化失败:', error)
    handleMapAPIError(error)
  }
}

const confirmLatLng = () => {
  centerDialogVisible.value = false
  emit('selectedLatLng', latLng.value)
}

const handleClose = () => {
  centerDialogVisible.value = false
  emit('update:dialogVisible', false)
}

const retryLoadMap = () => {
  console.log('重试加载地图...')
  mapError.value = false
  mapLoading.value = true

  // 清理之前的地图实例
  if (map.value) {
    map.value = null
  }

  // 重新初始化
  setTimeout(() => {
    initMap()
    initGeocoder()
  }, 500)
}

// 监听器
watch(() => props.dialogVisible, (newValue) => {
  if (newValue) {
    centerDialogVisible.value = true
    searchAddress.value = props.address || ''
    // 延迟初始化，确保对话框完全打开
    setTimeout(() => {
      openQQMap()
    }, 300)
  }
})

watch(centerDialogVisible, (val) => {
  if (!val) {
    emit('update:dialogVisible', false)
  }
})
</script>

<style scoped>
/* 使用属性选择器匹配动态ID */
div[id^="map-container-"] {
  width: 500px;
  height: 400px;
  margin: 0 auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f5f5f5;
  position: relative;
}

.dialog-inner {
  padding: 10px 0;
  width: 100%;
}

.map-search {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  gap: 10px;
  padding: 0 20px;
}

.map-search .el-input {
  width: 300px;
}

.dialog-footer {
  text-align: right;
}

/* 确保地图API样式不被覆盖 */
div[id^="map-container-"] :deep(.qq-maps-api) {
  width: 100% !important;
  height: 100% !important;
}

div[id^="map-container-"] :deep(img) {
  max-width: none !important;
}

div[id^="map-container-"] :deep(div) {
  box-sizing: content-box !important;
}

.map-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
}

.map-error p {
  margin-bottom: 10px;
  font-size: 14px;
}

.map-error .error-detail {
  font-size: 12px;
  color: #666;
  margin-bottom: 15px;
}
</style>
