<template>
  <div class="app-version-container">
    <TopNav title="APP版本管理" />
    
    <div class="content-wrapper">
      <!-- 搜索和操作区域 -->
      <el-card class="search-card" shadow="never">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="应用ID">
            <el-input 
              v-model="searchForm.appid" 
              placeholder="请输入应用ID" 
              clearable 
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="版本号">
            <el-input 
              v-model="searchForm.version" 
              placeholder="请输入版本号" 
              clearable 
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="平台类型">
            <el-select 
              v-model="searchForm.platform" 
              placeholder="请选择平台类型" 
              clearable 
              style="width: 150px"
            >
              <el-option label="师傅端" :value="1" />
              <el-option label="用户端" :value="2" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="强制更新">
            <el-select 
              v-model="searchForm.forceUpdate" 
              placeholder="请选择" 
              clearable 
              style="width: 120px"
            >
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </LbButton>
            <LbButton @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </LbButton>
          </el-form-item>
        </el-form>
        
        <div class="action-buttons">
          <LbButton type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增版本
          </LbButton>
        </div>
      </el-card>

      <!-- 版本列表 -->
      <el-card class="table-card" shadow="never">
        <el-table 
          :data="tableData" 
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="appid" label="应用ID" width="150" />
          <el-table-column prop="version" label="版本号" width="120" />
          <el-table-column prop="platformName" label="平台类型" width="120" />
          <el-table-column prop="description" label="升级说明" min-width="200" show-overflow-tooltip />
          <el-table-column label="强制更新" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.forceUpdate === 1 ? 'danger' : 'success'">
                {{ row.forceUpdate === 1 ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="180" />
          <el-table-column label="操作" width="400" fixed="right">
            <template #default="{ row }">
              <LbButton type="primary" size="small" @click="handleView(row)">
                详情
              </LbButton>
              <LbButton type="warning" size="small" @click="handleEdit(row)">
                编辑
              </LbButton>
              <LbButton type="danger" size="small" @click="handleDelete(row)">
                删除
              </LbButton>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="120px"
      >
        <el-form-item label="应用ID" prop="appid">
          <el-input v-model="formData.appid" placeholder="请输入应用ID" />
        </el-form-item>
        
        <el-form-item label="版本号" prop="version">
          <el-input v-model="formData.version" placeholder="请输入版本号，如：1.0.2" />
        </el-form-item>
        
        <el-form-item label="平台类型" prop="platform">
          <el-select v-model="formData.platform" placeholder="请选择平台类型" style="width: 100%">
            <el-option label="师傅端" :value="1" />
            <el-option label="用户端" :value="2" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="下载地址" prop="wgtUrl">
          <div style="display: flex; gap: 10px; align-items: center;">
            <el-input v-model="formData.wgtUrl" placeholder="请输入wgt升级包下载地址或点击上传文件" />
            <LbButton type="success" @click="handleUpload">
              <el-icon><Upload /></el-icon>
              上传文件
            </LbButton>
          </div>
        </el-form-item>
        
        <el-form-item label="升级说明" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入升级说明"
          />
        </el-form-item>
        
        <el-form-item label="强制更新" prop="forceUpdate">
          <el-radio-group v-model="formData.forceUpdate">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog 
      v-model="detailVisible" 
      title="版本详情" 
      width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">{{ detailData.id }}</el-descriptions-item>
        <el-descriptions-item label="应用ID">{{ detailData.appid }}</el-descriptions-item>
        <el-descriptions-item label="版本号">{{ detailData.version }}</el-descriptions-item>
        <el-descriptions-item label="平台类型">{{ detailData.platformName }}</el-descriptions-item>
        <el-descriptions-item label="下载地址" span="2">
          <el-link :href="detailData.wgtUrl" target="_blank" type="primary">
            {{ detailData.wgtUrl }}
          </el-link>
        </el-descriptions-item>
        <el-descriptions-item label="升级说明" span="2">{{ detailData.description }}</el-descriptions-item>
        <el-descriptions-item label="强制更新">
          <el-tag :type="detailData.forceUpdate === 1 ? 'danger' : 'success'">
            {{ detailData.forceUpdate === 1 ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detailData.updateTime }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 文件上传对话框 -->
    <el-dialog
      v-model="uploadVisible"
      title="上传版本包"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-upload
        ref="uploadRef"
        :action="uploadAction"
        :headers="uploadHeaders"
        :before-upload="beforeUpload"
        :on-success="onUploadSuccess"
        :on-error="onUploadError"
        :file-list="fileList"
        :limit="1"
        accept=".wgt"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传.wgt文件，且不超过50MB
          </div>
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="uploadVisible = false">关闭</LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Upload, UploadFilled } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import sysApi from '@/api-v2/modules/sys'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const detailVisible = ref(false)
const uploadVisible = ref(false)
const formRef = ref()
const uploadRef = ref()
const fileList = ref([])

// 搜索表单
const searchForm = reactive({
  appid: '',
  version: '',
  platform: null,
  forceUpdate: null
})

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  totalCount: 0,
  totalPage: 0
})

// 表单数据
const formData = reactive({
  id: null,
  appid: '',
  version: '',
  platform: null,
  wgtUrl: '',
  description: '',
  forceUpdate: 1
})

// 详情数据
const detailData = reactive({
  id: null,
  appid: '',
  version: '',
  platform: null,
  platformName: '',
  wgtUrl: '',
  description: '',
  forceUpdate: null,
  updateTime: ''
})

// 表单验证规则
const formRules = {
  appid: [
    { required: true, message: '请输入应用ID', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台类型', trigger: 'change' }
  ],
  wgtUrl: [
    { required: true, message: '请输入下载地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入升级说明', trigger: 'blur' }
  ],
  forceUpdate: [
    { required: true, message: '请选择是否强制更新', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return formData.id ? '编辑版本' : '新增版本'
})

const uploadAction = computed(() => {
  // 获取当前环境的基础URL
  const isDevelopment = process.env.NODE_ENV === 'development'

  if (isDevelopment) {
    // 开发环境使用相对路径，通过代理处理
    return '/api/admin/config/appWgt/upload'
  } else {
    // 生产环境需要完整的URL
    // 根据您提供的信息，线上上传接口的URL格式是：https://m.zskj.asia/img/api/admin/config/appWgt/upload
    // 这里需要特殊处理，因为上传接口的域名路径与普通API不同

    // 检查是否是线上环境（通过域名判断）
    const currentHost = window.location.hostname
    if (currentHost === 'm.zskj.asia' || currentHost.includes('zskj.asia')) {
      // 线上环境使用特殊的上传域名
      return 'https://m.zskj.asia/ims/api/admin/config/appWgt/upload'
    } else {
      // 其他生产环境使用标准配置
      let baseURL = 'http://************:8889/ims'

      if (typeof window !== 'undefined' && window.APP_CONFIG && window.APP_CONFIG.api && window.APP_CONFIG.api.baseURL) {
        baseURL = window.APP_CONFIG.api.baseURL
      }

      return `${baseURL}/api/admin/config/appWgt/upload`
    }
  }
})

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('token')
  return {
    'Authorization': `Bearer ${token}`
  }
})

// 方法
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await sysApi.getAppWgtList(params)
    if (response.code === '200') {
      tableData.value = response.data.list || []
      pagination.totalCount = response.data.totalCount || 0
      pagination.totalPage = response.data.totalPage || 0
    } else {
      ElMessage.error(response.msg || '获取列表失败')
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageNum = 1
  getList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    appid: '',
    version: '',
    platform: null,
    forceUpdate: null
  })
  pagination.pageNum = 1
  getList()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  getList()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  getList()
}

const handleAdd = () => {
  Object.assign(formData, {
    id: null,
    appid: '',
    version: '',
    platform: null,
    wgtUrl: '',
    description: '',
    forceUpdate: 1
  })
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  try {
    const response = await sysApi.getAppWgtDetail(row.id)
    if (response.code === '200') {
      Object.assign(formData, response.data)
      dialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const handleView = async (row) => {
  try {
    const response = await sysApi.getAppWgtDetail(row.id)
    if (response.code === '200') {
      Object.assign(detailData, response.data)
      detailVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除版本 ${row.version} 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await sysApi.deleteAppWgt(row.id)
      if (response.code === '200') {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    const isEdit = !!formData.id
    const apiMethod = isEdit ? sysApi.updateAppWgt : sysApi.addAppWgt

    const response = await apiMethod(formData)
    if (response.code === '200') {
      ElMessage.success(isEdit ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getList()
    } else {
      ElMessage.error(response.msg || (isEdit ? '更新失败' : '新增失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

const handleUpload = () => {
  // 检查是否在新增/编辑对话框中
  if (!dialogVisible.value) {
    ElMessage.warning('请先打开新增版本对话框')
    return
  }
  fileList.value = []
  uploadVisible.value = true
}

const beforeUpload = (file) => {
  const isWgt = file.name.toLowerCase().endsWith('.wgt')
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isWgt) {
    ElMessage.error('只能上传.wgt格式的文件!')
    return false
  }
  if (!isLt50M) {
    ElMessage.error('上传文件大小不能超过50MB!')
    return false
  }
  return true
}

const onUploadSuccess = (response) => {
  if (response.code === '200') {
    ElMessage.success('上传成功')
    // 将返回的下载地址填入到表单中
    if (response.data) {
      formData.wgtUrl = response.data
    }
    uploadVisible.value = false
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}

const onUploadError = (error) => {
  console.error('上传失败:', error)
  ElMessage.error('上传失败')
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-version-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.content-wrapper {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.table-card {
  flex: 1;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-upload-dragger) {
  width: 100%;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}
</style>
