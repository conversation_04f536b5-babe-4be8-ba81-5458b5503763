<!--
  数据统计概览模块
   统计功能的Vue3重构版本
  
  功能：
  - 关键指标展示
  - 趋势图表
  - 实时数据监控
  - 数据对比分析
-->

<template>
  <div class="statistics-overview">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>数据概览</h1>
      <p class="page-subtitle">实时监控系统关键指标和数据趋势</p>
    </div>

    <!-- 时间范围选择 -->
    <div class="time-range-selector">
      <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
        <el-radio-button label="today">今日</el-radio-button>
        <el-radio-button label="week">本周</el-radio-button>
        <el-radio-button label="month">本月</el-radio-button>
        <el-radio-button label="quarter">本季度</el-radio-button>
        <el-radio-button label="year">本年</el-radio-button>
      </el-radio-group>
      <el-date-picker
        v-model="customDateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        @change="handleCustomDateChange"
        style="margin-left: 16px"
      />
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-grid">
      <div 
        v-for="metric in keyMetrics" 
        :key="metric.key"
        class="metric-card"
        :class="{ 'metric-card-loading': metricsLoading }"
      >
        <div class="metric-icon" :style="{ backgroundColor: metric.color }">
          <el-icon :size="32">
            <component :is="metric.icon" />
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">
            <el-skeleton v-if="metricsLoading" :rows="1" animated />
            <span v-else>{{ formatNumber(metric.value) }}</span>
          </div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-change" :class="metric.trend">
            <el-icon v-if="!metricsLoading">
              <TrendCharts v-if="metric.trend === 'up'" />
              <Bottom v-else />
            </el-icon>
            <span v-if="!metricsLoading">{{ metric.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 访问量趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>访问量趋势</h3>
            <div class="chart-controls">
              <el-button-group size="small">
                <el-button 
                  :type="visitChartType === 'line' ? 'primary' : ''" 
                  @click="visitChartType = 'line'"
                >
                  线图
                </el-button>
                <el-button 
                  :type="visitChartType === 'bar' ? 'primary' : ''" 
                  @click="visitChartType = 'bar'"
                >
                  柱图
                </el-button>
              </el-button-group>
            </div>
          </div>
          <div class="chart-content">
            <el-skeleton v-if="chartsLoading" :rows="8" animated />
            <div v-else class="mock-chart">
              <div class="chart-placeholder">
                <el-icon :size="64"><TrendCharts /></el-icon>
                <p>{{ visitChartType === 'line' ? '线性' : '柱状' }}访问量趋势图</p>
                <div class="chart-data">
                  <div class="data-point" v-for="(point, index) in visitTrendData" :key="index">
                    <span class="data-label">{{ point.date }}</span>
                    <span class="data-value">{{ point.visits }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户分布饼图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户分布</h3>
            <el-dropdown @command="handleUserDistributionType">
              <el-button size="small">
                {{ userDistributionType === 'region' ? '地域分布' : '设备分布' }}
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="region">地域分布</el-dropdown-item>
                  <el-dropdown-item command="device">设备分布</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="chart-content">
            <el-skeleton v-if="chartsLoading" :rows="6" animated />
            <div v-else class="mock-chart">
              <div class="chart-placeholder">
                <el-icon :size="64"><PieChart /></el-icon>
                <p>{{ userDistributionType === 'region' ? '地域' : '设备' }}分布图</p>
                <div class="distribution-data">
                  <div 
                    class="distribution-item" 
                    v-for="(item, index) in currentDistributionData" 
                    :key="index"
                  >
                    <div class="distribution-color" :style="{ backgroundColor: item.color }"></div>
                    <span class="distribution-label">{{ item.name }}</span>
                    <span class="distribution-value">{{ item.value }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时数据监控 -->
      <div class="realtime-section">
        <div class="chart-card">
          <div class="chart-header">
            <h3>实时数据监控</h3>
            <div class="realtime-status">
              <el-icon class="status-icon online"><CircleCheck /></el-icon>
              <span>实时更新中</span>
            </div>
          </div>
          <div class="chart-content">
            <div class="realtime-metrics">
              <div class="realtime-metric" v-for="metric in realtimeMetrics" :key="metric.key">
                <div class="realtime-label">{{ metric.label }}</div>
                <div class="realtime-value">{{ metric.value }}</div>
                <div class="realtime-unit">{{ metric.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="data-table-section">
      <div class="table-card">
        <div class="table-header">
          <h3>热门页面排行</h3>
          <el-button size="small" @click="refreshTableData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="table-content">
          <el-table
            v-loading="tableLoading"
            :data="popularPages"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="rank" label="排名" width="80" align="center">
              <template #default="{ row }">
                <el-tag 
                  :type="row.rank <= 3 ? 'warning' : 'info'" 
                  size="small"
                >
                  {{ row.rank }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="page" label="页面" min-width="200" show-overflow-tooltip />
            <el-table-column prop="visits" label="访问量" width="120" align="center">
              <template #default="{ row }">
                <strong>{{ formatNumber(row.visits) }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="uniqueVisitors" label="独立访客" width="120" align="center" />
            <el-table-column prop="avgDuration" label="平均停留时间" width="140" align="center" />
            <el-table-column prop="bounceRate" label="跳出率" width="100" align="center">
              <template #default="{ row }">
                <span :class="{ 'high-bounce': row.bounceRate > 60 }">
                  {{ row.bounceRate }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  PieChart,
  Bottom,
  ArrowDown,
  CircleCheck,
  Refresh,
  User,
  Document,
  View,
  Timer
} from '@element-plus/icons-vue'

// 响应式数据
const metricsLoading = ref(true)
const chartsLoading = ref(true)
const tableLoading = ref(false)
const timeRange = ref('today')
const customDateRange = ref([])
const visitChartType = ref('line')
const userDistributionType = ref('region')
const realtimeTimer = ref(null)

// 关键指标数据
const keyMetrics = ref([
  {
    key: 'totalUsers',
    label: '总用户数',
    value: 12345,
    change: '+12.5%',
    trend: 'up',
    color: '#409eff',
    icon: 'User'
  },
  {
    key: 'totalViews',
    label: '总访问量',
    value: 234567,
    change: '+8.2%',
    trend: 'up',
    color: '#67c23a',
    icon: 'View'
  },
  {
    key: 'totalArticles',
    label: '文章总数',
    value: 1234,
    change: '+15.3%',
    trend: 'up',
    color: '#e6a23c',
    icon: 'Document'
  },
  {
    key: 'avgDuration',
    label: '平均停留时间',
    value: 245,
    change: '-2.1%',
    trend: 'down',
    color: '#f56c6c',
    icon: 'Timer'
  }
])

// 访问量趋势数据
const visitTrendData = ref([
  { date: '01-07', visits: 1200 },
  { date: '01-08', visits: 1350 },
  { date: '01-09', visits: 1180 },
  { date: '01-10', visits: 1420 },
  { date: '01-11', visits: 1680 }
])

// 地域分布数据
const regionDistributionData = ref([
  { name: '北京', value: 25, color: '#409eff' },
  { name: '上海', value: 20, color: '#67c23a' },
  { name: '广州', value: 15, color: '#e6a23c' },
  { name: '深圳', value: 12, color: '#f56c6c' },
  { name: '其他', value: 28, color: '#909399' }
])

// 设备分布数据
const deviceDistributionData = ref([
  { name: '桌面端', value: 45, color: '#409eff' },
  { name: '移动端', value: 40, color: '#67c23a' },
  { name: '平板端', value: 15, color: '#e6a23c' }
])

// 实时监控数据
const realtimeMetrics = ref([
  { key: 'onlineUsers', label: '在线用户', value: 1234, unit: '人' },
  { key: 'todayViews', label: '今日访问', value: 5678, unit: '次' },
  { key: 'todayRegisters', label: '今日注册', value: 89, unit: '人' },
  { key: 'serverLoad', label: '服务器负载', value: 45, unit: '%' }
])

// 热门页面数据
const popularPages = ref([
  {
    rank: 1,
    page: '/dashboard',
    visits: 12345,
    uniqueVisitors: 8901,
    avgDuration: '3:45',
    bounceRate: 25
  },
  {
    rank: 2,
    page: '/articles/vue3-guide',
    visits: 9876,
    uniqueVisitors: 7654,
    avgDuration: '5:20',
    bounceRate: 35
  },
  {
    rank: 3,
    page: '/user/profile',
    visits: 7654,
    uniqueVisitors: 5432,
    avgDuration: '2:15',
    bounceRate: 45
  },
  {
    rank: 4,
    page: '/articles/javascript-tips',
    visits: 6543,
    uniqueVisitors: 4321,
    avgDuration: '4:30',
    bounceRate: 40
  },
  {
    rank: 5,
    page: '/about',
    visits: 4321,
    uniqueVisitors: 3210,
    avgDuration: '1:50',
    bounceRate: 65
  }
])

// 计算属性
const currentDistributionData = computed(() => {
  return userDistributionType.value === 'region'
    ? regionDistributionData.value
    : deviceDistributionData.value
})

// 方法
const loadMetricsData = async () => {
  metricsLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 根据时间范围更新数据
    updateMetricsByTimeRange()

  } catch (error) {
    ElMessage.error('加载指标数据失败')
  } finally {
    metricsLoading.value = false
  }
}

const loadChartsData = async () => {
  chartsLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1200))

    // 根据时间范围更新图表数据
    updateChartsByTimeRange()

  } catch (error) {
    ElMessage.error('加载图表数据失败')
  } finally {
    chartsLoading.value = false
  }
}

const refreshTableData = async () => {
  tableLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 随机更新数据
    popularPages.value.forEach(page => {
      page.visits += Math.floor(Math.random() * 100)
      page.uniqueVisitors += Math.floor(Math.random() * 50)
    })

    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('刷新数据失败')
  } finally {
    tableLoading.value = false
  }
}

const updateMetricsByTimeRange = () => {
  // 根据时间范围调整指标数据
  const multipliers = {
    today: 1,
    week: 7,
    month: 30,
    quarter: 90,
    year: 365
  }

  const multiplier = multipliers[timeRange.value] || 1

  keyMetrics.value.forEach(metric => {
    const baseValue = metric.value / (multipliers[timeRange.value] || 1)
    metric.value = Math.floor(baseValue * multiplier)
  })
}

const updateChartsByTimeRange = () => {
  // 根据时间范围更新图表数据
  const dataPoints = {
    today: 24,
    week: 7,
    month: 30,
    quarter: 90,
    year: 12
  }

  const points = dataPoints[timeRange.value] || 7
  const newData = []

  for (let i = 0; i < points; i++) {
    newData.push({
      date: `${String(i + 1).padStart(2, '0')}`,
      visits: Math.floor(Math.random() * 1000) + 800
    })
  }

  visitTrendData.value = newData
}

const handleTimeRangeChange = (value) => {
  timeRange.value = value
  customDateRange.value = []
  loadMetricsData()
  loadChartsData()
}

const handleCustomDateChange = (dates) => {
  if (dates && dates.length === 2) {
    timeRange.value = 'custom'
    loadMetricsData()
    loadChartsData()
  }
}

const handleUserDistributionType = (type) => {
  userDistributionType.value = type
}

const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const updateRealtimeData = () => {
  // 模拟实时数据更新
  realtimeMetrics.value.forEach(metric => {
    const change = Math.floor(Math.random() * 20) - 10
    metric.value = Math.max(0, metric.value + change)
  })
}

const startRealtimeUpdate = () => {
  realtimeTimer.value = setInterval(updateRealtimeData, 5000)
}

const stopRealtimeUpdate = () => {
  if (realtimeTimer.value) {
    clearInterval(realtimeTimer.value)
    realtimeTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  loadMetricsData()
  loadChartsData()
  startRealtimeUpdate()
})

onUnmounted(() => {
  stopRealtimeUpdate()
})
</script>

<style scoped>
.statistics-overview {
  padding: var(--spacing-lg);
}

.page-title {
  margin-bottom: var(--spacing-xl);
}

.page-title h1 {
  font-size: var(--font-size-xxl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.time-range-selector {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.metric-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  transition: var(--transition-box-shadow);
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: var(--box-shadow-base);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light-2));
}

.metric-card-loading {
  opacity: 0.7;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: var(--border-radius-lg);
  color: white;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1;
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.metric-change.up {
  color: var(--color-success);
}

.metric-change.down {
  color: var(--color-danger);
}

.charts-section {
  margin-bottom: var(--spacing-xl);
}

.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.realtime-section {
  margin-bottom: var(--spacing-lg);
}

.chart-card,
.table-card {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.chart-header,
.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
}

.chart-header h3,
.table-header h3 {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.realtime-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.status-icon.online {
  color: var(--color-success);
}

.chart-content,
.table-content {
  padding: var(--spacing-lg);
}

.mock-chart {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: var(--color-text-secondary);
}

.chart-placeholder p {
  margin: var(--spacing-md) 0;
  font-size: var(--font-size-base);
}

.chart-data {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.data-point {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.data-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-placeholder);
}

.data-value {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-primary);
}

.distribution-data {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.distribution-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.distribution-label {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.distribution-value {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-primary);
}

.realtime-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.realtime-metric {
  text-align: center;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-sm);
  background-color: var(--bg-color-column);
}

.realtime-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.realtime-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.realtime-unit {
  font-size: var(--font-size-xs);
  color: var(--color-text-placeholder);
}

.data-table-section {
  margin-bottom: var(--spacing-xl);
}

.high-bounce {
  color: var(--color-danger);
  font-weight: 500;
}

/* Element Plus 表格样式覆盖 */
:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background-color: var(--table-header-bg);
  color: var(--color-text-primary);
  font-weight: 500;
}

:deep(.el-table tr:hover > td) {
  background-color: var(--table-row-hover-bg);
}

/* 骨架屏样式 */
:deep(.el-skeleton) {
  width: 100%;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .statistics-overview {
    padding: var(--spacing-md);
  }

  .time-range-selector {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .metric-card {
    padding: var(--spacing-lg);
  }

  .metric-icon {
    width: 48px;
    height: 48px;
  }

  .metric-value {
    font-size: 24px;
  }

  .chart-content,
  .table-content {
    padding: var(--spacing-md);
  }

  .mock-chart {
    height: 200px;
  }

  .chart-data {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .realtime-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .page-title h1 {
    font-size: var(--font-size-xl);
  }

  .metrics-grid {
    gap: var(--spacing-md);
  }

  .metric-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .realtime-metrics {
    grid-template-columns: 1fr;
  }

  .chart-controls {
    flex-direction: column;
  }
}
</style>
