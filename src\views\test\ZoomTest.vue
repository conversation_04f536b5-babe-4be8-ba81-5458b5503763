<!--
  页面缩放功能测试页面
-->

<template>
  <div class="zoom-test-page page-container">
    <!-- 缩放控制组件 -->
    <ZoomControl target-selector=".zoom-test-page" @zoom-change="handleZoomChange" />
    
    <div class="page-header">
      <h1>页面缩放功能测试</h1>
      <p>当前缩放级别: <strong>{{ currentZoomLevel }}</strong></p>
    </div>

    <div class="content-section">
      <!-- 表格示例 -->
      <el-card class="demo-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>数据表格示例</span>
          </div>
        </template>
        
        <el-table :data="tableData" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="email" label="邮箱" width="200" />
          <el-table-column prop="phone" label="电话" width="150" />
          <el-table-column prop="address" label="地址" min-width="200" />
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleEdit(scope.row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 表单示例 -->
      <el-card class="demo-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>表单示例</span>
          </div>
        </template>
        
        <el-form :model="form" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名">
                <el-input v-model="form.username" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱">
                <el-input v-model="form.email" placeholder="请输入邮箱" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="电话">
                <el-input v-model="form.phone" placeholder="请输入电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="城市">
                <el-select v-model="form.city" placeholder="请选择城市" style="width: 100%">
                  <el-option label="北京" value="beijing" />
                  <el-option label="上海" value="shanghai" />
                  <el-option label="广州" value="guangzhou" />
                  <el-option label="深圳" value="shenzhen" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="地址">
            <el-input v-model="form.address" type="textarea" rows="3" placeholder="请输入详细地址" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary">提交</el-button>
            <el-button>重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 统计卡片示例 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">1,234</div>
            <div class="stat-label">总用户数</div>
            <div class="stat-change positive">
              <el-icon><ArrowUp /></el-icon>
              +12.5%
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">5,678</div>
            <div class="stat-label">订单数量</div>
            <div class="stat-change positive">
              <el-icon><ArrowUp /></el-icon>
              +8.3%
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">¥89,012</div>
            <div class="stat-label">总收入</div>
            <div class="stat-change negative">
              <el-icon><ArrowDown /></el-icon>
              -2.1%
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">98.5%</div>
            <div class="stat-label">满意度</div>
            <div class="stat-change positive">
              <el-icon><ArrowUp /></el-icon>
              +0.8%
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 按钮组示例 -->
      <el-card class="demo-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>按钮组示例</span>
          </div>
        </template>
        
        <div class="button-group">
          <el-button type="primary">主要按钮</el-button>
          <el-button type="success">成功按钮</el-button>
          <el-button type="info">信息按钮</el-button>
          <el-button type="warning">警告按钮</el-button>
          <el-button type="danger">危险按钮</el-button>
        </div>
        
        <div class="button-group">
          <el-button size="large">大型按钮</el-button>
          <el-button>默认按钮</el-button>
          <el-button size="small">小型按钮</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import ZoomControl from '@/components/common/ZoomControl.vue'

// 响应式数据
const currentZoomLevel = ref('正常 (100%)')

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    address: '北京市朝阳区某某街道123号'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    address: '上海市浦东新区某某路456号'
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
    address: '广州市天河区某某大道789号'
  }
])

// 表单数据
const form = reactive({
  username: '',
  email: '',
  phone: '',
  city: '',
  address: ''
})

// 方法
const handleZoomChange = (zoom) => {
  const zoomLabels = {
    'small': '缩小 (80%)',
    'normal': '正常 (100%)',
    'large': '放大 (120%)',
    'extra-large': '超大 (140%)'
  }
  currentZoomLevel.value = zoomLabels[zoom] || '未知'
  ElMessage.success(`页面缩放已调整为: ${currentZoomLevel.value}`)
}

const handleEdit = (row) => {
  ElMessage.info(`编辑用户: ${row.name}`)
}

const handleDelete = (row) => {
  ElMessage.warning(`删除用户: ${row.name}`)
}
</script>

<style scoped>
.zoom-test-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.content-section {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.stats-row {
  margin-bottom: 20px;
}

.button-group {
  margin-bottom: 15px;
}

.button-group .el-button {
  margin-right: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .zoom-test-page {
    padding: 10px;
  }
  
  .stats-row .el-col {
    margin-bottom: 15px;
  }
  
  .button-group .el-button {
    margin-bottom: 10px;
    width: 100%;
  }
}
</style>
