<!--
  用户分析模块
  用户行为和数据分析
-->

<template>
  <div class="user-analysis">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>用户分析</h1>
      <p class="page-subtitle">深入分析用户行为和数据趋势</p>
    </div>

    <!-- 用户概览指标 -->
    <div class="metrics-grid">
      <div class="metric-card" v-for="metric in userMetrics" :key="metric.key">
        <div class="metric-icon" :style="{ backgroundColor: metric.color }">
          <el-icon :size="28">
            <component :is="metric.icon" />
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatNumber(metric.value) }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-change" :class="metric.trend">
            <el-icon>
              <TrendCharts v-if="metric.trend === 'up'" />
              <Bottom v-else />
            </el-icon>
            {{ metric.change }}
          </div>
        </div>
      </div>
    </div>

    <!-- 用户增长趋势 -->
    <div class="chart-section">
      <div class="chart-card">
        <div class="chart-header">
          <h3>用户增长趋势</h3>
          <el-button-group size="small">
            <el-button :type="growthPeriod === 'week' ? 'primary' : ''" @click="growthPeriod = 'week'">
              最近一周
            </el-button>
            <el-button :type="growthPeriod === 'month' ? 'primary' : ''" @click="growthPeriod = 'month'">
              最近一月
            </el-button>
            <el-button :type="growthPeriod === 'year' ? 'primary' : ''" @click="growthPeriod = 'year'">
              最近一年
            </el-button>
          </el-button-group>
        </div>
        <div class="chart-content">
          <div class="mock-chart">
            <div class="chart-placeholder">
              <el-icon :size="64"><TrendCharts /></el-icon>
              <p>用户增长趋势图</p>
              <div class="trend-data">
                <div class="trend-item" v-for="(item, index) in userGrowthData" :key="index">
                  <span class="trend-date">{{ item.date }}</span>
                  <span class="trend-value">+{{ item.newUsers }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户活跃度分析 -->
    <div class="activity-section">
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户活跃度</h3>
          </div>
          <div class="chart-content">
            <div class="activity-stats">
              <div class="activity-item" v-for="activity in activityData" :key="activity.type">
                <div class="activity-label">{{ activity.label }}</div>
                <div class="activity-value">{{ activity.value }}%</div>
                <div class="activity-bar">
                  <div
                    class="activity-progress"
                    :style="{ width: activity.value + '%', backgroundColor: activity.color }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>用户地域分布</h3>
          </div>
          <div class="chart-content">
            <div class="region-stats">
              <div class="region-item" v-for="region in regionData" :key="region.name">
                <div class="region-info">
                  <span class="region-name">{{ region.name }}</span>
                  <span class="region-count">{{ region.count }}人</span>
                </div>
                <div class="region-percentage">{{ region.percentage }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户行为分析表格 -->
    <div class="behavior-section">
      <div class="table-card">
        <div class="table-header">
          <h3>用户行为分析</h3>
          <el-button size="small" @click="refreshBehaviorData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
        <div class="table-content">
          <el-table
            v-loading="behaviorLoading"
            :data="behaviorData"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="action" label="用户行为" width="150" />
            <el-table-column prop="count" label="次数" width="100" align="center">
              <template #default="{ row }">
                <strong>{{ formatNumber(row.count) }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="percentage" label="占比" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getPercentageType(row.percentage)" size="small">
                  {{ row.percentage }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="avgDuration" label="平均时长" width="120" align="center" />
            <el-table-column prop="trend" label="趋势" width="100" align="center">
              <template #default="{ row }">
                <span :class="'trend-' + row.trend">
                  <el-icon>
                    <TrendCharts v-if="row.trend === 'up'" />
                    <Bottom v-else />
                  </el-icon>
                  {{ row.trendValue }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明" min-width="200" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  Bottom,
  Refresh,
  User,
  UserFilled,
  Clock,
  View
} from '@element-plus/icons-vue'

// 响应式数据
const behaviorLoading = ref(false)
const growthPeriod = ref('month')

// 用户指标数据
const userMetrics = ref([
  {
    key: 'totalUsers',
    label: '总用户数',
    value: 15678,
    change: '+12.5%',
    trend: 'up',
    color: '#409eff',
    icon: 'User'
  },
  {
    key: 'activeUsers',
    label: '活跃用户',
    value: 8934,
    change: '+8.2%',
    trend: 'up',
    color: '#67c23a',
    icon: 'UserFilled'
  },
  {
    key: 'newUsers',
    label: '新增用户',
    value: 1234,
    change: '+15.3%',
    trend: 'up',
    color: '#e6a23c',
    icon: 'TrendCharts'
  },
  {
    key: 'avgSessionTime',
    label: '平均会话时长',
    value: 245,
    change: '-2.1%',
    trend: 'down',
    color: '#f56c6c',
    icon: 'Clock'
  }
])

// 用户增长数据
const userGrowthData = ref([
  { date: '01-07', newUsers: 123 },
  { date: '01-08', newUsers: 156 },
  { date: '01-09', newUsers: 134 },
  { date: '01-10', newUsers: 178 },
  { date: '01-11', newUsers: 198 }
])

// 活跃度数据
const activityData = ref([
  { type: 'daily', label: '日活跃用户', value: 75, color: '#409eff' },
  { type: 'weekly', label: '周活跃用户', value: 60, color: '#67c23a' },
  { type: 'monthly', label: '月活跃用户', value: 45, color: '#e6a23c' },
  { type: 'retention', label: '用户留存率', value: 35, color: '#f56c6c' }
])

// 地域分布数据
const regionData = ref([
  { name: '北京', count: 3456, percentage: 22 },
  { name: '上海', count: 2890, percentage: 18 },
  { name: '广州', count: 2234, percentage: 14 },
  { name: '深圳', count: 1890, percentage: 12 },
  { name: '其他', count: 5208, percentage: 34 }
])

// 用户行为数据
const behaviorData = ref([
  {
    action: '页面浏览',
    count: 45678,
    percentage: 35,
    avgDuration: '2:34',
    trend: 'up',
    trendValue: '+5.2%',
    description: '用户浏览页面的总次数'
  },
  {
    action: '搜索查询',
    count: 12345,
    percentage: 25,
    avgDuration: '1:45',
    trend: 'up',
    trendValue: '+8.1%',
    description: '用户执行搜索操作的次数'
  },
  {
    action: '内容互动',
    count: 8901,
    percentage: 20,
    avgDuration: '3:12',
    trend: 'down',
    trendValue: '-2.3%',
    description: '用户与内容的互动次数'
  },
  {
    action: '功能使用',
    count: 6789,
    percentage: 15,
    avgDuration: '4:56',
    trend: 'up',
    trendValue: '+12.7%',
    description: '用户使用各种功能的次数'
  },
  {
    action: '其他操作',
    count: 2345,
    percentage: 5,
    avgDuration: '1:23',
    trend: 'down',
    trendValue: '-1.5%',
    description: '其他用户操作行为'
  }
])

// 方法
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const getPercentageType = (percentage) => {
  if (percentage >= 30) return 'success'
  if (percentage >= 20) return 'warning'
  return 'info'
}

const refreshBehaviorData = async () => {
  behaviorLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 随机更新数据
    behaviorData.value.forEach(item => {
      item.count += Math.floor(Math.random() * 100)
    })

    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('刷新数据失败')
  } finally {
    behaviorLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  console.log('用户分析页面已加载')
})
</script>

<style scoped>
.user-analysis {
  padding: var(--spacing-lg);
}

.page-title {
  margin-bottom: var(--spacing-xl);
}

.page-title h1 {
  font-size: var(--font-size-xxl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.metric-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  transition: var(--transition-box-shadow);
}

.metric-card:hover {
  box-shadow: var(--box-shadow-base);
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: var(--border-radius-lg);
  color: white;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.metric-change.up {
  color: var(--color-success);
}

.metric-change.down {
  color: var(--color-danger);
}

.chart-section,
.activity-section,
.behavior-section {
  margin-bottom: var(--spacing-xl);
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.chart-card,
.table-card {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.chart-header,
.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
}

.chart-header h3,
.table-header h3 {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  margin: 0;
}

.chart-content,
.table-content {
  padding: var(--spacing-lg);
}

.mock-chart {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: var(--color-text-secondary);
}

.chart-placeholder p {
  margin: var(--spacing-md) 0;
}

.trend-data {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.trend-date {
  font-size: var(--font-size-xs);
  color: var(--color-text-placeholder);
}

.trend-value {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-success);
}

.activity-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.activity-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.activity-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.activity-value {
  font-weight: 500;
  color: var(--color-text-primary);
}

.activity-bar {
  height: 8px;
  background-color: var(--bg-color-column);
  border-radius: 4px;
  overflow: hidden;
}

.activity-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.region-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.region-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color-light);
}

.region-item:last-child {
  border-bottom: none;
}

.region-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.region-name {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.region-count {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.region-percentage {
  font-weight: 500;
  color: var(--color-primary);
}

.trend-up {
  color: var(--color-success);
}

.trend-down {
  color: var(--color-danger);
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .user-analysis {
    padding: var(--spacing-md);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .metric-card {
    padding: var(--spacing-lg);
  }

  .chart-content,
  .table-content {
    padding: var(--spacing-md);
  }

  .trend-data {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
</style>
