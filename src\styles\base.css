/**
 * 基础组件样式

 * 
 * 包含：
 * - 基础布局样式
 * - 通用组件样式
 * - 页面结构样式
 * - 业务组件基础样式
 */

/* ===== 基础布局 ===== */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  /* background-color: var(--bg-color-page); */
  transition: var(--transition-base);
}

/* 主要内容区域 */
.main-content {
  padding: var(--spacing-lg);
  background-color: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  margin: var(--spacing-lg);
  transition: var(--transition-box-shadow);
}

.main-content:hover {
  box-shadow: var(--box-shadow-base);
}

/* 页面标题 */
.page-title {
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
  font-weight: 400;
}

.page-title-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-lg);
  font-size: var(--breadcrumb-font-size);
  color: var(--breadcrumb-text-color);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.breadcrumb-separator {
  color: var(--color-text-placeholder);
  margin: var(--breadcrumb-separator-margin);
}

.breadcrumb-link {
  color: var(--breadcrumb-link-color);
  text-decoration: none;
  transition: var(--transition-color);
}

.breadcrumb-link:hover {
  color: var(--color-primary-dark-1);
  text-decoration: underline;
}

/* ===== 卡片组件 ===== */

.card {
  background-color: var(--bg-color-base);
  border: 1px solid var(--border-color-light);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: var(--transition-box-shadow);
}

.card:hover {
  box-shadow: var(--box-shadow-base);
}

.card-header {
  padding: var(--card-header-padding);
  border-bottom: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.card-extra {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.card-body {
  padding: var(--card-body-padding);
}

.card-footer {
  padding: var(--card-footer-padding);
  border-top: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ===== 表单样式 ===== */

.form-container {
  background-color: var(--bg-color-base);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
}

.form-section {
  margin-bottom: var(--spacing-xl);
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.form-row {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--form-item-margin-bottom);
}

.form-col {
  flex: 1;
}

.form-col-auto {
  flex: 0 0 auto;
}

.form-col-2 {
  flex: 0 0 calc(50% - var(--spacing-lg) / 2);
}

.form-col-3 {
  flex: 0 0 calc(33.333% - var(--spacing-lg) * 2 / 3);
}

.form-col-4 {
  flex: 0 0 calc(25% - var(--spacing-lg) * 3 / 4);
}

/* ===== 表格样式 ===== */

.table-container {
  background-color: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.table-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.table-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.table-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.table-body {
  padding: var(--spacing-lg);
}

.table-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ===== 搜索表单 ===== */

.search-form {
  background-color: var(--bg-color-base);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  margin-bottom: var(--spacing-lg);
}

.search-form .form-row {
  margin-bottom: var(--spacing-md);
}

.search-form .form-row:last-child {
  margin-bottom: 0;
}

.search-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color-light);
}

/* ===== 操作按钮组 ===== */

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons .btn {
  margin: 0;
}

/* ===== 状态标签 ===== */

.status-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--tag-padding);
  border-radius: var(--tag-border-radius);
  font-size: var(--tag-font-size);
  font-weight: 500;
  height: var(--tag-height);
  margin: var(--tag-margin);
  transition: var(--transition-base);
}

.status-tag.success {
  background-color: var(--color-success-light);
  color: var(--color-success-dark);
  border: 1px solid var(--color-success);
}

.status-tag.warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning-dark);
  border: 1px solid var(--color-warning);
}

.status-tag.danger {
  background-color: var(--color-danger-light);
  color: var(--color-danger-dark);
  border: 1px solid var(--color-danger);
}

.status-tag.info {
  background-color: var(--color-info-light);
  color: var(--color-info-dark);
  border: 1px solid var(--color-info);
}

.status-tag.primary {
  background-color: var(--bg-color-primary-light);
  color: var(--color-primary-dark-1);
  border: 1px solid var(--color-primary);
}

/* ===== 空状态 ===== */

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  text-align: center;
  min-height: 200px;
}

.empty-icon {
  font-size: 64px;
  color: var(--color-text-placeholder);
  margin-bottom: var(--spacing-lg);
}

.empty-title {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
}

.empty-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-placeholder);
  margin-bottom: var(--spacing-lg);
  max-width: 300px;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

/* ===== 加载状态 ===== */

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  min-height: 200px;
}

.loading-spinner {
  width: var(--loading-spinner-size);
  height: var(--loading-spinner-size);
  border: var(--loading-spinner-border) solid var(--border-color-light);
  border-top: var(--loading-spinner-border) solid var(--color-primary);
  border-radius: 50%;
  animation: spin var(--animation-duration-base) linear infinite;
  margin-bottom: var(--loading-text-margin);
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== 工具栏 ===== */

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--bg-color-base);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-md);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.toolbar-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0;
}
