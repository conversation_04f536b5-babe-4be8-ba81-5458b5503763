/**
 * Service模块组件样式模板
 * 提供可复用的组件样式类，便于在其他页面复用
 * 
 * 使用方式：
 * 1. 导入样式文件：import '@/styles/service-components.css'
 * 2. 在模板中使用对应的CSS类名
 * 
 * 组件列表：
 * - 页面布局组件
 * - 搜索表单组件
 * - 数据表格组件
 * - 操作按钮组件
 * - 状态显示组件
 */

/* ===== 页面布局组件模板 ===== */

/* 标准页面布局 */
.service-page-layout {
  padding: 0;
  min-height: calc(100vh - 60px);
  background-color: var(--service-bg-page);
}

.service-page-layout__content {
  background: var(--service-bg-container);
  border-radius: var(--service-radius-container);
  padding: var(--service-spacing-xl);
  box-shadow: var(--service-shadow-container);
}

/* 编辑页面布局 */
.service-edit-layout {
  padding: var(--service-spacing-xl);
  background-color: var(--service-bg-container);
  min-height: calc(100vh - 60px);
}

.service-edit-layout__form {
  max-width: 800px;
  margin: 0 auto;
  background: var(--service-bg-container);
  padding: 30px;
  border-radius: var(--service-radius-container);
  box-shadow: var(--service-shadow-form);
}

/* ===== 搜索表单组件模板 ===== */

/* 标准搜索表单 */
.service-search-form {
  padding: var(--service-spacing-xl);
  background: var(--service-bg-search);
  border-radius: var(--service-radius-form);
  margin-bottom: var(--service-spacing-xl);
}

.service-search-form .el-form-item {
  margin-bottom: 0;
  margin-right: var(--service-spacing-xl);
}

.service-search-form .el-form-item__label {
  font-size: var(--service-font-size-base);
  font-weight: var(--service-font-weight-medium);
  color: var(--service-text-primary);
}

.service-search-form .el-input,
.service-search-form .el-select {
  width: auto;
  min-width: 120px;
}

/* 搜索按钮组 */
.service-search-actions {
  display: flex;
  gap: var(--service-spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

/* ===== 数据表格组件模板 ===== */

/* 表格容器 */
.service-data-table {
  background: var(--service-bg-container);
  border-radius: var(--service-radius-container);
  overflow: hidden;
  box-shadow: var(--service-shadow-table);
  margin-bottom: var(--service-spacing-xl);
}

/* 表格头部样式 */
.service-data-table .el-table__header-wrapper th {
  background-color: var(--service-bg-table-header) !important;
  color: var(--service-text-regular) !important;
  font-size: var(--service-font-size-md) !important;
  font-weight: var(--service-font-weight-semibold) !important;
  padding: 15px 8px !important;
  border-bottom: 1px solid var(--service-border-lighter) !important;
}

/* 表格行样式 */
.service-data-table .el-table__body-wrapper td {
  font-size: var(--service-font-size-base) !important;
  padding: var(--service-spacing-md) 8px !important;
  color: var(--service-text-primary) !important;
  border-bottom: 1px solid var(--service-border-lighter) !important;
}

.service-data-table .el-table__row:hover > td {
  background-color: var(--service-bg-hover) !important;
}

/* 表格边框 */
.service-data-table .el-table {
  border-left: 1px solid var(--service-border-lighter);
  border-right: 1px solid var(--service-border-lighter);
}

/* ===== 操作按钮组件模板 ===== */

/* 表格操作按钮组 */
.service-table-operations {
  display: flex;
  gap: var(--service-spacing-xs);
  align-items: center;
  flex-wrap: wrap;
}

.service-table-operations .service-btn {
  margin: 0;
}

/* 页面顶部操作按钮组 */
.service-page-operations {
  display: flex;
  gap: var(--service-spacing-sm);
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: var(--service-spacing-xl);
}

/* 对话框底部按钮组 */
.service-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--service-spacing-sm);
}

/* ===== 状态显示组件模板 ===== */

/* 状态开关组件 */
.service-status-switch .el-switch {
  --el-switch-on-color: var(--service-success);
  --el-switch-off-color: var(--service-text-placeholder);
}

/* 状态标签组件 */
.service-status-tags {
  display: flex;
  gap: var(--service-spacing-xs);
  align-items: center;
  flex-wrap: wrap;
}

/* 价格显示组件 */
.service-price-display {
  font-size: var(--service-font-size-base);
  font-weight: var(--service-font-weight-medium);
  color: var(--service-danger);
}

.service-price-display--large {
  font-size: var(--service-font-size-lg);
}

/* 时间显示组件 */
.service-time-display {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.service-time-display__date {
  font-size: var(--service-font-size-base);
  color: var(--service-text-primary);
  line-height: 1.4;
  margin: 0;
}

.service-time-display__time {
  font-size: var(--service-font-size-sm);
  color: var(--service-text-secondary);
  line-height: 1.4;
  margin: 0;
}

/* ===== 表单组件模板 ===== */

/* 标准表单布局 */
.service-form-standard {
  background: var(--service-bg-container);
  padding: 30px;
  border-radius: var(--service-radius-container);
  box-shadow: var(--service-shadow-form);
}

.service-form-standard .el-form-item {
  margin-bottom: 22px;
}

.service-form-standard .el-form-item__label {
  font-weight: var(--service-font-weight-semibold);
  color: var(--service-text-regular);
  line-height: 40px;
}

/* 水平表单布局 */
.service-form-horizontal .el-form-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--service-spacing-lg);
}

.service-form-horizontal .el-form-item__label {
  flex: 0 0 120px;
  margin-bottom: 0;
  margin-right: var(--service-spacing-lg);
  text-align: right;
}

.service-form-horizontal .el-form-item__content {
  flex: 1;
}

/* ===== 上传组件模板 ===== */

/* 图片上传组件 */
.service-image-upload .el-upload--picture-card {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--service-border-base);
  border-radius: var(--service-radius-form);
  cursor: pointer;
  transition: var(--service-transition-base);
}

.service-image-upload .el-upload--picture-card:hover {
  border-color: var(--service-primary);
}

.service-image-upload .el-upload-list--picture-card .el-upload-list__item {
  width: 120px;
  height: 120px;
  border-radius: var(--service-radius-form);
}

.service-image-upload .el-upload__tip {
  font-size: var(--service-font-size-sm);
  color: var(--service-text-regular);
  margin-top: var(--service-spacing-sm);
  line-height: 1.4;
}

/* 上传进度显示 */
.service-upload-progress {
  margin-top: var(--service-spacing-sm);
  padding: var(--service-spacing-sm);
  background-color: var(--service-bg-search);
  border-radius: var(--service-radius-input);
  border: 1px solid var(--service-border-light);
}

.service-upload-progress p {
  margin: var(--service-spacing-xs) 0 0 0;
  font-size: var(--service-font-size-sm);
  color: var(--service-text-regular);
  text-align: center;
}

/* ===== 分页组件模板 ===== */

.service-pagination {
  margin-top: var(--service-spacing-xl);
  text-align: right;
}

.service-pagination .el-pagination {
  --el-pagination-button-color: var(--service-text-regular);
  --el-pagination-hover-color: var(--service-primary);
  --el-pagination-bg-color: var(--service-bg-container);
  --el-pagination-button-bg-color: var(--service-bg-container);
}

/* ===== 树形结构组件模板 ===== */

/* 树形表格行样式 */
.service-tree-table .service-tree-parent {
  font-weight: var(--service-font-weight-medium);
}

.service-tree-table .service-tree-child {
  background-color: rgba(248, 249, 250, 0.5);
}

/* 树形节点 */
.service-tree-node {
  display: flex;
  align-items: center;
}

.service-tree-arrow {
  margin-right: var(--service-spacing-xs);
  transition: transform 0.2s ease-in-out;
  cursor: pointer;
  color: var(--service-text-secondary);
}

.service-tree-arrow--expanded {
  transform: rotate(90deg);
}

.service-tree-indent {
  display: inline-block;
  width: 19px;
  height: 1px;
}

/* ===== 响应式适配 ===== */

@media (max-width: 768px) {
  .service-search-form {
    padding: var(--service-spacing-md);
  }
  
  .service-search-form .el-form-item {
    margin-right: 0;
    margin-bottom: var(--service-spacing-sm);
  }
  
  .service-search-actions {
    width: 100%;
    justify-content: stretch;
  }
  
  .service-search-actions .service-btn {
    flex: 1;
  }
  
  .service-table-operations {
    flex-direction: column;
    align-items: stretch;
  }
  
  .service-table-operations .service-btn {
    width: 100%;
    justify-content: center;
  }
  
  .service-edit-layout__form {
    padding: var(--service-spacing-xl);
  }
}

@media (max-width: 480px) {
  .service-page-layout__content {
    padding: var(--service-spacing-sm);
  }
  
  .service-search-form {
    padding: var(--service-spacing-sm);
  }
  
  .service-edit-layout {
    padding: var(--service-spacing-sm);
  }
  
  .service-edit-layout__form {
    padding: var(--service-spacing-md);
  }
  
  .service-image-upload .el-upload--picture-card {
    width: 100px;
    height: 100px;
  }
}
