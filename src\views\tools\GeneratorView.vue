<!--
  代码生成工具
  自动生成CRUD代码和模板
-->

<template>
  <div class="generator-view">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>代码生成器</h1>
      <p class="page-subtitle">快速生成CRUD代码和模板文件</p>
    </div>

    <!-- 生成配置 -->
    <div class="generator-config">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>生成配置</span>
            <el-button @click="resetConfig">重置配置</el-button>
          </div>
        </template>

        <el-form
          ref="configFormRef"
          :model="generatorConfig"
          :rules="configRules"
          label-width="120px"
          size="default"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模块名称" prop="moduleName">
                <el-input
                  v-model="generatorConfig.moduleName"
                  placeholder="请输入模块名称，如：user"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模块描述" prop="moduleDescription">
                <el-input
                  v-model="generatorConfig.moduleDescription"
                  placeholder="请输入模块描述，如：用户管理"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="表名" prop="tableName">
                <el-input
                  v-model="generatorConfig.tableName"
                  placeholder="请输入数据库表名，如：sys_user"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类名前缀" prop="classPrefix">
                <el-input
                  v-model="generatorConfig.classPrefix"
                  placeholder="请输入类名前缀，如：User"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="生成类型" prop="generateTypes">
            <el-checkbox-group v-model="generatorConfig.generateTypes">
              <el-checkbox label="controller">Controller 控制器</el-checkbox>
              <el-checkbox label="service">Service 服务层</el-checkbox>
              <el-checkbox label="mapper">Mapper 数据层</el-checkbox>
              <el-checkbox label="entity">Entity 实体类</el-checkbox>
              <el-checkbox label="vue">Vue 前端页面</el-checkbox>
              <el-checkbox label="api">API 接口文件</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="包路径" prop="packagePath">
            <el-input
              v-model="generatorConfig.packagePath"
              placeholder="请输入包路径，如：com.example.system"
            />
          </el-form-item>

          <el-form-item label="作者信息" prop="author">
            <el-input
              v-model="generatorConfig.author"
              placeholder="请输入作者信息"
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 字段配置 -->
    <div class="fields-config">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>字段配置</span>
            <el-button type="primary" size="small" @click="addField">
              <el-icon><Plus /></el-icon>
              添加字段
            </el-button>
          </div>
        </template>

        <el-table
          :data="generatorConfig.fields"
          border
          style="width: 100%"
        >
          <el-table-column prop="fieldName" label="字段名" width="150">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.fieldName"
                placeholder="字段名"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column prop="fieldType" label="字段类型" width="120">
            <template #default="{ row, $index }">
              <el-select
                v-model="row.fieldType"
                placeholder="类型"
                size="small"
              >
                <el-option label="String" value="String" />
                <el-option label="Integer" value="Integer" />
                <el-option label="Long" value="Long" />
                <el-option label="Date" value="Date" />
                <el-option label="Boolean" value="Boolean" />
                <el-option label="BigDecimal" value="BigDecimal" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="fieldComment" label="字段注释" width="150">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.fieldComment"
                placeholder="注释"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column prop="isRequired" label="必填" width="80" align="center">
            <template #default="{ row, $index }">
              <el-checkbox v-model="row.isRequired" />
            </template>
          </el-table-column>
          <el-table-column prop="isQuery" label="查询" width="80" align="center">
            <template #default="{ row, $index }">
              <el-checkbox v-model="row.isQuery" />
            </template>
          </el-table-column>
          <el-table-column prop="isList" label="列表" width="80" align="center">
            <template #default="{ row, $index }">
              <el-checkbox v-model="row.isList" />
            </template>
          </el-table-column>
          <el-table-column prop="isEdit" label="编辑" width="80" align="center">
            <template #default="{ row, $index }">
              <el-checkbox v-model="row.isEdit" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template #default="{ row, $index }">
              <el-button
                type="danger"
                size="small"
                @click="removeField($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 生成操作 -->
    <div class="generator-actions">
      <el-card>
        <div class="actions-content">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              :loading="generating"
              @click="generateCode"
            >
              <el-icon><DocumentAdd /></el-icon>
              生成代码
            </el-button>
            <el-button
              type="success"
              size="large"
              @click="previewCode"
            >
              <el-icon><View /></el-icon>
              预览代码
            </el-button>
            <el-button
              type="info"
              size="large"
              @click="downloadCode"
            >
              <el-icon><Download /></el-icon>
              下载代码
            </el-button>
          </div>

          <div class="generation-info">
            <el-alert
              title="代码生成说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <p>1. 请先配置模块基本信息和字段信息</p>
                <p>2. 选择需要生成的代码类型</p>
                <p>3. 点击生成代码按钮开始生成</p>
                <p>4. 生成的代码将包含完整的CRUD功能</p>
              </template>
            </el-alert>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 代码预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="代码预览"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="code-preview">
        <el-tabs v-model="activePreviewTab">
          <el-tab-pane
            v-for="file in previewFiles"
            :key="file.name"
            :label="file.name"
            :name="file.name"
          >
            <div class="code-content">
              <div class="code-header">
                <span class="file-path">{{ file.path }}</span>
                <el-button size="small" @click="copyCode(file.content)">
                  <el-icon><DocumentCopy /></el-icon>
                  复制代码
                </el-button>
              </div>
              <pre class="code-block"><code>{{ file.content }}</code></pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  DocumentAdd,
  View,
  Download,
  DocumentCopy
} from '@element-plus/icons-vue'

// 响应式数据
const configFormRef = ref()
const generating = ref(false)
const previewVisible = ref(false)
const activePreviewTab = ref('')

// 生成器配置
const generatorConfig = reactive({
  moduleName: '',
  moduleDescription: '',
  tableName: '',
  classPrefix: '',
  generateTypes: ['controller', 'service', 'entity', 'vue'],
  packagePath: 'com.example.system',
  author: 'Admin System',
  fields: [
    {
      fieldName: 'id',
      fieldType: 'Long',
      fieldComment: '主键ID',
      isRequired: true,
      isQuery: false,
      isList: true,
      isEdit: false
    },
    {
      fieldName: 'name',
      fieldType: 'String',
      fieldComment: '名称',
      isRequired: true,
      isQuery: true,
      isList: true,
      isEdit: true
    }
  ]
})

// 预览文件
const previewFiles = ref([])

// 表单验证规则
const configRules = {
  moduleName: [
    { required: true, message: '请输入模块名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '模块名称只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ],
  moduleDescription: [
    { required: true, message: '请输入模块描述', trigger: 'blur' }
  ],
  tableName: [
    { required: true, message: '请输入表名', trigger: 'blur' }
  ],
  classPrefix: [
    { required: true, message: '请输入类名前缀', trigger: 'blur' },
    { pattern: /^[A-Z][a-zA-Z0-9]*$/, message: '类名前缀必须以大写字母开头', trigger: 'blur' }
  ],
  generateTypes: [
    { required: true, message: '请选择生成类型', trigger: 'change' }
  ],
  packagePath: [
    { required: true, message: '请输入包路径', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者信息', trigger: 'blur' }
  ]
}

// 方法
const addField = () => {
  generatorConfig.fields.push({
    fieldName: '',
    fieldType: 'String',
    fieldComment: '',
    isRequired: false,
    isQuery: false,
    isList: true,
    isEdit: true
  })
}

const removeField = (index) => {
  if (generatorConfig.fields.length <= 1) {
    ElMessage.warning('至少需要保留一个字段')
    return
  }
  generatorConfig.fields.splice(index, 1)
}

const resetConfig = () => {
  ElMessageBox.confirm(
    '确定要重置所有配置吗？',
    '重置确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    Object.assign(generatorConfig, {
      moduleName: '',
      moduleDescription: '',
      tableName: '',
      classPrefix: '',
      generateTypes: ['controller', 'service', 'entity', 'vue'],
      packagePath: 'com.example.system',
      author: 'Admin System',
      fields: [
        {
          fieldName: 'id',
          fieldType: 'Long',
          fieldComment: '主键ID',
          isRequired: true,
          isQuery: false,
          isList: true,
          isEdit: false
        }
      ]
    })
    ElMessage.success('配置已重置')
  }).catch(() => {
    // 取消重置
  })
}

const generateCode = async () => {
  if (!configFormRef.value) return

  try {
    await configFormRef.value.validate()

    generating.value = true

    // 模拟代码生成
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('代码生成成功！')

    // 生成预览文件
    generatePreviewFiles()

  } catch (error) {
    if (error !== false) {
      ElMessage.error('代码生成失败')
    }
  } finally {
    generating.value = false
  }
}

const previewCode = async () => {
  if (!configFormRef.value) return

  try {
    await configFormRef.value.validate()

    // 生成预览文件
    generatePreviewFiles()

    previewVisible.value = true
    activePreviewTab.value = previewFiles.value[0]?.name || ''

  } catch (error) {
    if (error !== false) {
      ElMessage.error('请先完善配置信息')
    }
  }
}

const downloadCode = () => {
  ElMessage.info('下载功能开发中...')
}

const copyCode = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('代码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const generatePreviewFiles = () => {
  const { moduleName, classPrefix, packagePath } = generatorConfig

  previewFiles.value = [
    {
      name: `${classPrefix}Controller.java`,
      path: `src/main/java/${packagePath.replace(/\./g, '/')}/controller/${classPrefix}Controller.java`,
      content: generateControllerCode()
    },
    {
      name: `${classPrefix}Service.java`,
      path: `src/main/java/${packagePath.replace(/\./g, '/')}/service/${classPrefix}Service.java`,
      content: generateServiceCode()
    },
    {
      name: `${classPrefix}.java`,
      path: `src/main/java/${packagePath.replace(/\./g, '/')}/entity/${classPrefix}.java`,
      content: generateEntityCode()
    },
    {
      name: `${moduleName}.vue`,
      path: `src/views/${moduleName}/${moduleName}.vue`,
      content: generateVueCode()
    }
  ]
}

const generateControllerCode = () => {
  const { classPrefix, packagePath, author, moduleDescription } = generatorConfig

  return `package ${packagePath}.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import ${packagePath}.entity.${classPrefix};
import ${packagePath}.service.${classPrefix}Service;

/**
 * ${moduleDescription}控制器
 *
 * <AUTHOR>
 * @date ${new Date().toLocaleDateString()}
 */
@RestController
@RequestMapping("/${generatorConfig.moduleName}")
public class ${classPrefix}Controller {

    @Autowired
    private ${classPrefix}Service ${generatorConfig.moduleName}Service;

    /**
     * 查询${moduleDescription}列表
     */
    @GetMapping("/list")
    public Result list(${classPrefix} ${generatorConfig.moduleName}) {
        return Result.success(${generatorConfig.moduleName}Service.selectList(${generatorConfig.moduleName}));
    }

    /**
     * 获取${moduleDescription}详细信息
     */
    @GetMapping("/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(${generatorConfig.moduleName}Service.selectById(id));
    }

    /**
     * 新增${moduleDescription}
     */
    @PostMapping
    public Result add(@RequestBody ${classPrefix} ${generatorConfig.moduleName}) {
        return Result.success(${generatorConfig.moduleName}Service.insert(${generatorConfig.moduleName}));
    }

    /**
     * 修改${moduleDescription}
     */
    @PutMapping
    public Result edit(@RequestBody ${classPrefix} ${generatorConfig.moduleName}) {
        return Result.success(${generatorConfig.moduleName}Service.update(${generatorConfig.moduleName}));
    }

    /**
     * 删除${moduleDescription}
     */
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return Result.success(${generatorConfig.moduleName}Service.deleteByIds(ids));
    }
}`
}

const generateServiceCode = () => {
  const { classPrefix, packagePath, author, moduleDescription } = generatorConfig

  return `package ${packagePath}.service;

import java.util.List;
import ${packagePath}.entity.${classPrefix};

/**
 * ${moduleDescription}服务接口
 *
 * <AUTHOR>
 * @date ${new Date().toLocaleDateString()}
 */
public interface ${classPrefix}Service {

    /**
     * 查询${moduleDescription}列表
     */
    List<${classPrefix}> selectList(${classPrefix} ${generatorConfig.moduleName});

    /**
     * 根据ID查询${moduleDescription}
     */
    ${classPrefix} selectById(Long id);

    /**
     * 新增${moduleDescription}
     */
    int insert(${classPrefix} ${generatorConfig.moduleName});

    /**
     * 修改${moduleDescription}
     */
    int update(${classPrefix} ${generatorConfig.moduleName});

    /**
     * 批量删除${moduleDescription}
     */
    int deleteByIds(Long[] ids);
}`
}

const generateEntityCode = () => {
  const { classPrefix, packagePath, author, moduleDescription, tableName, fields } = generatorConfig

  let fieldsCode = ''
  fields.forEach(field => {
    fieldsCode += `
    /**
     * ${field.fieldComment}
     */
    private ${field.fieldType} ${field.fieldName};
`
  })

  return `package ${packagePath}.entity;

import java.util.Date;

/**
 * ${moduleDescription}实体类
 *
 * <AUTHOR>
 * @date ${new Date().toLocaleDateString()}
 */
public class ${classPrefix} {
${fieldsCode}
    // getter and setter methods...
}`
}

const generateVueCode = () => {
  return '// Vue组件代码生成功能暂时简化\n// 完整的代码生成功能需要更复杂的模板处理\nconst vueTemplate = "Vue组件模板代码"\nreturn vueTemplate'
}
</script>

<style scoped>
.generator-view {
  padding: var(--spacing-lg);
}

.page-title {
  margin-bottom: var(--spacing-xl);
}

.page-title h1 {
  font-size: var(--font-size-xxl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.generator-config,
.fields-config,
.generator-actions {
  margin-bottom: var(--spacing-xl);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actions-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-xl);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-md);
}

.generation-info {
  flex: 1;
  max-width: 400px;
}

.code-preview {
  height: 600px;
}

.code-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--bg-color-column);
  border-bottom: 1px solid var(--border-color-light);
}

.file-path {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-family: 'Courier New', monospace;
}

.code-block {
  flex: 1;
  margin: 0;
  padding: var(--spacing-lg);
  background-color: #f8f9fa;
  border: none;
  font-family: 'Courier New', monospace;
  font-size: var(--font-size-sm);
  line-height: 1.6;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Element Plus 样式覆盖 */
:deep(.el-card__body) {
  padding: var(--spacing-lg);
}

:deep(.el-table .el-table__cell) {
  padding: var(--spacing-sm) var(--spacing-md);
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

:deep(.el-alert__content) {
  padding-left: 0;
}

:deep(.el-alert__content p) {
  margin: var(--spacing-xs) 0;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .generator-view {
    padding: var(--spacing-md);
  }

  .actions-content {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .action-buttons {
    flex-direction: column;
  }

  .code-preview {
    height: 400px;
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: var(--spacing-md);
  }
}
</style>
