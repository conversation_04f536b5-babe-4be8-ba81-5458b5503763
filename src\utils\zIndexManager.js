/**
 * Z-Index 管理工具
 * 用于处理页面缩放时弹窗组件的层级问题
 */

// z-index 基础值配置
const Z_INDEX_CONFIG = {
  // 正常状态下的z-index
  normal: {
    overlay: 2000,
    dialog: 2001,
    message: 3000,
    messageBox: 3001,
    notification: 4000,
    drawer: 2500,
    popover: 2100,
    tooltip: 2200,
    dropdown: 2300,
    select: 2400,
    cascader: 2450,
    datePicker: 2500,
    timePicker: 2500,
    colorPicker: 2500
  },
  // 缩放状态下的z-index（更高的值）
  zoomed: {
    overlay: 10001,
    dialog: 10002,
    message: 10003,
    messageBox: 10004,
    notification: 10005,
    drawer: 10006,
    popover: 10007,
    tooltip: 10008,
    dropdown: 10009,
    select: 10010,
    cascader: 10011,
    datePicker: 10012,
    timePicker: 10013,
    colorPicker: 10014
  }
}

// Element Plus 组件选择器映射
const ELEMENT_SELECTORS = {
  overlay: '.el-overlay',
  dialog: '.el-dialog',
  message: '.el-message',
  messageBox: '.el-message-box',
  notification: '.el-notification',
  drawer: '.el-drawer',
  popover: '.el-popover',
  tooltip: '.el-tooltip',
  dropdown: '.el-dropdown-menu',
  select: '.el-select-dropdown',
  cascader: '.el-cascader-panel',
  datePicker: '.el-date-picker',
  timePicker: '.el-time-picker',
  colorPicker: '.el-color-picker__panel'
}

/**
 * 应用z-index到指定元素
 * @param {string} selector - CSS选择器
 * @param {number} zIndex - z-index值
 */
function applyZIndex(selector, zIndex) {
  const elements = document.querySelectorAll(selector)
  elements.forEach(element => {
    element.style.zIndex = zIndex
  })
}

/**
 * 设置正常状态的z-index
 */
function setNormalZIndex() {
  Object.keys(ELEMENT_SELECTORS).forEach(key => {
    const selector = ELEMENT_SELECTORS[key]
    const zIndex = Z_INDEX_CONFIG.normal[key]
    applyZIndex(selector, zIndex)
  })
  console.log('🔧 已设置正常状态的z-index')
}

/**
 * 设置缩放状态的z-index
 */
function setZoomedZIndex() {
  Object.keys(ELEMENT_SELECTORS).forEach(key => {
    const selector = ELEMENT_SELECTORS[key]
    const zIndex = Z_INDEX_CONFIG.zoomed[key]
    applyZIndex(selector, zIndex)
  })
  console.log('🔧 已设置缩放状态的z-index')
}

/**
 * 监听DOM变化，自动应用z-index
 * @param {boolean} isZoomed - 是否处于缩放状态
 */
function observeElementChanges(isZoomed = false) {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // 检查新添加的元素是否是需要处理的弹窗组件
          Object.keys(ELEMENT_SELECTORS).forEach(key => {
            const selector = ELEMENT_SELECTORS[key]
            const className = selector.replace('.', '')
            
            if (node.classList && node.classList.contains(className)) {
              const zIndex = isZoomed ? Z_INDEX_CONFIG.zoomed[key] : Z_INDEX_CONFIG.normal[key]
              node.style.zIndex = zIndex
              console.log(`🔧 自动设置 ${selector} 的z-index为 ${zIndex}`)
            }
            
            // 检查子元素
            const childElements = node.querySelectorAll && node.querySelectorAll(selector)
            if (childElements && childElements.length > 0) {
              const zIndex = isZoomed ? Z_INDEX_CONFIG.zoomed[key] : Z_INDEX_CONFIG.normal[key]
              childElements.forEach(child => {
                child.style.zIndex = zIndex
              })
              console.log(`🔧 自动设置 ${childElements.length} 个 ${selector} 子元素的z-index为 ${zIndex}`)
            }
          })
        }
      })
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true
  })

  return observer
}

/**
 * 检查当前是否处于缩放状态
 * @returns {boolean}
 */
function isLayoutZoomed() {
  const layoutContainer = document.querySelector('.layout-container')
  if (!layoutContainer) return false
  
  return layoutContainer.classList.contains('zoom-small') ||
         layoutContainer.classList.contains('zoom-large') ||
         layoutContainer.classList.contains('zoom-extra-large')
}

/**
 * 获取当前缩放级别
 * @returns {string}
 */
function getCurrentZoomLevel() {
  const layoutContainer = document.querySelector('.layout-container')
  if (!layoutContainer) return 'normal'
  
  if (layoutContainer.classList.contains('zoom-small')) return 'small'
  if (layoutContainer.classList.contains('zoom-large')) return 'large'
  if (layoutContainer.classList.contains('zoom-extra-large')) return 'extra-large'
  
  return 'normal'
}

/**
 * 初始化z-index管理器
 */
function initZIndexManager() {
  let observer = null
  let currentZoomState = isLayoutZoomed()
  
  // 初始设置
  if (currentZoomState) {
    setZoomedZIndex()
  } else {
    setNormalZIndex()
  }
  
  // 开始监听DOM变化
  observer = observeElementChanges(currentZoomState)
  
  // 监听缩放状态变化
  const checkZoomState = () => {
    const newZoomState = isLayoutZoomed()
    if (newZoomState !== currentZoomState) {
      currentZoomState = newZoomState
      
      // 停止旧的观察器
      if (observer) {
        observer.disconnect()
      }
      
      // 应用新的z-index
      if (currentZoomState) {
        setZoomedZIndex()
      } else {
        setNormalZIndex()
      }
      
      // 重新开始监听
      observer = observeElementChanges(currentZoomState)
      
      console.log(`🔄 缩放状态变化: ${currentZoomState ? '缩放模式' : '正常模式'}`)
    }
  }
  
  // 定期检查缩放状态
  const intervalId = setInterval(checkZoomState, 1000)
  
  // 返回清理函数
  return () => {
    if (observer) {
      observer.disconnect()
    }
    clearInterval(intervalId)
    console.log('🧹 z-index管理器已清理')
  }
}

/**
 * 手动更新z-index（用于缩放状态变化时立即调用）
 * @param {string} zoomLevel - 缩放级别 ('normal', 'small', 'large', 'extra-large')
 */
function updateZIndexForZoom(zoomLevel) {
  const isZoomed = zoomLevel !== 'normal'
  
  if (isZoomed) {
    setZoomedZIndex()
  } else {
    setNormalZIndex()
  }
  
  console.log(`🔧 手动更新z-index for zoom level: ${zoomLevel}`)
}

// 导出工具函数
export {
  initZIndexManager,
  updateZIndexForZoom,
  setNormalZIndex,
  setZoomedZIndex,
  isLayoutZoomed,
  getCurrentZoomLevel,
  Z_INDEX_CONFIG
}

// 默认导出初始化函数
export default initZIndexManager
