<!--
  上传配置页面
-->

<template>
  <div class="lb-system-upload">
    <TopNav />
    <div class="page-main">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>上传配置</span>
          </div>
        </template>
        
        <el-form 
          :model="configForm" 
          ref="configFormRef" 
          label-width="140px"
          class="config-form"
        >
          <el-form-item label="上传方式">
            <el-radio-group v-model="configForm.upload_type">
              <el-radio value="local">本地存储</el-radio>
              <el-radio value="oss">阿里云OSS</el-radio>
              <el-radio value="qiniu">七牛云</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="最大文件大小">
            <el-input-number v-model="configForm.max_size" :min="1" :max="100" />
            <span style="margin-left: 10px;">MB</span>
          </el-form-item>
          
          <el-form-item label="允许的文件类型">
            <el-input v-model="configForm.allowed_types" placeholder="如：jpg,png,gif,pdf" />
          </el-form-item>
          
          <el-form-item>
            <LbButton type="primary" @click="saveConfig" :loading="saveLoading">保存配置</LbButton>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const saveLoading = ref(false)
const configFormRef = ref()

const configForm = reactive({
  upload_type: 'local',
  max_size: 10,
  allowed_types: 'jpg,png,gif,pdf'
})

const getConfig = async () => {
  try {
    const response = await fetch('/api/system/upload/config')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(configForm, result.data || {})
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
}

const saveConfig = async () => {
  try {
    saveLoading.value = true
    
    const response = await fetch('/api/system/upload/config', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(configForm)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(result.meg || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.lb-system-upload {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 600px;
}
</style>
