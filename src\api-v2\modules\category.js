/**
 * 分类管理模块 - V2版本
 * 按照API封装规范文档实现分类管理相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取分类列表
   * @param {Object} querys 查询参数
   * @param {number} querys.parentId 父级分类ID，0为顶级分类
   * @param {number} querys.status 状态，非必填，-1不可用，1可用
   * @param {string} querys.name 分类名称，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回分类列表数据
   */
  categoryList(querys) {
    console.log('📂 分类列表API-V2请求参数:', querys)
    return get('/api/admin/category/list', querys)
  },

  /**
   * 获取分类树形结构
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，非必填
   * @returns {Promise} 返回分类树形数据
   */
  categoryTree(querys) {
    console.log('🌳 分类树形结构API-V2请求参数:', querys)
    return get('/api/admin/category/tree', querys)
  },

  /**
   * 获取分类详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 分类ID
   * @returns {Promise} 返回分类详情数据
   */
  categoryInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }
    console.log('🔍 获取分类详情API-V2请求:', querys)
    return get(`/api/admin/category/info/${querys.id}`)
  },

  /**
   * 新增分类
   * @param {Object} querys 分类数据
   * @param {string} querys.name 分类名称
   * @param {string} querys.icon 分类图标URL
   * @param {number} querys.parentId 父级分类ID，0为顶级分类
   * @param {number} querys.sort 排序
   * @param {string} querys.description 分类描述
   * @param {number} querys.status 状态，1可用，-1不可用
   * @returns {Promise} 返回新增结果
   */
  categoryAdd(querys) {
    if (!querys || !querys.name) {
      return Promise.reject(new Error('分类名称不能为空'))
    }

    const apiData = {
      name: querys.name,
      icon: querys.icon || '',
      parentId: querys.parentId || 0,
      sort: querys.sort || 0,
      description: querys.description || '',
      status: querys.status || 1
    }

    console.log('➕ 新增分类API-V2请求数据:', apiData)
    return post('/api/admin/category/add', apiData)
  },

  /**
   * 编辑分类
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  categoryUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('✏️ 编辑分类API-V2请求:', querys)
    return post('/api/admin/category/update', querys)
  },

  /**
   * 删除分类
   * @param {Object} querys 删除参数
   * @param {number} querys.id 分类ID
   * @returns {Promise} 返回删除结果
   */
  categoryDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('🗑️ 删除分类API-V2请求:', querys)
    return post(`/api/admin/category/delete/${querys.id}`)
  },

  /**
   * 更新分类状态
   * @param {Object} querys 状态更新参数
   * @param {number} querys.id 分类ID
   * @param {number} querys.status 状态，1可用，-1不可用
   * @returns {Promise} 返回更新结果
   */
  categoryStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    if (![1, -1].includes(querys.status)) {
      return Promise.reject(new Error('状态值无效'))
    }

    console.log('🔄 更新分类状态API-V2请求:', querys)
    return post(`/api/admin/category/status/${querys.id}`, { status: querys.status })
  },

  /**
   * 移动分类
   * @param {Object} querys 移动参数
   * @param {number} querys.id 分类ID
   * @param {number} querys.targetParentId 目标父级分类ID
   * @param {number} querys.sort 新的排序值
   * @returns {Promise} 返回移动结果
   */
  categoryMove(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('🔄 移动分类API-V2请求:', querys)
    return post('/api/admin/category/move', querys)
  },

  /**
   * 批量更新分类排序
   * @param {Object} querys 排序参数
   * @param {Array} querys.sortData 排序数据，格式：[{id: 1, sort: 1}, {id: 2, sort: 2}]
   * @returns {Promise} 返回更新结果
   */
  categorySortBatch(querys) {
    if (!querys || !querys.sortData || !Array.isArray(querys.sortData)) {
      return Promise.reject(new Error('排序数据不能为空'))
    }

    console.log('🔢 批量更新分类排序API-V2请求:', querys)
    return post('/api/admin/category/sort/batch', querys)
  },

  /**
   * 获取分类下的服务数量
   * @param {Object} querys 查询参数
   * @param {number} querys.categoryId 分类ID
   * @returns {Promise} 返回服务数量
   */
  categoryServiceCount(querys) {
    if (!querys || !querys.categoryId) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('📊 获取分类服务数量API-V2请求:', querys)
    return get(`/api/admin/category/service/count/${querys.categoryId}`)
  },

  /**
   * 复制分类
   * @param {Object} querys 复制参数
   * @param {number} querys.id 源分类ID
   * @param {string} querys.newName 新分类名称
   * @param {number} querys.targetParentId 目标父级分类ID
   * @returns {Promise} 返回复制结果
   */
  categoryCopy(querys) {
    if (!querys || !querys.id || !querys.newName) {
      return Promise.reject(new Error('源分类ID和新分类名称不能为空'))
    }

    console.log('📋 复制分类API-V2请求:', querys)
    return post('/api/admin/category/copy', querys)
  },

  /**
   * 获取分类路径
   * @param {Object} querys 查询参数
   * @param {number} querys.categoryId 分类ID
   * @returns {Promise} 返回分类路径数据
   */
  categoryPath(querys) {
    if (!querys || !querys.categoryId) {
      return Promise.reject(new Error('分类ID不能为空'))
    }

    console.log('🛤️ 获取分类路径API-V2请求:', querys)
    return get(`/api/admin/category/path/${querys.categoryId}`)
  },

  /**
   * 检查分类名称是否重复
   * @param {Object} querys 检查参数
   * @param {string} querys.name 分类名称
   * @param {number} querys.parentId 父级分类ID
   * @param {number} querys.excludeId 排除的分类ID（编辑时使用）
   * @returns {Promise} 返回检查结果
   */
  categoryNameCheck(querys) {
    if (!querys || !querys.name) {
      return Promise.reject(new Error('分类名称不能为空'))
    }

    console.log('✅ 检查分类名称API-V2请求:', querys)
    return post('/api/admin/category/name/check', querys)
  }
}
