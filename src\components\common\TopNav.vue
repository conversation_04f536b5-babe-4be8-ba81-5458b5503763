<!--
  TopNav 面包屑导航组件

  自动生成面包屑导航
-->

<template>
  <div class="top-nav">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbList"
        :key="index"
        :to="item.path && index < breadcrumbList.length - 1 ? { path: item.path } : null"
      >
        <el-icon v-if="item.icon && index === 0">
          <component :is="item.icon" />
        </el-icon>
        {{ item.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { mainMenuList } from '@/config/menuConfig.js'

// 定义 props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  isBack: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()

// 计算面包屑列表
const breadcrumbList = computed(() => {
  const breadcrumbs = []
  
  // 添加首页
  breadcrumbs.push({
    title: '首页',
    path: '/',
    icon: 'House'
  })
  
  // 根据当前路由生成面包屑
  const currentPath = route.path
  
  // 查找对应的主菜单
  const mainMenu = mainMenuList.find(menu => currentPath.startsWith(menu.path))
  if (mainMenu) {
    breadcrumbs.push({
      title: mainMenu.name,
      path: mainMenu.path,
      icon: mainMenu.icon
    })
  }
  
  // 添加当前页面
  let currentPageTitle = ''

  // 优先使用传入的 title prop
  if (props.title) {
    currentPageTitle = props.title
  } else if (route.meta && route.meta.title) {
    currentPageTitle = route.meta.title
  } else {
    // 根据路径推断页面名称
    const pathSegments = currentPath.split('/').filter(Boolean)
    if (pathSegments.length > 1) {
      currentPageTitle = getPageNameByPath(currentPath)
    }
  }

  if (currentPageTitle) {
    breadcrumbs.push({
      title: currentPageTitle,
      path: currentPath
    })
  }
  
  return breadcrumbs
})

// 根据路径获取页面名称
const getPageNameByPath = (path) => {
  const pageNameMap = {
    // 服务项目模块
    '/service/list': '服务项目',
    '/service/banner': '轮播图设置',
    '/service/jingang': '金刚区设置',
    '/service/fenlei': '分类设置',
    '/service/daili': '服务点设置',
    '/service/peizhi': '项目配置',
    '/service/edit': '编辑服务项目',
    '/service/add': '新增服务项目',
    
    // 师傅管理模块
    '/technician/list': '师傅管理',
    '/technician/level': '师傅等级',
    '/technician/deposit': '师傅押金',
    '/technician/distance': '接单范围',
    
    // 营销管理模块
    '/market/list': '卡券管理',
    '/market/edit': '编辑卡券',
    '/market/add': '新增卡券',
    '/market/notice': '公告设置',
    '/market/partner': '合伙人管理',
    '/market/partner/invite': '合伙人邀请列表',
    
    // 订单管理模块
    '/shop/order': '订单管理',
    '/shop/refund': '退款管理',
    '/shop/evaluate/list': '评价管理',
    '/shop/commission/distribution': '分销佣金',
    '/shop/AfterSale': '售后管理',
    
    // 分销管理模块
    '/distribution/examine': '分销商审核',
    '/distribution/set': '分销设置',
    
    // 财务管理模块
    '/finance/list': '财务管理',
    '/finance/record': '提现申请',
    
    // 用户管理模块
    '/custom/list': '用户管理',
    
    // 账号设置模块
    '/account/franchisee': '代理商管理',
    '/account/acountRole': '角色管理',
    '/account/acountAdmin': '管理员',
    '/account/acountThree': '第三方管理',
    
    // 系统设置模块
    '/sys/upgrade': '系统升级',
    '/sys/examine': '上传微信审核',
    '/sys/wechat': '小程序设置',
    '/sys/web': '公众号设置',
    '/sys/app': 'APP设置',
    '/sys/info': '隐私协议',
    '/sys/payment': '支付配置',
    '/sys/upload': '上传配置',
    '/sys/transaction': '交易设置',
    '/sys/notice': '万能通知',
    '/sys/message': '短信通知',
    '/sys/information': '备案信息',
    '/sys/print': '打印机设置',
    '/sys/car_fee': '车费设置',
    '/sys/city': '城市设置',
    '/sys/travel': '出行设置',
    '/sys/other': '其他设置',
    '/sys/version': '版本管理'
  }
  
  return pageNameMap[path] || ''
}
</script>

<style scoped>
.top-nav {
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

/* 面包屑样式调整 */
.top-nav :deep(.el-breadcrumb) {
  font-size: 14px;
  line-height: 1;
}

.top-nav :deep(.el-breadcrumb__item) {
  display: inline-flex;
  align-items: center;
}

.top-nav :deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  display: inline-flex;
  align-items: center;
  color: #606266;
  font-weight: normal;
}

.top-nav :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #303133;
  font-weight: 500;
}

.top-nav :deep(.el-breadcrumb__item .el-breadcrumb__inner:hover) {
  color: #409eff;
  cursor: pointer;
}

.top-nav :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover) {
  color: #303133;
  cursor: default;
}

/* 图标样式 */
.top-nav :deep(.el-icon) {
  margin-right: 4px;
  font-size: 14px;
}

/* 分隔符样式 */
.top-nav :deep(.el-breadcrumb__separator) {
  margin: 0 8px;
  color: #c0c4cc;
}

/*  的样式 */
.top-nav {
  background-color: #fff;
  padding: 12px 0;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-nav {
    padding: 8px 0;
    margin-bottom: 12px;
  }
  
  .top-nav :deep(.el-breadcrumb) {
    font-size: 12px;
  }
  
  .top-nav :deep(.el-breadcrumb__separator) {
    margin: 0 4px;
  }
}
</style>
