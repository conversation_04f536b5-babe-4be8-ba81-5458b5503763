<!--
  管理员管理页面
  根据API接口文档重构，支持4个接口的完整功能
-->

<template>
  <div class="lb-account-admin">
    <TopNav />
    <div class="page-main">
      <!-- 操作按钮 -->
      <el-row class="page-header">
        <LbButton type="primary" @click="handleAdd">新增管理员</LbButton>
      </el-row>
      
      <!-- 数据表格 -->
      <el-card class="table-card" shadow="never">
        <el-table 
          v-loading="loading" 
          :data="tableData" 
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
          border
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" width="150" />
          <el-table-column prop="roleName" label="角色" width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.roleName" type="primary" size="small">
                {{ scope.row.roleName }}
              </el-tag>
              <span v-else class="text-muted">未分配角色</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="menus" label="权限菜单" min-width="300">
            <template #default="scope">
              <div class="menu-permissions">
                <el-tag
                  v-for="menu in getDisplayMenus(scope.row.menus)"
                  :key="menu.id"
                  size="small"
                  style="margin-right: 5px; margin-bottom: 5px;"
                >
                  {{ menu.menuName }}
                </el-tag>
                <span v-if="scope.row.menus && scope.row.menus.length > 5" class="more-menus">
                  等{{ scope.row.menus.length }}个权限
                </span>
                <span v-if="!scope.row.menus || scope.row.menus.length === 0" class="text-muted">
                  暂无权限
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="mini"
                  type="primary"
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </LbButton>
                <LbButton
                  size="mini"
                  :type="scope.row.status === 1 ? 'danger' : 'success'"
                  @click="toggleStatus(scope.row)"
                >
                  {{ scope.row.status === 1 ? '禁用' : '启用' }}
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="type === 'add' ? '新增管理员' : '编辑管理员'" 
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="type === 'add'">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码（不填则使用默认密码admin123）"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="type === 'add' && form.password">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请确认密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="选择角色" prop="roleId">
          <el-select v-model="form.roleId" placeholder="请选择角色" style="width: 100%;">
            <el-option
              v-for="item in roleList"
              :key="item.id"
              :label="item.roleName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <LbButton @click="handleClose">取消</LbButton>
        <LbButton type="primary" @click="handleConfirm" :loading="submitLoading">确定</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const type = ref('add')
const formRef = ref()
const submitLoading = ref(false)
const roleList = ref([])

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  totalCount: 0,
  totalPage: 0
})

// 表单数据
const form = reactive({
  id: '',
  username: '',
  password: '',
  confirmPassword: '',
  roleId: ''
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (_rule, value, callback) => {
        if (form.password && value !== form.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  roleId: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 方法
const getAdminList = async (pageNum = 1) => {
  loading.value = true
  pagination.pageNum = pageNum

  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }

    const response = await api.admin.getAdminList(params)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      pagination.totalCount = response.data.totalCount || 0
      pagination.totalPage = response.data.totalPage || 0
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const getRoleList = async () => {
  try {
    const response = await api.admin.getAllRoles()

    if (response.code === '200') {
      roleList.value = response.data || []
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

// 获取显示的菜单（最多显示5个）
const getDisplayMenus = (menus) => {
  if (!menus || !Array.isArray(menus)) return []
  return menus.slice(0, 5).sort((a, b) => a.sort - b.sort)
}

const getStatusType = (status) => {
  return status === 1 ? 'success' : 'danger'
}

const getStatusText = (status) => {
  return status === 1 ? '启用' : '禁用'
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const handleAdd = () => {
  type.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  type.value = 'edit'

  // 根据角色名称找到角色ID
  const role = roleList.value.find(r => r.roleName === row.roleName)

  Object.assign(form, {
    id: row.id,
    username: row.username,
    password: '',
    confirmPassword: '',
    roleId: role ? role.id : ''
  })
  dialogVisible.value = true
}

const toggleStatus = async (row) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}管理员 "${row.username}" 吗？`,
      `${action}确认`,
      {
        confirmButtonText: `确定${action}`,
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.admin.changeAdminStatus(row.id)

    if (response.code === '200') {
      ElMessage.success(`${action}成功`)
      getAdminList()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改管理员状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const handleConfirm = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    const requestData = {
      username: form.username,
      roleId: form.roleId
    }

    // 如果是新增且有密码，添加密码字段
    if (type.value === 'add' && form.password) {
      requestData.password = form.password
    }

    // 如果是编辑，添加ID
    if (type.value === 'edit') {
      requestData.id = form.id
    }

    const response = type.value === 'add'
      ? await api.admin.addAdmin(requestData)
      : await api.admin.editAdmin(requestData)

    if (response.code === '200') {
      ElMessage.success(type.value === 'add' ? '新增成功' : '编辑成功')
      dialogVisible.value = false
      getAdminList()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    id: '',
    username: '',
    password: '',
    confirmPassword: '',
    roleId: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getAdminList(1)
}

const handleCurrentChange = (page) => {
  getAdminList(page)
}

// 生命周期
onMounted(() => {
  getAdminList()
  getRoleList()
})
</script>

<style scoped>
.lb-account-admin {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  width: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.menu-permissions {
  max-width: 100%;
}

.more-menus {
  color: #999;
  font-size: 12px;
  margin-left: 5px;
}

.text-muted {
  color: #999;
  font-style: italic;
}

@media (max-width: 768px) {
  .lb-account-admin {
    padding: 10px;
  }

  .table-operate {
    flex-direction: column;
  }

  .pagination-section {
    text-align: center;
  }
}
</style>
