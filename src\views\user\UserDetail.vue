<!--
  用户详情页面
  严格按照原版后台的用户详情功能重构
-->

<template>
  <div class="lb-user-detail">
    <TopNav title="用户详情" :isBack="true" />
    <div class="page-main">
      <!-- 用户基本信息 -->
      <el-card class="user-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>用户基本信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="user-avatar">
              <img :src="userInfo.avatarUrl" alt="用户头像" />
            </div>
          </el-col>
          <el-col :span="18">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户ID">{{ userInfo.id }}</el-descriptions-item>
              <el-descriptions-item label="微信昵称">{{ userInfo.nickName }}</el-descriptions-item>
              <el-descriptions-item label="手机号">{{ userInfo.mobile || '未绑定' }}</el-descriptions-item>
              <el-descriptions-item label="性别">{{ getGenderText(userInfo.gender) }}</el-descriptions-item>
              <el-descriptions-item label="注册时间">{{ userInfo.create_time }}</el-descriptions-item>
              <el-descriptions-item label="最后登录">{{ userInfo.last_login_time || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="用户状态">
                <el-tag :type="getStatusType(userInfo.status)" size="small">
                  {{ getStatusText(userInfo.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="用户类型">
                <el-tag :type="getTypeColor(userInfo.type)" size="small">
                  {{ getTypeText(userInfo.type) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="地址" span="2">{{ userInfo.address || '未填写' }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 用户统计信息 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ userStats.order_count || 0 }}</div>
              <div class="stats-label">订单数量</div>
            </div>
            <div class="stats-icon orders">
              <i class="el-icon-shopping-cart-2"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ userStats.total_amount || 0 }}</div>
              <div class="stats-label">消费金额</div>
            </div>
            <div class="stats-icon amount">
              <i class="el-icon-money"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ userStats.balance || 0 }}</div>
              <div class="stats-label">账户余额</div>
            </div>
            <div class="stats-icon balance">
              <i class="el-icon-wallet"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ userStats.coupon_count || 0 }}</div>
              <div class="stats-label">优惠券</div>
            </div>
            <div class="stats-icon coupons">
              <i class="el-icon-ticket"></i>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <el-row class="action-buttons">
        <LbButton 
          type="primary" 
          @click="editUser"
        >
          编辑用户
        </LbButton>
        <LbButton 
          :type="userInfo.status === 1 ? 'danger' : 'success'"
          @click="toggleUserStatus"
        >
          {{ userInfo.status === 1 ? '禁用用户' : '启用用户' }}
        </LbButton>
        <LbButton 
          type="warning"
          @click="setCustomerService"
        >
          设置客服
        </LbButton>
        <LbButton 
          type="info"
          @click="viewOrders"
        >
          查看订单
        </LbButton>
      </el-row>
      
      <!-- 最近订单 -->
      <el-card class="recent-orders" shadow="never">
        <template #header>
          <div class="card-header">
            <span>最近订单</span>
            <LbButton type="text" @click="viewAllOrders">查看全部</LbButton>
          </div>
        </template>
        
        <el-table 
          :data="recentOrders" 
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
        >
          <el-table-column prop="order_code" label="订单号" width="180" />
          <el-table-column prop="service_name" label="服务项目" width="200" />
          <el-table-column prop="total_price" label="订单金额" width="120">
            <template #default="scope">
              <span style="color: #e6a23c; font-weight: 600;">¥{{ scope.row.total_price }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="订单状态" width="120">
            <template #default="scope">
              <el-tag :type="getOrderStatusType(scope.row.status)" size="small">
                {{ getOrderStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="下单时间" width="170">
            <template #default="scope">
              <div>{{ formatDate(scope.row.create_time, 1) }}</div>
              <div>{{ formatDate(scope.row.create_time, 2) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <LbButton
                size="mini"
                type="primary"
                @click="viewOrderDetail(scope.row)"
              >
                查看详情
              </LbButton>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <div class="space-lg mt-lg mb-lg"></div>
      <LbButton type="primary" @click="$router.go(-1)">
        返回
      </LbButton>
    </div>
    
    <!-- 客服设置对话框 -->
    <el-dialog v-model="serviceVisible" title="设置客服" width="40%">
      <el-form :model="serviceForm" :rules="serviceRules" ref="serviceFormRef">
        <el-form-item label="客服类型" prop="service_type">
          <el-radio-group v-model="serviceForm.service_type">
            <el-radio :value="1">在线客服</el-radio>
            <el-radio :value="2">电话客服</el-radio>
            <el-radio :value="3">微信客服</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="客服信息" prop="service_info">
          <el-input 
            v-model="serviceForm.service_info" 
            placeholder="请输入客服信息"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="serviceForm.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <LbButton @click="serviceVisible = false">取消</LbButton>
        <LbButton type="primary" @click="submitService" :loading="serviceLoading">确定设置</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const serviceVisible = ref(false)
const serviceFormRef = ref()
const serviceLoading = ref(false)
const recentOrders = ref([])

// 用户信息
const userInfo = reactive({
  id: '',
  nickName: '',
  avatarUrl: '',
  mobile: '',
  gender: 1,
  create_time: '',
  last_login_time: '',
  status: 1,
  type: 1,
  address: ''
})

// 用户统计
const userStats = reactive({
  order_count: 0,
  total_amount: 0,
  balance: 0,
  coupon_count: 0
})

// 客服设置表单
const serviceForm = reactive({
  service_type: 1,
  service_info: '',
  remark: ''
})

// 客服设置验证规则
const serviceRules = {
  service_type: [
    { required: true, message: '请选择客服类型', trigger: 'change' }
  ],
  service_info: [
    { required: true, message: '请输入客服信息', trigger: 'blur' }
  ]
}

// 方法
const getUserDetail = async () => {
  loading.value = true
  
  try {
    const response = await fetch(`/api/user/detail/${route.params.id}`)
    const result = await response.json()

    if (result.code === 200) {
      Object.assign(userInfo, result.data.info || {})
      Object.assign(userStats, result.data.stats || {})
      recentOrders.value = result.data.recent_orders || []
    } else {
      ElMessage.error(result.meg || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

const getGenderText = (gender) => {
  const genderMap = {
    0: '未知',
    1: '男',
    2: '女'
  }
  return genderMap[gender] || '未知'
}

const getStatusType = (status) => {
  const statusMap = {
    1: 'success',  // 正常
    2: 'danger'    // 禁用
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '正常',
    2: '禁用'
  }
  return statusMap[status] || '未知'
}

const getTypeColor = (type) => {
  const typeMap = {
    1: 'primary',  // 普通用户
    2: 'warning'   // VIP用户
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type) => {
  const typeMap = {
    1: '普通用户',
    2: 'VIP用户'
  }
  return typeMap[type] || '普通用户'
}

const getOrderStatusType = (status) => {
  const statusMap = {
    1: 'warning',  // 待付款
    2: 'info',     // 待服务
    3: 'primary',  // 服务中
    4: 'success',  // 已完成
    5: 'danger'    // 已取消
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const statusMap = {
    1: '待付款',
    2: '待服务',
    3: '服务中',
    4: '已完成',
    5: '已取消'
  }
  return statusMap[status] || '未知'
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const editUser = () => {
  router.push(`/user/edit/${userInfo.id}`)
}

const toggleUserStatus = async () => {
  try {
    const action = userInfo.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}用户 "${userInfo.nickName}" 吗？`,
      `${action}用户确认`,
      {
        confirmButtonText: `确定${action}`,
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/user/status/${userInfo.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        status: userInfo.status === 1 ? 2 : 1 
      })
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success(`${action}成功`)
      getUserDetail()
    } else {
      ElMessage.error(result.meg || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改用户状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const setCustomerService = () => {
  serviceForm.service_type = 1
  serviceForm.service_info = ''
  serviceForm.remark = ''
  serviceVisible.value = true
}

const submitService = async () => {
  try {
    await serviceFormRef.value.validate()
    
    serviceLoading.value = true
    
    const response = await fetch(`/api/user/customer-service/${userInfo.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(serviceForm)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('客服设置成功')
      serviceVisible.value = false
    } else {
      ElMessage.error(result.meg || '设置失败')
    }
  } catch (error) {
    console.error('客服设置失败:', error)
    ElMessage.error('设置失败')
  } finally {
    serviceLoading.value = false
  }
}

const viewOrders = () => {
  router.push(`/shop/order?user_id=${userInfo.id}`)
}

const viewAllOrders = () => {
  router.push(`/shop/order?user_id=${userInfo.id}`)
}

const viewOrderDetail = (order) => {
  router.push(`/shop/order/detail/${order.id}`)
}

// 生命周期
onMounted(() => {
  if (route.params.id) {
    getUserDetail()
  }
})
</script>

<style scoped>
.lb-user-detail {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.user-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-avatar {
  text-align: center;
}

.user-avatar img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 3px solid #f0f0f0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-content {
  padding: 20px;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.stats-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #fff;
}

.stats-icon.orders {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stats-icon.amount {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stats-icon.balance {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
}

.stats-icon.coupons {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.action-buttons {
  margin-bottom: 20px;
}

.action-buttons .el-button {
  margin-right: 10px;
}

.recent-orders {
  margin-bottom: 20px;
}

.mt-lg {
  margin-top: 20px;
}

.mb-lg {
  margin-bottom: 20px;
}

.space-lg {
  height: 20px;
}

@media (max-width: 768px) {
  .lb-user-detail {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .action-buttons .el-button {
    margin-bottom: 10px;
  }
}
</style>
