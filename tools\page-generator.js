#!/usr/bin/env node

/**
 * 页面和API自动生成工具
 * 基于ServiceFenlei.vue、MarketList.vue、MarketNotice.vue的成功模式
 * 自动生成完整的CRUD页面和API配置
 */

const fs = require('fs')
const path = require('path')

// 配置模板
const CONFIG_TEMPLATE = {
  // API配置模板
  api: {
    baseUrl: '/api/admin',
    methods: ['list', 'info', 'add', 'update', 'delete', 'status']
  },
  
  // 页面配置模板
  page: {
    components: ['TopNav', 'LbButton', 'LbPage'],
    features: ['search', 'table', 'pagination', 'dialog', 'crud']
  },
  
  // 字段类型映射
  fieldTypes: {
    'id': { type: 'number', display: 'ID', width: '100', align: 'center' },
    'name': { type: 'string', display: '名称', searchable: true, required: true },
    'title': { type: 'string', display: '标题', searchable: true, required: true },
    'content': { type: 'textarea', display: '内容', searchable: true, required: true },
    'status': { type: 'switch', display: '状态', searchable: true, options: [{label: '启用', value: 1}, {label: '禁用', value: 0}] },
    'type': { type: 'select', display: '类型', searchable: true },
    'createTime': { type: 'datetime', display: '创建时间', width: '120' },
    'updateTime': { type: 'datetime', display: '更新时间', width: '120' }
  }
}

/**
 * 生成API配置
 */
function generateAPI(config) {
  const { moduleName, moduleNameCn, apiPath, fields } = config
  
  const apiTemplate = `
  // ==================== ${moduleNameCn}管理相关接口 ====================

  /**
   * 获取${moduleNameCn}列表
   * @param {Object} querys 查询参数
${fields.filter(f => f.searchable).map(f => `   * @param {${f.type === 'number' ? 'number' : 'string'}} querys.${f.name} ${f.display}，非必填`).join('\n')}
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回${moduleNameCn}列表数据
   */
  ${moduleName}List(querys) {
    console.log('📋 ${moduleNameCn}列表API-V2请求参数:', querys)
    return get('${apiPath}/list', querys)
  },

  /**
   * 获取${moduleNameCn}详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id ${moduleNameCn}ID
   * @returns {Promise} 返回${moduleNameCn}详情数据
   */
  ${moduleName}Info(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('${moduleNameCn}ID不能为空'))
    }
    console.log('🔍 获取${moduleNameCn}详情API-V2请求:', querys)
    return get(\`${apiPath}/detail/\${querys.id}\`)
  },

  /**
   * 新增${moduleNameCn}
   * @param {Object} querys ${moduleNameCn}数据
${fields.filter(f => f.required).map(f => `   * @param {${f.type === 'number' ? 'number' : 'string'}} querys.${f.name} ${f.display}`).join('\n')}
   * @returns {Promise} 返回新增结果
   */
  ${moduleName}Add(querys) {
    const requiredField = '${fields.find(f => f.required && f.name !== 'id')?.name || 'name'}'
    if (!querys || !querys[requiredField]) {
      return Promise.reject(new Error('${fields.find(f => f.required && f.name !== 'id')?.display || '名称'}不能为空'))
    }

    const apiData = {
${fields.filter(f => f.name !== 'id' && f.name !== 'createTime' && f.name !== 'updateTime').map(f => {
  if (f.type === 'number') {
    return `      ${f.name}: querys.${f.name} || ${f.defaultValue || 0},`
  } else {
    return `      ${f.name}: querys.${f.name} || '${f.defaultValue || ''}',`
  }
}).join('\n')}
    }

    console.log('➕ 新增${moduleNameCn}API-V2请求数据:', apiData)
    return post('${apiPath}/add', apiData)
  },

  /**
   * 编辑${moduleNameCn}
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  ${moduleName}Update(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('${moduleNameCn}ID不能为空'))
    }

    console.log('✏️ 编辑${moduleNameCn}API-V2请求:', querys)
    return post('${apiPath}/edit', querys)
  },

  /**
   * 删除${moduleNameCn}
   * @param {Object} querys 删除参数
   * @param {number} querys.id ${moduleNameCn}ID
   * @returns {Promise} 返回删除结果
   */
  ${moduleName}Delete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('${moduleNameCn}ID不能为空'))
    }

    console.log('🗑️ 删除${moduleNameCn}API-V2请求:', querys)
    return post(\`${apiPath}/delete/\${querys.id}\`)
  },

  /**
   * 修改${moduleNameCn}状态
   * @param {Object} querys 状态修改参数
   * @param {number} querys.id ${moduleNameCn}ID
   * @returns {Promise} 返回状态修改结果
   */
  ${moduleName}Status(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('${moduleNameCn}ID不能为空'))
    }

    console.log('🔄 修改${moduleNameCn}状态API-V2请求:', querys)
    return post(\`${apiPath}/status/\${querys.id}\`)
  },
`

  return apiTemplate
}

/**
 * 生成页面模板
 */
function generatePageTemplate(config) {
  const { moduleName, moduleNameCn, componentName, fields } = config
  
  // 生成搜索表单
  const searchFormFields = fields.filter(f => f.searchable).map(field => {
    if (field.type === 'select' && field.options) {
      return `          <el-form-item label="${field.display}" prop="${field.name}">
            <el-select
              size="default"
              v-model="searchForm.${field.name}"
              placeholder="请选择${field.display}"
              clearable
              style="width: 120px"
            >
${field.options.map(opt => `              <el-option label="${opt.label}" :value="${opt.value}" />`).join('\n')}
            </el-select>
          </el-form-item>`
    } else if (field.type === 'string' || field.type === 'textarea') {
      return `          <el-form-item label="${field.display}" prop="${field.name}">
            <el-input
              size="default"
              v-model="searchForm.${field.name}"
              placeholder="请输入${field.display}"
              clearable
              style="width: 200px"
            />
          </el-form-item>`
    }
    return ''
  }).filter(Boolean).join('\n\n')

  // 生成表格列
  const tableColumns = fields.map(field => {
    if (field.name === 'id') {
      return `        <el-table-column prop="id" label="ID" width="100" align="center" />`
    } else if (field.type === 'switch') {
      return `        <el-table-column prop="${field.name}" label="${field.display}" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.${field.name}"
              :active-value="${field.options[0].value}"
              :inactive-value="${field.options[1].value}"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>`
    } else if (field.type === 'select' && field.options) {
      return `        <el-table-column prop="${field.name}" label="${field.display}" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.${field.name} === ${field.options[0].value} ? 'primary' : 'warning'">
              {{ scope.row.${field.name} === ${field.options[0].value} ? '${field.options[0].label}' : '${field.options[1].label}' }}
            </el-tag>
          </template>
        </el-table-column>`
    } else if (field.type === 'datetime') {
      return `        <el-table-column prop="${field.name}" label="${field.display}" min-width="120">
          <template #default="scope">
            <div class="time-column">
              <p>{{ formatDate(scope.row.${field.name}) }}</p>
              <p>{{ formatTime(scope.row.${field.name}) }}</p>
            </div>
          </template>
        </el-table-column>`
    } else if (field.type === 'textarea') {
      return `        <el-table-column prop="${field.name}" label="${field.display}" min-width="300">
          <template #default="scope">
            <div class="content-column">
              <p>{{ scope.row.${field.name} }}</p>
            </div>
          </template>
        </el-table-column>`
    } else {
      return `        <el-table-column prop="${field.name}" label="${field.display}" min-width="150" />`
    }
  }).join('\n        \n')

  return { searchFormFields, tableColumns }
}

module.exports = {
  generateAPI,
  generatePageTemplate,
  CONFIG_TEMPLATE
}
