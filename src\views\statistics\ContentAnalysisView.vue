<!--
  内容分析模块
  内容数据和表现分析
-->

<template>
  <div class="content-analysis">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>内容分析</h1>
      <p class="page-subtitle">分析内容表现和用户互动数据</p>
    </div>

    <!-- 内容概览指标 -->
    <div class="metrics-grid">
      <div class="metric-card" v-for="metric in contentMetrics" :key="metric.key">
        <div class="metric-icon" :style="{ backgroundColor: metric.color }">
          <el-icon :size="28">
            <component :is="metric.icon" />
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatNumber(metric.value) }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-change" :class="metric.trend">
            <el-icon>
              <TrendCharts v-if="metric.trend === 'up'" />
              <Bottom v-else />
            </el-icon>
            {{ metric.change }}
          </div>
        </div>
      </div>
    </div>

    <!-- 内容表现分析 -->
    <div class="performance-section">
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>内容发布趋势</h3>
            <el-button-group size="small">
              <el-button :type="publishPeriod === 'week' ? 'primary' : ''" @click="publishPeriod = 'week'">
                最近一周
              </el-button>
              <el-button :type="publishPeriod === 'month' ? 'primary' : ''" @click="publishPeriod = 'month'">
                最近一月
              </el-button>
            </el-button-group>
          </div>
          <div class="chart-content">
            <div class="mock-chart">
              <div class="chart-placeholder">
                <el-icon :size="64"><EditPen /></el-icon>
                <p>内容发布趋势图</p>
                <div class="publish-data">
                  <div class="publish-item" v-for="(item, index) in publishTrendData" :key="index">
                    <span class="publish-date">{{ item.date }}</span>
                    <span class="publish-count">{{ item.count }}篇</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>内容分类分布</h3>
          </div>
          <div class="chart-content">
            <div class="category-stats">
              <div class="category-item" v-for="category in categoryData" :key="category.name">
                <div class="category-info">
                  <div class="category-color" :style="{ backgroundColor: category.color }"></div>
                  <span class="category-name">{{ category.name }}</span>
                </div>
                <div class="category-stats-detail">
                  <span class="category-count">{{ category.count }}篇</span>
                  <span class="category-percentage">{{ category.percentage }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门内容排行 -->
    <div class="popular-section">
      <div class="table-card">
        <div class="table-header">
          <h3>热门内容排行</h3>
          <div class="header-controls">
            <el-select v-model="sortBy" size="small" style="width: 120px">
              <el-option label="按阅读量" value="views" />
              <el-option label="按点赞数" value="likes" />
              <el-option label="按评论数" value="comments" />
              <el-option label="按分享数" value="shares" />
            </el-select>
            <el-button size="small" @click="refreshPopularData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        <div class="table-content">
          <el-table
            v-loading="popularLoading"
            :data="popularContent"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="rank" label="排名" width="80" align="center">
              <template #default="{ row }">
                <el-tag
                  :type="row.rank <= 3 ? 'warning' : 'info'"
                  size="small"
                >
                  {{ row.rank }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <el-link type="primary">{{ row.title }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="category" label="分类" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ row.category }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="author" label="作者" width="100" />
            <el-table-column prop="views" label="阅读量" width="100" align="center">
              <template #default="{ row }">
                <strong>{{ formatNumber(row.views) }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="likes" label="点赞数" width="100" align="center" />
            <el-table-column prop="comments" label="评论数" width="100" align="center" />
            <el-table-column prop="shares" label="分享数" width="100" align="center" />
            <el-table-column prop="publishTime" label="发布时间" width="160" />
          </el-table>
        </div>
      </div>
    </div>

    <!-- 内容互动分析 -->
    <div class="interaction-section">
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>互动数据趋势</h3>
          </div>
          <div class="chart-content">
            <div class="interaction-stats">
              <div class="interaction-item" v-for="interaction in interactionData" :key="interaction.type">
                <div class="interaction-header">
                  <span class="interaction-label">{{ interaction.label }}</span>
                  <span class="interaction-total">{{ formatNumber(interaction.total) }}</span>
                </div>
                <div class="interaction-chart">
                  <div class="interaction-bar">
                    <div
                      class="interaction-progress"
                      :style="{ width: interaction.percentage + '%', backgroundColor: interaction.color }"
                    ></div>
                  </div>
                  <span class="interaction-change" :class="interaction.trend">
                    {{ interaction.change }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>内容质量评分</h3>
          </div>
          <div class="chart-content">
            <div class="quality-stats">
              <div class="quality-overview">
                <div class="quality-score">
                  <span class="score-value">8.6</span>
                  <span class="score-label">综合评分</span>
                </div>
                <div class="score-trend">
                  <el-icon class="trend-up"><TrendCharts /></el-icon>
                  <span>+0.3</span>
                </div>
              </div>
              <div class="quality-details">
                <div class="quality-item" v-for="quality in qualityData" :key="quality.type">
                  <span class="quality-name">{{ quality.name }}</span>
                  <div class="quality-bar">
                    <div
                      class="quality-fill"
                      :style="{ width: quality.score * 10 + '%', backgroundColor: getQualityColor(quality.score) }"
                    ></div>
                  </div>
                  <span class="quality-value">{{ quality.score }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  Bottom,
  EditPen,
  Refresh,
  Document,
  View,
  ChatDotRound,
  Share
} from '@element-plus/icons-vue'

// 响应式数据
const popularLoading = ref(false)
const publishPeriod = ref('month')
const sortBy = ref('views')

// 内容指标数据
const contentMetrics = ref([
  {
    key: 'totalContent',
    label: '总内容数',
    value: 2345,
    change: '+15.3%',
    trend: 'up',
    color: '#409eff',
    icon: 'Document'
  },
  {
    key: 'totalViews',
    label: '总阅读量',
    value: 456789,
    change: '+22.1%',
    trend: 'up',
    color: '#67c23a',
    icon: 'View'
  },
  {
    key: 'totalInteractions',
    label: '总互动数',
    value: 89012,
    change: '+8.7%',
    trend: 'up',
    color: '#e6a23c',
    icon: 'ChatDotRound'
  },
  {
    key: 'avgEngagement',
    label: '平均参与度',
    value: 67,
    change: '-1.2%',
    trend: 'down',
    color: '#f56c6c',
    icon: 'Share'
  }
])

// 发布趋势数据
const publishTrendData = ref([
  { date: '01-07', count: 12 },
  { date: '01-08', count: 15 },
  { date: '01-09', count: 8 },
  { date: '01-10', count: 18 },
  { date: '01-11', count: 22 }
])

// 分类分布数据
const categoryData = ref([
  { name: '技术分享', count: 456, percentage: 35, color: '#409eff' },
  { name: '产品动态', count: 312, percentage: 24, color: '#67c23a' },
  { name: '行业资讯', count: 234, percentage: 18, color: '#e6a23c' },
  { name: '公司新闻', count: 189, percentage: 15, color: '#f56c6c' },
  { name: '其他', count: 109, percentage: 8, color: '#909399' }
])

// 热门内容数据
const popularContent = ref([
  {
    rank: 1,
    title: 'Vue3 + Vite 项目搭建最佳实践',
    category: '技术分享',
    author: '张三',
    views: 12345,
    likes: 567,
    comments: 89,
    shares: 123,
    publishTime: '2025-01-10 10:00'
  },
  {
    rank: 2,
    title: 'Element Plus 组件库使用指南',
    category: '技术分享',
    author: '李四',
    views: 9876,
    likes: 432,
    comments: 67,
    shares: 98,
    publishTime: '2025-01-09 14:30'
  },
  {
    rank: 3,
    title: '2025年前端技术趋势展望',
    category: '行业资讯',
    author: '王五',
    views: 8765,
    likes: 398,
    comments: 56,
    shares: 87,
    publishTime: '2025-01-08 16:45'
  },
  {
    rank: 4,
    title: '公司年度技术大会圆满举办',
    category: '公司新闻',
    author: '赵六',
    views: 7654,
    likes: 234,
    comments: 45,
    shares: 67,
    publishTime: '2025-01-07 09:00'
  },
  {
    rank: 5,
    title: '新产品功能发布说明',
    category: '产品动态',
    author: '钱七',
    views: 6543,
    likes: 189,
    comments: 34,
    shares: 56,
    publishTime: '2025-01-06 11:20'
  }
])

// 互动数据
const interactionData = ref([
  {
    type: 'views',
    label: '阅读量',
    total: 456789,
    percentage: 85,
    color: '#409eff',
    trend: 'up',
    change: '+12.5%'
  },
  {
    type: 'likes',
    label: '点赞数',
    total: 23456,
    percentage: 65,
    color: '#67c23a',
    trend: 'up',
    change: '+8.3%'
  },
  {
    type: 'comments',
    label: '评论数',
    total: 12345,
    percentage: 45,
    color: '#e6a23c',
    trend: 'down',
    change: '-2.1%'
  },
  {
    type: 'shares',
    label: '分享数',
    total: 6789,
    percentage: 35,
    color: '#f56c6c',
    trend: 'up',
    change: '+15.7%'
  }
])

// 质量评分数据
const qualityData = ref([
  { type: 'readability', name: '可读性', score: 8.8 },
  { type: 'originality', name: '原创性', score: 9.2 },
  { type: 'engagement', name: '互动性', score: 7.9 },
  { type: 'timeliness', name: '时效性', score: 8.5 },
  { type: 'accuracy', name: '准确性', score: 9.0 }
])

// 方法
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const getQualityColor = (score) => {
  if (score >= 9) return '#67c23a'
  if (score >= 8) return '#409eff'
  if (score >= 7) return '#e6a23c'
  return '#f56c6c'
}

const refreshPopularData = async () => {
  popularLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 随机更新数据
    popularContent.value.forEach(item => {
      item.views += Math.floor(Math.random() * 100)
      item.likes += Math.floor(Math.random() * 10)
      item.comments += Math.floor(Math.random() * 5)
      item.shares += Math.floor(Math.random() * 8)
    })

    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('刷新数据失败')
  } finally {
    popularLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  console.log('内容分析页面已加载')
})
</script>

<style scoped>
.content-analysis {
  padding: var(--spacing-lg);
}

.page-title {
  margin-bottom: var(--spacing-xl);
}

.page-title h1 {
  font-size: var(--font-size-xxl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.metric-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  transition: var(--transition-box-shadow);
}

.metric-card:hover {
  box-shadow: var(--box-shadow-base);
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: var(--border-radius-lg);
  color: white;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.metric-change.up {
  color: var(--color-success);
}

.metric-change.down {
  color: var(--color-danger);
}

.performance-section,
.popular-section,
.interaction-section {
  margin-bottom: var(--spacing-xl);
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.chart-card,
.table-card {
  background: var(--bg-color-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.chart-header,
.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color-light);
  background-color: var(--bg-color-column);
}

.chart-header h3,
.table-header h3 {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  margin: 0;
}

.header-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.chart-content,
.table-content {
  padding: var(--spacing-lg);
}

.mock-chart {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: var(--color-text-secondary);
}

.chart-placeholder p {
  margin: var(--spacing-md) 0;
}

.publish-data {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.publish-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.publish-date {
  font-size: var(--font-size-xs);
  color: var(--color-text-placeholder);
}

.publish-count {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-primary);
}

.category-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color-light);
}

.category-item:last-child {
  border-bottom: none;
}

.category-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.category-name {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.category-stats-detail {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
}

.category-count {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.category-percentage {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.interaction-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.interaction-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.interaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.interaction-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.interaction-total {
  font-weight: 500;
  color: var(--color-text-primary);
}

.interaction-chart {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.interaction-bar {
  flex: 1;
  height: 8px;
  background-color: var(--bg-color-column);
  border-radius: 4px;
  overflow: hidden;
}

.interaction-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.interaction-change {
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.interaction-change.up {
  color: var(--color-success);
}

.interaction-change.down {
  color: var(--color-danger);
}

.quality-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.quality-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--bg-color-column);
  border-radius: var(--border-radius-sm);
}

.quality-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.score-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-primary);
}

.score-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.score-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-success);
  font-weight: 500;
}

.trend-up {
  color: var(--color-success);
}

.quality-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.quality-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.quality-name {
  width: 60px;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.quality-bar {
  flex: 1;
  height: 6px;
  background-color: var(--bg-color-column);
  border-radius: 3px;
  overflow: hidden;
}

.quality-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.quality-value {
  width: 30px;
  text-align: right;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-primary);
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .content-analysis {
    padding: var(--spacing-md);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .metric-card {
    padding: var(--spacing-lg);
  }

  .chart-content,
  .table-content {
    padding: var(--spacing-md);
  }

  .publish-data {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .header-controls {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
</style>
