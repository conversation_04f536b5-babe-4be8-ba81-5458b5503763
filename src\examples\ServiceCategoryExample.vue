<template>
  <div class="service-category-example">
    <h2>🗂️ 服务分类管理示例</h2>
    
    <!-- 分类列表 -->
    <div class="section">
      <h3>分类列表</h3>
      <el-button @click="loadCategoryList" type="primary">加载分类列表</el-button>
      <el-table :data="categoryList" style="width: 100%; margin-top: 10px;">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="分类名称"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '可用' : '不可用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recommend" label="推荐">
          <template #default="scope">
            <el-tag :type="scope.row.recommend === 1 ? 'warning' : 'info'">
              {{ scope.row.recommend === 1 ? '推荐' : '普通' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="viewCategory(scope.row.id)">查看</el-button>
            <el-button size="small" type="warning" @click="toggleStatus(scope.row)">
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button size="small" type="success" @click="toggleRecommend(scope.row)">
              {{ scope.row.recommend === 1 ? '取消推荐' : '设为推荐' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增分类 -->
    <div class="section">
      <h3>新增分类</h3>
      <el-form :model="newCategory" label-width="100px" style="max-width: 500px;">
        <el-form-item label="分类名称">
          <el-input v-model="newCategory.name" placeholder="请输入分类名称"></el-input>
        </el-form-item>
        <el-form-item label="分类图片">
          <el-input v-model="newCategory.img" placeholder="请输入图片URL"></el-input>
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input v-model="newCategory.description" type="textarea" placeholder="请输入分类描述"></el-input>
        </el-form-item>
        <el-form-item label="父级分类">
          <el-select v-model="newCategory.parentId" placeholder="请选择父级分类">
            <el-option label="顶级分类" :value="0"></el-option>
            <el-option 
              v-for="parent in parentCategories" 
              :key="parent.id" 
              :label="parent.name" 
              :value="parent.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="newCategory.sort" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addCategory">新增分类</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作日志 -->
    <div class="section">
      <h3>操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span :class="['log-message', log.type]">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCurrentInstance } from 'vue'

export default {
  name: 'ServiceCategoryExample',
  data() {
    return {
      categoryList: [],
      parentCategories: [],
      newCategory: {
        name: '',
        img: '',
        description: '',
        parentId: 0,
        sort: 0,
        status: 1,
        recommend: 0
      },
      logs: []
    }
  },
  mounted() {
    this.loadParentCategories()
  },
  methods: {
    // 获取Vue实例和API
    getApi() {
      const { proxy } = getCurrentInstance()
      return proxy.$api
    },

    // 添加日志
    addLog(message, type = 'info') {
      this.logs.unshift({
        time: new Date().toLocaleTimeString(),
        message,
        type
      })
      if (this.logs.length > 10) {
        this.logs.pop()
      }
    },

    // 加载分类列表
    async loadCategoryList() {
      try {
        this.addLog('🔍 正在加载分类列表...', 'info')
        const result = await this.getApi().service.serviceCateList({
          pageNum: 1,
          pageSize: 20,
          status: 1
        })
        
        if (result.code === '200') {
          this.categoryList = result.data.list || []
          this.addLog(`✅ 分类列表加载成功，共 ${this.categoryList.length} 条记录`, 'success')
        } else {
          this.addLog(`❌ 分类列表加载失败：${result.msg}`, 'error')
        }
      } catch (error) {
        this.addLog(`❌ 分类列表加载异常：${error.message}`, 'error')
        console.error('加载分类列表失败:', error)
      }
    },

    // 加载父级分类列表
    async loadParentCategories() {
      try {
        const result = await this.getApi().service.serviceCateParentList({
          status: 1
        })
        
        if (result.code === '200') {
          this.parentCategories = result.data || []
          this.addLog(`✅ 父级分类列表加载成功`, 'success')
        }
      } catch (error) {
        this.addLog(`❌ 父级分类列表加载失败：${error.message}`, 'error')
        console.error('加载父级分类列表失败:', error)
      }
    },

    // 查看分类详情
    async viewCategory(id) {
      try {
        this.addLog(`🔍 正在查看分类详情 ID: ${id}`, 'info')
        const result = await this.getApi().service.serviceCateInfo({ id })
        
        if (result.code === '200') {
          this.addLog(`✅ 分类详情获取成功：${result.data.name}`, 'success')
          console.log('分类详情:', result.data)
        } else {
          this.addLog(`❌ 分类详情获取失败：${result.msg}`, 'error')
        }
      } catch (error) {
        this.addLog(`❌ 分类详情获取异常：${error.message}`, 'error')
        console.error('获取分类详情失败:', error)
      }
    },

    // 切换状态
    async toggleStatus(category) {
      try {
        const newStatus = category.status === 1 ? -1 : 1
        this.addLog(`🔄 正在${newStatus === 1 ? '启用' : '禁用'}分类：${category.name}`, 'info')
        
        const result = await this.getApi().service.serviceCateStatus({
          id: category.id,
          status: newStatus
        })
        
        if (result.code === '200') {
          category.status = newStatus
          this.addLog(`✅ 分类状态更新成功`, 'success')
        } else {
          this.addLog(`❌ 分类状态更新失败：${result.msg}`, 'error')
        }
      } catch (error) {
        this.addLog(`❌ 分类状态更新异常：${error.message}`, 'error')
        console.error('更新分类状态失败:', error)
      }
    },

    // 切换推荐状态
    async toggleRecommend(category) {
      try {
        const newRecommend = category.recommend === 1 ? 0 : 1
        this.addLog(`⭐ 正在${newRecommend === 1 ? '设为推荐' : '取消推荐'}分类：${category.name}`, 'info')
        
        const result = await this.getApi().service.serviceCateRecommend({
          id: category.id,
          recommend: newRecommend
        })
        
        if (result.code === '200') {
          category.recommend = newRecommend
          this.addLog(`✅ 分类推荐状态更新成功`, 'success')
        } else {
          this.addLog(`❌ 分类推荐状态更新失败：${result.msg}`, 'error')
        }
      } catch (error) {
        this.addLog(`❌ 分类推荐状态更新异常：${error.message}`, 'error')
        console.error('更新分类推荐状态失败:', error)
      }
    },

    // 新增分类
    async addCategory() {
      if (!this.newCategory.name) {
        this.addLog('❌ 请输入分类名称', 'error')
        return
      }

      try {
        this.addLog(`➕ 正在新增分类：${this.newCategory.name}`, 'info')
        const result = await this.getApi().service.serviceCateAdd(this.newCategory)
        
        if (result.code === '200') {
          this.addLog(`✅ 分类新增成功`, 'success')
          this.resetForm()
          this.loadCategoryList() // 重新加载列表
        } else {
          this.addLog(`❌ 分类新增失败：${result.msg}`, 'error')
        }
      } catch (error) {
        this.addLog(`❌ 分类新增异常：${error.message}`, 'error')
        console.error('新增分类失败:', error)
      }
    },

    // 重置表单
    resetForm() {
      this.newCategory = {
        name: '',
        img: '',
        description: '',
        parentId: 0,
        sort: 0,
        status: 1,
        recommend: 0
      }
    }
  }
}
</script>

<style scoped>
.service-category-example {
  padding: 20px;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-message.success {
  color: #67c23a;
}

.log-message.error {
  color: #f56c6c;
}

.log-message.info {
  color: #409eff;
}
</style>
