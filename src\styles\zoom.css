/*
  页面缩放样式
  支持页面整体缩放功能
*/

/* 基础缩放容器样式 */
.page-container,
.zoom-container {
  transform-origin: top left;
  transition: transform 0.3s ease;
  width: 100%;
  min-height: 100vh;
}

/* 布局容器缩放优化 */
.layout-container {
  transform-origin: top left;
  transition: transform 0.3s ease;
}

/* 缩放级别定义 */
.zoom-small {
  transform: scale(0.8);
}

.zoom-normal {
  transform: scale(1);
}

.zoom-large {
  transform: scale(1.2);
}

.zoom-extra-large {
  transform: scale(1.4);
}

/* 布局容器缩放级别 */
.layout-container.zoom-small {
  transform: scale(0.8);
}

.layout-container.zoom-normal {
  transform: scale(1);
}

.layout-container.zoom-large {
  transform: scale(1.2);
}

.layout-container.zoom-extra-large {
  transform: scale(1.4);
}

/* 确保缩放时页面布局正确 */
.zoom-small .page-main,
.zoom-large .page-main,
.zoom-extra-large .page-main {
  overflow: visible;
}

/* 布局容器缩放时的优化 */
.layout-container.zoom-small,
.layout-container.zoom-large,
.layout-container.zoom-extra-large {
  /* 确保缩放时布局容器能正确显示 */
  width: 100vw;
  height: 100vh;
  overflow: visible;
}

/* 缩放时调整视口大小以避免滚动条问题 */
body:has(.layout-container.zoom-small) {
  overflow-x: auto;
  min-width: calc(100vw / 0.8);
}

body:has(.layout-container.zoom-large) {
  overflow-x: auto;
  min-width: calc(100vw / 1.2);
}

body:has(.layout-container.zoom-extra-large) {
  overflow-x: auto;
  min-width: calc(100vw / 1.4);
}

/* 布局缩放时的body样式优化 */
body.layout-zoom-small {
  overflow-x: auto;
  min-width: calc(100vw / 0.8);
}

body.layout-zoom-large {
  overflow-x: auto;
  min-width: calc(100vw / 1.2);
}

body.layout-zoom-extra-large {
  overflow-x: auto;
  min-width: calc(100vw / 1.4);
}

body.layout-zoom-normal {
  overflow-x: auto;
  min-width: 100vw;
}

/* 缩放时调整body的最小高度，避免滚动条问题 */
body:has(.zoom-small) {
  min-height: calc(100vh / 0.8);
}

body:has(.zoom-large) {
  min-height: calc(100vh / 1.2);
}

body:has(.zoom-extra-large) {
  min-height: calc(100vh / 1.4);
}

/* 针对不同缩放级别的特殊调整 */

/* 小缩放时的优化 */
.zoom-small {
  /* 确保小缩放时内容不会太小而难以阅读 */
}

.zoom-small .el-table .cell {
  font-size: 14px;
}

.zoom-small .el-button {
  min-height: 28px;
}

/* 大缩放时的优化 */
.zoom-large,
.zoom-extra-large {
  /* 确保大缩放时内容不会超出屏幕 */
}

.zoom-large .el-table .cell,
.zoom-extra-large .el-table .cell {
  word-break: break-word;
}

/* 响应式缩放调整 */
@media (max-width: 1200px) {
  .zoom-large {
    transform: scale(1.1);
  }
  
  .zoom-extra-large {
    transform: scale(1.25);
  }
}

@media (max-width: 768px) {
  .zoom-small {
    transform: scale(0.85);
  }
  
  .zoom-large {
    transform: scale(1.05);
  }
  
  .zoom-extra-large {
    transform: scale(1.15);
  }
}

@media (max-width: 480px) {
  .zoom-small {
    transform: scale(0.9);
  }
  
  .zoom-normal {
    transform: scale(1);
  }
  
  .zoom-large {
    transform: scale(1.02);
  }
  
  .zoom-extra-large {
    transform: scale(1.05);
  }
}

/* 打印时禁用缩放 */
@media print {
  .zoom-small,
  .zoom-normal,
  .zoom-large,
  .zoom-extra-large {
    transform: scale(1) !important;
  }
}

/* 缩放动画优化 */
.zoom-container * {
  transition: inherit;
}

/* 确保固定定位元素在缩放时正确显示 */
.zoom-small .zoom-controls,
.zoom-large .zoom-controls,
.zoom-extra-large .zoom-controls {
  transform: scale(1);
  transform-origin: top right;
}

/* 缩放控制按钮在布局容器缩放时的位置调整 */
.layout-container.zoom-small ~ .zoom-controls,
.layout-container.zoom-large ~ .zoom-controls,
.layout-container.zoom-extra-large ~ .zoom-controls {
  position: fixed;
  z-index: 9999;
  transform: scale(1) !important;
}

/* 针对不同缩放级别调整缩放控制按钮位置 */
.layout-container.zoom-small ~ .zoom-controls {
  top: calc(80px * 0.8);
  right: calc(20px * 0.8);
}

.layout-container.zoom-large ~ .zoom-controls {
  top: calc(80px * 1.2);
  right: calc(20px * 1.2);
}

.layout-container.zoom-extra-large ~ .zoom-controls {
  top: calc(80px * 1.4);
  right: calc(20px * 1.4);
}

/* 缩放时的滚动条优化 */
.zoom-small::-webkit-scrollbar,
.zoom-large::-webkit-scrollbar,
.zoom-extra-large::-webkit-scrollbar {
  width: 8px;
}

.zoom-small::-webkit-scrollbar-thumb,
.zoom-large::-webkit-scrollbar-thumb,
.zoom-extra-large::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

/* 缩放时的表格优化 */
.zoom-small .el-table,
.zoom-large .el-table,
.zoom-extra-large .el-table {
  table-layout: auto;
}

.zoom-small .el-table .el-table__cell,
.zoom-large .el-table .el-table__cell,
.zoom-extra-large .el-table .el-table__cell {
  padding: 8px;
}

/* 缩放时的对话框优化 */
.zoom-large .el-dialog,
.zoom-extra-large .el-dialog {
  max-width: 90vw;
}

.zoom-small .el-dialog {
  min-width: 400px;
}

/* 布局容器缩放时的弹窗层级优化 */
.layout-container.zoom-small .el-overlay,
.layout-container.zoom-normal .el-overlay,
.layout-container.zoom-large .el-overlay,
.layout-container.zoom-extra-large .el-overlay {
  z-index: 10001 !important;
}

.layout-container.zoom-small .el-dialog,
.layout-container.zoom-normal .el-dialog,
.layout-container.zoom-large .el-dialog,
.layout-container.zoom-extra-large .el-dialog {
  z-index: 10002 !important;
}

/* 确保弹窗在缩放时不受影响 */
.layout-container.zoom-small .el-overlay .el-dialog,
.layout-container.zoom-large .el-overlay .el-dialog,
.layout-container.zoom-extra-large .el-overlay .el-dialog {
  transform: scale(1) !important;
  transform-origin: center center !important;
}

/* Element Plus 其他弹出组件的z-index优化 */
.layout-container.zoom-small .el-message,
.layout-container.zoom-normal .el-message,
.layout-container.zoom-large .el-message,
.layout-container.zoom-extra-large .el-message {
  z-index: 10003 !important;
}

.layout-container.zoom-small .el-message-box,
.layout-container.zoom-normal .el-message-box,
.layout-container.zoom-large .el-message-box,
.layout-container.zoom-extra-large .el-message-box {
  z-index: 10004 !important;
}

.layout-container.zoom-small .el-notification,
.layout-container.zoom-normal .el-notification,
.layout-container.zoom-large .el-notification,
.layout-container.zoom-extra-large .el-notification {
  z-index: 10005 !important;
}

.layout-container.zoom-small .el-drawer,
.layout-container.zoom-normal .el-drawer,
.layout-container.zoom-large .el-drawer,
.layout-container.zoom-extra-large .el-drawer {
  z-index: 10006 !important;
}

.layout-container.zoom-small .el-popover,
.layout-container.zoom-normal .el-popover,
.layout-container.zoom-large .el-popover,
.layout-container.zoom-extra-large .el-popover {
  z-index: 10007 !important;
}

.layout-container.zoom-small .el-tooltip,
.layout-container.zoom-normal .el-tooltip,
.layout-container.zoom-large .el-tooltip,
.layout-container.zoom-extra-large .el-tooltip {
  z-index: 10008 !important;
}
