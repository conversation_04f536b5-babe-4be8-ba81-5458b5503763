/* 
  页面缩放样式
  支持页面整体缩放功能
*/

/* 基础缩放容器样式 */
.page-container,
.zoom-container {
  transform-origin: top left;
  transition: transform 0.3s ease;
  width: 100%;
  min-height: 100vh;
}

/* 缩放级别定义 */
.zoom-small {
  transform: scale(0.8);
}

.zoom-normal {
  transform: scale(1);
}

.zoom-large {
  transform: scale(1.2);
}

.zoom-extra-large {
  transform: scale(1.4);
}

/* 确保缩放时页面布局正确 */
.zoom-small .page-main,
.zoom-large .page-main,
.zoom-extra-large .page-main {
  overflow: visible;
}

/* 缩放时调整body的最小高度，避免滚动条问题 */
body:has(.zoom-small) {
  min-height: calc(100vh / 0.8);
}

body:has(.zoom-large) {
  min-height: calc(100vh / 1.2);
}

body:has(.zoom-extra-large) {
  min-height: calc(100vh / 1.4);
}

/* 针对不同缩放级别的特殊调整 */

/* 小缩放时的优化 */
.zoom-small {
  /* 确保小缩放时内容不会太小而难以阅读 */
}

.zoom-small .el-table .cell {
  font-size: 14px;
}

.zoom-small .el-button {
  min-height: 28px;
}

/* 大缩放时的优化 */
.zoom-large,
.zoom-extra-large {
  /* 确保大缩放时内容不会超出屏幕 */
}

.zoom-large .el-table .cell,
.zoom-extra-large .el-table .cell {
  word-break: break-word;
}

/* 响应式缩放调整 */
@media (max-width: 1200px) {
  .zoom-large {
    transform: scale(1.1);
  }
  
  .zoom-extra-large {
    transform: scale(1.25);
  }
}

@media (max-width: 768px) {
  .zoom-small {
    transform: scale(0.85);
  }
  
  .zoom-large {
    transform: scale(1.05);
  }
  
  .zoom-extra-large {
    transform: scale(1.15);
  }
}

@media (max-width: 480px) {
  .zoom-small {
    transform: scale(0.9);
  }
  
  .zoom-normal {
    transform: scale(1);
  }
  
  .zoom-large {
    transform: scale(1.02);
  }
  
  .zoom-extra-large {
    transform: scale(1.05);
  }
}

/* 打印时禁用缩放 */
@media print {
  .zoom-small,
  .zoom-normal,
  .zoom-large,
  .zoom-extra-large {
    transform: scale(1) !important;
  }
}

/* 缩放动画优化 */
.zoom-container * {
  transition: inherit;
}

/* 确保固定定位元素在缩放时正确显示 */
.zoom-small .zoom-controls,
.zoom-large .zoom-controls,
.zoom-extra-large .zoom-controls {
  transform: scale(1);
  transform-origin: top right;
}

/* 缩放时的滚动条优化 */
.zoom-small::-webkit-scrollbar,
.zoom-large::-webkit-scrollbar,
.zoom-extra-large::-webkit-scrollbar {
  width: 8px;
}

.zoom-small::-webkit-scrollbar-thumb,
.zoom-large::-webkit-scrollbar-thumb,
.zoom-extra-large::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

/* 缩放时的表格优化 */
.zoom-small .el-table,
.zoom-large .el-table,
.zoom-extra-large .el-table {
  table-layout: auto;
}

.zoom-small .el-table .el-table__cell,
.zoom-large .el-table .el-table__cell,
.zoom-extra-large .el-table .el-table__cell {
  padding: 8px;
}

/* 缩放时的对话框优化 */
.zoom-large .el-dialog,
.zoom-extra-large .el-dialog {
  max-width: 90vw;
}

.zoom-small .el-dialog {
  min-width: 400px;
}
