/**
 * 分销管理模块 - V2版本
 * 按照API封装规范文档实现分销管理相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取分销商审核列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 审核状态，1待审核，2已通过，3已拒绝
   * @param {string} querys.name 分销商姓名，非必填
   * @param {string} querys.phone 手机号，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回审核列表数据
   */
  distributorExamineList(querys) {
    console.log('📋 分销商审核列表API-V2请求参数:', querys)
    return get('/api/admin/distribution/examine/list', querys)
  },

  /**
   * 获取分销商审核详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 申请ID
   * @returns {Promise} 返回审核详情数据
   */
  distributorExamineInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('申请ID不能为空'))
    }
    console.log('🔍 获取分销商审核详情API-V2请求:', querys)
    return get(`/api/admin/distribution/examine/info/${querys.id}`)
  },

  /**
   * 审核分销商申请
   * @param {Object} querys 审核参数
   * @param {number} querys.id 申请ID
   * @param {number} querys.status 审核状态，2通过，3拒绝
   * @param {string} querys.remark 审核备注
   * @returns {Promise} 返回审核结果
   */
  distributorExamineHandle(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('申请ID不能为空'))
    }

    if (![2, 3].includes(querys.status)) {
      return Promise.reject(new Error('审核状态无效'))
    }

    console.log('✅ 审核分销商申请API-V2请求:', querys)
    return post(`/api/admin/distribution/examine/handle/${querys.id}`, querys)
  },

  /**
   * 获取分销商列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，1正常，-1禁用
   * @param {string} querys.name 分销商姓名，非必填
   * @param {string} querys.phone 手机号，非必填
   * @param {number} querys.level 分销等级，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回分销商列表数据
   */
  distributorList(querys) {
    console.log('👥 分销商列表API-V2请求参数:', querys)
    return get('/api/admin/distribution/list', querys)
  },

  /**
   * 获取分销商详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 分销商ID
   * @returns {Promise} 返回分销商详情数据
   */
  distributorInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分销商ID不能为空'))
    }
    console.log('🔍 获取分销商详情API-V2请求:', querys)
    return get(`/api/admin/distribution/info/${querys.id}`)
  },

  /**
   * 新增分销商
   * @param {Object} querys 分销商数据
   * @param {string} querys.name 分销商姓名
   * @param {string} querys.phone 手机号
   * @param {string} querys.avatar 头像URL
   * @param {number} querys.level 分销等级
   * @param {number} querys.commissionRate 佣金比例（百分比）
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回新增结果
   */
  distributorAdd(querys) {
    if (!querys || !querys.name || !querys.phone) {
      return Promise.reject(new Error('分销商姓名和手机号不能为空'))
    }

    const apiData = {
      name: querys.name,
      phone: querys.phone,
      avatar: querys.avatar || '',
      level: querys.level || 1,
      commissionRate: querys.commissionRate || 10,
      status: querys.status || 1
    }

    console.log('➕ 新增分销商API-V2请求数据:', apiData)
    return post('/api/admin/distribution/add', apiData)
  },

  /**
   * 编辑分销商
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  distributorUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分销商ID不能为空'))
    }

    console.log('✏️ 编辑分销商API-V2请求:', querys)
    return post('/api/admin/distribution/update', querys)
  },

  /**
   * 删除分销商
   * @param {Object} querys 删除参数
   * @param {number} querys.id 分销商ID
   * @returns {Promise} 返回删除结果
   */
  distributorDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分销商ID不能为空'))
    }

    console.log('🗑️ 删除分销商API-V2请求:', querys)
    return post(`/api/admin/distribution/delete/${querys.id}`)
  },

  /**
   * 更新分销商状态
   * @param {Object} querys 状态更新参数
   * @param {number} querys.id 分销商ID
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回更新结果
   */
  distributorStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('分销商ID不能为空'))
    }

    if (![1, -1].includes(querys.status)) {
      return Promise.reject(new Error('状态值无效'))
    }

    console.log('🔄 更新分销商状态API-V2请求:', querys)
    return post(`/api/admin/distribution/status/${querys.id}`, { status: querys.status })
  },

  /**
   * 获取分销设置
   * @returns {Promise} 返回分销设置数据
   */
  distributionSettingInfo() {
    console.log('⚙️ 获取分销设置API-V2请求')
    return get('/api/admin/distribution/setting/info')
  },

  /**
   * 更新分销设置
   * @param {Object} querys 设置数据
   * @param {number} querys.enableDistribution 是否启用分销，1启用，0禁用
   * @param {number} querys.level1Rate 一级分销佣金比例
   * @param {number} querys.level2Rate 二级分销佣金比例
   * @param {number} querys.level3Rate 三级分销佣金比例
   * @param {number} querys.minWithdraw 最低提现金额
   * @param {string} querys.withdrawRule 提现规则说明
   * @returns {Promise} 返回更新结果
   */
  distributionSettingUpdate(querys) {
    console.log('⚙️ 更新分销设置API-V2请求:', querys)
    return post('/api/admin/distribution/setting/update', querys)
  },

  /**
   * 获取分销商业绩统计
   * @param {Object} querys 查询参数
   * @param {number} querys.distributorId 分销商ID
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @returns {Promise} 返回业绩统计数据
   */
  distributorPerformance(querys) {
    console.log('📊 分销商业绩统计API-V2请求参数:', querys)
    return get('/api/admin/distribution/performance', querys)
  },

  /**
   * 获取分销商下级列表
   * @param {Object} querys 查询参数
   * @param {number} querys.distributorId 分销商ID
   * @param {number} querys.level 下级层级，1一级，2二级，3三级
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回下级列表数据
   */
  distributorSubordinateList(querys) {
    if (!querys || !querys.distributorId) {
      return Promise.reject(new Error('分销商ID不能为空'))
    }

    console.log('👥 分销商下级列表API-V2请求参数:', querys)
    return get('/api/admin/distribution/subordinate/list', querys)
  }
}
