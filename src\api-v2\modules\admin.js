/**
 * 管理员管理API模块
 * 提供管理员相关的所有接口调用
 */

import { get, post } from '../index'
import CryptoJS from 'crypto-js'

export default {
  /**
   * 获取管理员列表
   * @param {Object} params 查询参数
   * @param {number} params.pageNum 页码（可选，默认1）
   * @param {number} params.pageSize 每页数量（可选，默认10）
   * @returns {Promise} 返回管理员列表数据
   */
  getAdminList(params = {}) {
    console.log('👥 获取管理员列表API-V2请求:', params)
    
    return get('/api/admin/manage/list', params)
      .then(response => {
        console.log('👥 管理员列表API响应:', response)
        return response
      })
      .catch(error => {
        console.error('👥 获取管理员列表失败:', error)
        throw error
      })
  },

  /**
   * 新增管理员
   * @param {Object} data 管理员数据
   * @param {string} data.username 用户名（必填）
   * @param {string} data.password 密码（可选，不传则使用默认密码admin123）
   * @param {string} data.roleId 角色ID（必填）
   * @returns {Promise} 返回新增结果
   */
  addAdmin(data) {
    console.log('➕ 新增管理员API-V2请求:', data)
    
    // 处理密码MD5加密
    const requestData = {
      username: data.username,
      roleId: data.roleId
    }
    
    // 如果有密码，进行MD5加密
    if (data.password && data.password.trim()) {
      requestData.password = CryptoJS.MD5(data.password).toString().toUpperCase()
    } else {
      // 不传密码，后端会设置默认值admin123
      requestData.password = null
    }
    
    return post('/api/admin/manage/add', requestData)
      .then(response => {
        console.log('➕ 新增管理员API响应:', response)
        return response
      })
      .catch(error => {
        console.error('➕ 新增管理员失败:', error)
        throw error
      })
  },

  /**
   * 编辑管理员
   * @param {Object} data 管理员数据
   * @param {number} data.id 管理员ID
   * @param {string} data.username 用户名（必填）
   * @param {string} data.roleId 角色ID（必填）
   * @returns {Promise} 返回编辑结果
   */
  editAdmin(data) {
    console.log('✏️ 编辑管理员API-V2请求:', data)
    
    // 编辑时不传密码
    const requestData = {
      id: data.id,
      username: data.username,
      roleId: data.roleId
    }
    
    return post('/api/admin/manage/edit', requestData)
      .then(response => {
        console.log('✏️ 编辑管理员API响应:', response)
        return response
      })
      .catch(error => {
        console.error('✏️ 编辑管理员失败:', error)
        throw error
      })
  },

  /**
   * 修改管理员状态
   * @param {number} id 管理员ID
   * @returns {Promise} 返回修改结果
   */
  changeAdminStatus(id) {
    console.log('🔄 修改管理员状态API-V2请求:', { id })
    
    return post(`/api/admin/manage/status/${id}`)
      .then(response => {
        console.log('🔄 修改管理员状态API响应:', response)
        return response
      })
      .catch(error => {
        console.error('🔄 修改管理员状态失败:', error)
        throw error
      })
  },

  /**
   * 获取所有角色列表（用于下拉选择）
   * 复用角色管理的接口
   * @returns {Promise} 返回角色列表
   */
  getAllRoles() {
    console.log('📋 获取所有角色API-V2请求（管理员管理用）')
    
    return get('/api/admin/role/all')
      .then(response => {
        console.log('📋 所有角色API响应（管理员管理用）:', response)
        return response
      })
      .catch(error => {
        console.error('📋 获取所有角色失败（管理员管理用）:', error)
        throw error
      })
  },

  /**
   * 格式化管理员菜单权限显示
   * @param {Array} menus 菜单数组
   * @returns {string} 格式化后的菜单名称字符串
   */
  formatMenuNames(menus) {
    if (!menus || !Array.isArray(menus) || menus.length === 0) {
      return '暂无权限'
    }
    
    // 按sort排序并提取菜单名称
    const sortedMenus = menus.sort((a, b) => a.sort - b.sort)
    const menuNames = sortedMenus.map(menu => menu.menuName)
    
    // 如果菜单太多，只显示前几个
    if (menuNames.length > 5) {
      return menuNames.slice(0, 5).join('、') + `等${menuNames.length}个权限`
    }
    
    return menuNames.join('、')
  },

  /**
   * 获取管理员权限菜单数量
   * @param {Array} menus 菜单数组
   * @returns {number} 菜单数量
   */
  getMenuCount(menus) {
    return menus && Array.isArray(menus) ? menus.length : 0
  },

  /**
   * 检查管理员是否有特定权限
   * @param {Array} menus 菜单数组
   * @param {string} route 路由路径
   * @returns {boolean} 是否有权限
   */
  hasPermission(menus, route) {
    if (!menus || !Array.isArray(menus)) {
      return false
    }
    
    return menus.some(menu => menu.route === route)
  },

  /**
   * 生成管理员权限摘要
   * @param {Array} menus 菜单数组
   * @returns {Object} 权限摘要信息
   */
  generatePermissionSummary(menus) {
    if (!menus || !Array.isArray(menus) || menus.length === 0) {
      return {
        total: 0,
        summary: '暂无权限',
        hasFullAccess: false
      }
    }
    
    const total = menus.length
    const allMenuRoutes = [
      '/service', '/technician', '/market', '/shop', '/distribution',
      '/finance', '/user', '/account', '/sys', '/log'
    ]
    
    const hasFullAccess = total >= allMenuRoutes.length
    
    let summary = ''
    if (hasFullAccess) {
      summary = '全部权限'
    } else if (total <= 3) {
      summary = menus.map(m => m.menuName).join('、')
    } else {
      summary = `${menus.slice(0, 2).map(m => m.menuName).join('、')}等${total}个权限`
    }
    
    return {
      total,
      summary,
      hasFullAccess
    }
  }
}
