/**
 * Vue Router 4 路由配置

 * 
 * 功能：
 * - 路由配置和懒加载
 * - 权限控制和路由守卫
 * - 动态路由和菜单生成
 * - 路由元信息管理
 */

import { createRouter, createWebHistory } from 'vue-router'
import { setupRouterGuards } from './guards'
import { constantRoutes } from './routes/constant'

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})
// router.addRoute('Service', {
//   path: 'text',
//   name: 'ServiceText',
//   component: () => import('@/views/service/ServiceText.vue'),
//   meta: { title: '测试项目' }
// })
// 设置路由守卫
setupRouterGuards(router)

// 导出路由实例
export default router
