{"name": "admin-system-v3", "version": "3.0.0", "description": "今师傅后台管理系统", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "build:config": "vite build && node scripts/build-config.js", "preview": "vite preview", "config": "node scripts/build-config.js", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.0", "crypto-js": "^4.2.0", "element-plus": "^2.10.4", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "quill": "^2.0.3", "vue": "^3.5.17", "vue-quill-editor": "^3.0.6", "vue-router": "^4.2.0", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.1.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie <= 11"], "keywords": ["vue3", "admin", "management-system", "element-plus", "vite", "composition-api", "mock", "javascript"], "author": "开发团队", "license": "MIT"}