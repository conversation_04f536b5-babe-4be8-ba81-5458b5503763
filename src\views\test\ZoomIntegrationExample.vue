<!--
  缩放功能集成示例
  展示如何在现有页面中快速集成缩放功能
-->

<template>
  <div class="zoom-integration-example page-container">
    <!-- 步骤1: 添加缩放控制组件 -->
    <ZoomControl 
      target-selector=".zoom-integration-example" 
      @zoom-change="handleZoomChange" 
    />
    
    <div class="page-header">
      <h1>缩放功能集成示例</h1>
      <p>这个页面展示了如何在现有页面中快速集成缩放功能</p>
    </div>

    <div class="integration-steps">
      <el-card class="step-card" shadow="never">
        <template #header>
          <div class="step-header">
            <span class="step-number">1</span>
            <span class="step-title">添加页面容器类</span>
          </div>
        </template>
        
        <div class="step-content">
          <p>在页面根元素上添加 <code>page-container</code> 类：</p>
          <pre><code>&lt;div class="your-page-name page-container"&gt;
  &lt;!-- 页面内容 --&gt;
&lt;/div&gt;</code></pre>
        </div>
      </el-card>

      <el-card class="step-card" shadow="never">
        <template #header>
          <div class="step-header">
            <span class="step-number">2</span>
            <span class="step-title">引入缩放控制组件</span>
          </div>
        </template>
        
        <div class="step-content">
          <p>在script部分导入组件：</p>
          <pre><code>import ZoomControl from '@/components/common/ZoomControl.vue'</code></pre>
          
          <p>在template中添加组件：</p>
          <pre><code>&lt;ZoomControl 
  target-selector=".your-page-name" 
  @zoom-change="handleZoomChange" 
/&gt;</code></pre>
        </div>
      </el-card>

      <el-card class="step-card" shadow="never">
        <template #header>
          <div class="step-header">
            <span class="step-number">3</span>
            <span class="step-title">添加事件处理（可选）</span>
          </div>
        </template>
        
        <div class="step-content">
          <p>添加缩放变化处理函数：</p>
          <pre><code>const handleZoomChange = (zoom) => {
  console.log('页面缩放级别已更改为:', zoom)
  // 可以在这里添加自定义逻辑
}</code></pre>
        </div>
      </el-card>

      <el-card class="step-card" shadow="never">
        <template #header>
          <div class="step-header">
            <span class="step-number">4</span>
            <span class="step-title">完成！</span>
          </div>
        </template>
        
        <div class="step-content">
          <p>现在你的页面已经支持缩放功能了！</p>
          <p>用户可以通过右上角的缩放控制按钮来调整页面大小。</p>
          
          <div class="feature-list">
            <h4>功能特点：</h4>
            <ul>
              <li>✅ 支持4个缩放级别（80%、100%、120%、140%）</li>
              <li>✅ 缩放偏好自动保存</li>
              <li>✅ 平滑的缩放动画</li>
              <li>✅ 响应式适配</li>
              <li>✅ 易于集成</li>
            </ul>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 示例内容区域 -->
    <div class="example-content">
      <h2>示例内容区域</h2>
      <p>这里是一些示例内容，用于演示缩放效果。你可以点击右上角的缩放按钮来测试不同的缩放级别。</p>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover">
            <h3>卡片1</h3>
            <p>这是第一个示例卡片的内容。</p>
            <el-button type="primary">操作按钮</el-button>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <h3>卡片2</h3>
            <p>这是第二个示例卡片的内容。</p>
            <el-button type="success">操作按钮</el-button>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <h3>卡片3</h3>
            <p>这是第三个示例卡片的内容。</p>
            <el-button type="warning">操作按钮</el-button>
          </el-card>
        </el-col>
      </el-row>

      <el-table :data="tableData" style="width: 100%; margin-top: 20px;" border>
        <el-table-column prop="name" label="名称" width="180" />
        <el-table-column prop="type" label="类型" width="180" />
        <el-table-column prop="description" label="描述" />
      </el-table>
    </div>

    <!-- 当前缩放状态显示 -->
    <div class="zoom-status">
      <el-alert
        :title="`当前缩放级别: ${currentZoomLevel}`"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ZoomControl from '@/components/common/ZoomControl.vue'

// 响应式数据
const currentZoomLevel = ref('正常 (100%)')

// 示例表格数据
const tableData = ref([
  {
    name: '缩放功能',
    type: '页面功能',
    description: '支持页面整体缩放，提供更好的用户体验'
  },
  {
    name: '响应式设计',
    type: '布局特性',
    description: '在不同屏幕尺寸下自动适配缩放比例'
  },
  {
    name: '状态持久化',
    type: '用户体验',
    description: '用户的缩放偏好会自动保存并在下次访问时恢复'
  }
])

// 缩放变化处理
const handleZoomChange = (zoom) => {
  const zoomLabels = {
    'small': '缩小 (80%)',
    'normal': '正常 (100%)',
    'large': '放大 (120%)',
    'extra-large': '超大 (140%)'
  }
  currentZoomLevel.value = zoomLabels[zoom] || '未知'
}
</script>

<style scoped>
.zoom-integration-example {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.integration-steps {
  max-width: 800px;
  margin: 0 auto 30px;
}

.step-card {
  margin-bottom: 20px;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 14px;
  font-weight: bold;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.step-content {
  padding: 10px 0;
}

.step-content p {
  margin-bottom: 10px;
  color: #606266;
  line-height: 1.6;
}

.step-content pre {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  margin: 10px 0;
  overflow-x: auto;
}

.step-content code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: #e6a23c;
}

.feature-list h4 {
  margin: 15px 0 10px 0;
  color: #303133;
}

.feature-list ul {
  margin: 0;
  padding-left: 20px;
}

.feature-list li {
  margin-bottom: 5px;
  color: #606266;
  line-height: 1.5;
}

.example-content {
  max-width: 1000px;
  margin: 0 auto 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.example-content h2 {
  margin: 0 0 15px 0;
  color: #303133;
}

.example-content p {
  margin-bottom: 20px;
  color: #606266;
  line-height: 1.6;
}

.zoom-status {
  max-width: 800px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .zoom-integration-example {
    padding: 10px;
  }
  
  .integration-steps,
  .example-content,
  .zoom-status {
    margin-left: 0;
    margin-right: 0;
  }
  
  .step-content pre {
    font-size: 12px;
    padding: 8px;
  }
}
</style>
