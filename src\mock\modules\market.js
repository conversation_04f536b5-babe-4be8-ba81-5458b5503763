/**
 * 营销管理模块Mock数据

 */

import Mock from 'mockjs'
import { successResponse, pageResponse, createCrudMock, errorResponse } from '../utils.js'

// 卡券管理数据
const couponList = Mock.mock({
  'list|20-50': [{
    'id|+1': 1,
    'name': '@ctitle(5, 15)',
    'type|1': [1, 2, 3], // 1-满减券 2-折扣券 3-代金券
    'amount|10-500.2': 1, // 面额
    'minAmount|0-1000.2': 1, // 最低消费金额
    'discount|0.1-0.9': 1, // 折扣(仅折扣券)
    'totalCount|100-10000': 1, // 总发放量
    'usedCount|0-5000': 1, // 已使用量
    'startTime': '@datetime',
    'endTime': '@datetime',
    'description': '@cparagraph(1, 2)',
    'status|1': [0, 1, 2], // 0-未开始 1-进行中 2-已结束
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 公告设置数据
const noticeList = Mock.mock({
  'list|10-30': [{
    'id|+1': 1,
    'title': '@ctitle(10, 30)',
    'content': '@cparagraph(3, 8)',
    'type|1': [1, 2, 3], // 1-系统公告 2-活动公告 3-维护公告
    'priority|1': [1, 2, 3], // 1-低 2-中 3-高
    'isTop|1': [true, false], // 是否置顶
    'startTime': '@datetime',
    'endTime': '@datetime',
    'readCount|0-10000': 1, // 阅读量
    'status|1': [0, 1], // 0-草稿 1-已发布
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 邀请好友活动数据
const inviteActivityList = Mock.mock({
  'list|5-15': [{
    'id|+1': 1,
    'name': '@ctitle(8, 20)',
    'description': '@cparagraph(2, 5)',
    'inviterReward|10-100.2': 1, // 邀请人奖励
    'inviteeReward|5-50.2': 1, // 被邀请人奖励
    'maxInviteCount|10-100': 1, // 最大邀请数量
    'startTime': '@datetime',
    'endTime': '@datetime',
    'participantCount|0-1000': 1, // 参与人数
    'successCount|0-500': 1, // 成功邀请数
    'totalReward|0-50000.2': 1, // 总奖励金额
    'status|1': [0, 1, 2], // 0-未开始 1-进行中 2-已结束
    'createTime': '@datetime',
    'updateTime': '@datetime'
  }]
}).list

// 创建CRUD接口
createCrudMock('/api/market/coupon', couponList)
createCrudMock('/api/market/notice', noticeList)
createCrudMock('/api/market/invite', inviteActivityList)

// 营销统计数据
Mock.mock('/api/market/statistics', 'get', () => {
  return successResponse({
    totalCoupons: couponList.length,
    activeCoupons: couponList.filter(item => item.status === 1).length,
    totalNotices: noticeList.length,
    activeNotices: noticeList.filter(item => item.status === 1).length,
    totalInviteActivities: inviteActivityList.length,
    activeInviteActivities: inviteActivityList.filter(item => item.status === 1).length,
    couponUsageRate: (couponList.reduce((sum, item) => sum + item.usedCount, 0) / couponList.reduce((sum, item) => sum + item.totalCount, 0) * 100).toFixed(1)
  })
})

// 卡券发放
Mock.mock('/api/market/coupon/distribute', 'post', (options) => {
  const { couponId, userIds, distributeType } = JSON.parse(options.body)
  
  return successResponse({
    couponId,
    distributeCount: userIds ? userIds.length : Mock.Random.integer(100, 1000),
    distributeType
  }, '卡券发放成功')
})

// 公告发布
Mock.mock(/\/api\/market\/notice\/publish\/(\d+)/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/publish\/(\d+)/)[1])
  const notice = noticeList.find(item => item.id === id)
  
  if (notice) {
    notice.status = 1
    notice.publishTime = Mock.Random.datetime()
    notice.updateTime = Mock.Random.datetime()
    return successResponse(notice, '公告发布成功')
  } else {
    return errorResponse('公告不存在', 404)
  }
})

// 公告置顶
Mock.mock(/\/api\/market\/notice\/top\/(\d+)/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/top\/(\d+)/)[1])
  const { isTop } = JSON.parse(options.body)
  const notice = noticeList.find(item => item.id === id)
  
  if (notice) {
    notice.isTop = isTop
    notice.updateTime = Mock.Random.datetime()
    return successResponse(notice, isTop ? '置顶成功' : '取消置顶成功')
  } else {
    return errorResponse('公告不存在', 404)
  }
})

// 合伙人管理数据
const partnerList = Mock.mock({
  'list|15-30': [{
    'id|+1': 1,
    'userId|11000-12000': 1,
    'level|1-3': 1,
    'commissionRate1|1-10.2': 1,
    'commissionRate2|1-15.2': 1,
    'status|1': [0, 1],
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'updateTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
  }]
}).list

// 合伙人邀请列表数据
const partnerInviteList = Mock.mock({
  'list|20-50': [{
    'id|11000-12000': 1,
    'nickName': '@pick(["@phone", "@cname"])',
    'phone': '@phone',
    'avatarUrl': '@pick(["/static/mine/default_user.png", "@image(100x100, #409eff, #fff, 头像)"])',
    'shifu|0-1': 0,
    'children|0-5': [{
      'nickName': '@cname',
      'phone': '@phone',
      'avatarUrl': '@image(60x60, #409eff, #fff, 头像)'
    }],
    'cnum|0-10': 1
  }]
}).list

// 创建CRUD接口
createCrudMock('/api/admin/partner', partnerList)

// 合伙人列表API
Mock.mock('/api/admin/partner/list', 'get', (options) => {
  const { userId, level, status, pageNum = 1, pageSize = 10 } = options.url.includes('?')
    ? Object.fromEntries(new URLSearchParams(options.url.split('?')[1]))
    : {}

  let filteredList = [...partnerList]

  if (userId) {
    filteredList = filteredList.filter(item => item.userId.toString().includes(userId))
  }
  if (level) {
    filteredList = filteredList.filter(item => item.level === parseInt(level))
  }
  if (status !== undefined && status !== '') {
    filteredList = filteredList.filter(item => item.status === parseInt(status))
  }

  const start = (pageNum - 1) * pageSize
  const end = start + parseInt(pageSize)
  const list = filteredList.slice(start, end)

  return successResponse({
    list,
    totalCount: filteredList.length,
    totalPage: Math.ceil(filteredList.length / pageSize),
    pageNum: parseInt(pageNum),
    pageSize: parseInt(pageSize)
  })
})

// 新增合伙人API
Mock.mock('/api/admin/partner/add', 'post', (options) => {
  const data = JSON.parse(options.body)
  const newPartner = {
    id: partnerList.length + 1,
    ...data,
    createTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
    updateTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
  }
  partnerList.push(newPartner)
  return successResponse(newPartner, '新增成功')
})

// 修改合伙人状态API
Mock.mock(/\/api\/admin\/partner\/status\/(\d+)/, 'post', (options) => {
  const id = parseInt(options.url.match(/\/status\/(\d+)/)[1])
  const partner = partnerList.find(item => item.id === id)

  if (partner) {
    partner.status = partner.status === 1 ? 0 : 1
    partner.updateTime = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    return successResponse(partner, '状态修改成功')
  } else {
    return errorResponse('合伙人不存在', 404)
  }
})

// 等级/分佣比例调整API
Mock.mock(/\/api\/admin\/partner\/updateLevelAndCommission\/(\d+)/, 'post', (options) => {
  const id = parseInt(options.url.match(/\/updateLevelAndCommission\/(\d+)/)[1])
  const partner = partnerList.find(item => item.id === id)

  if (partner) {
    // 模拟FormData解析
    const formData = options.body
    if (formData.get) {
      if (formData.get('level')) partner.level = parseInt(formData.get('level'))
      if (formData.get('commissionRate1')) partner.commissionRate1 = parseFloat(formData.get('commissionRate1'))
      if (formData.get('commissionRate2')) partner.commissionRate2 = parseFloat(formData.get('commissionRate2'))
    }
    partner.updateTime = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    return successResponse(partner, '调整成功')
  } else {
    return errorResponse('合伙人不存在', 404)
  }
})

// 合伙人邀请列表API
Mock.mock('/api/admin/partner/inviteList', 'get', (options) => {
  const { userId, type, pageNum = 1, pageSize = 10 } = options.url.includes('?')
    ? Object.fromEntries(new URLSearchParams(options.url.split('?')[1]))
    : {}

  let filteredList = [...partnerInviteList]

  if (userId) {
    filteredList = filteredList.filter(item => item.id.toString().includes(userId))
  }
  if (type && type !== '0') {
    const filterType = parseInt(type)
    if (filterType === 1) {
      filteredList = filteredList.filter(item => item.shifu === 0)
    } else if (filterType === 2) {
      filteredList = filteredList.filter(item => item.shifu === 1)
    }
  }

  const start = (pageNum - 1) * pageSize
  const end = start + parseInt(pageSize)
  const list = filteredList.slice(start, end)

  return successResponse({
    userNum: partnerInviteList.filter(item => item.shifu === 0).length,
    shiFuNum: partnerInviteList.filter(item => item.shifu === 1).length,
    vip: true,
    pageInfo: {
      list,
      totalCount: filteredList.length,
      totalPage: Math.ceil(filteredList.length / pageSize),
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }
  })
})

// 合伙人佣金统计列表数据
const partnerCommissionList = Mock.mock({
  'list|20-50': [{
    'id|+1': 1,
    'type|1': [1, 2],
    'price|0.01-10.00': 1,
    'userId|11000-12000': 1,
    'coachId|0-100': 1,
    'agentId|0': 0,
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
  }]
}).list

// 合伙人推广订单列表数据
const partnerOrdersList = Mock.mock({
  'list|15-30': [{
    'id|+1': 400,
    'orderCode': () => Mock.Random.datetime('yyyyMMddHHmmss') + Mock.Random.string('number', 12),
    'payType': '@pick(["1", "2", "7"])',
    'payPrice|10-200.0': 1,
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'goodsId|400-600': 1,
    'goodsName': '@pick(["洗菜盆疏通", "空调清洗", "洗衣机维修", "空调维修", "空调安装", "马桶安装"])',
    'goodsCover': '@pick(["https://zskj.asia/attachment/image/666/24/09/3be1eb5bac97455c9c4eba07f22412e1.jpeg", "https://zskj.asia/attachment/image/666/24/12/04c976c0061143dda484a822e684d17d.jpg"])',
    'canRefund': null,
    'refundStatus|0-1': 0,
    'isComment|0-1': 0,
    'quotedPriceVos': null,
    'coachServicePrice|5-180.0': 1,
    'num': null,
    'nickName': '@cname',
    'phone': '@phone'
  }]
}).list

// 合伙人佣金统计列表API
Mock.mock('/api/admin/partner/commission', 'get', (options) => {
  const { userId, pageNum = 1, pageSize = 10 } = options.url.includes('?')
    ? Object.fromEntries(new URLSearchParams(options.url.split('?')[1]))
    : {}

  let filteredList = [...partnerCommissionList]

  if (userId) {
    filteredList = filteredList.filter(item => item.userId.toString().includes(userId))
  }

  const start = (pageNum - 1) * pageSize
  const end = start + parseInt(pageSize)
  const list = filteredList.slice(start, end)

  return successResponse({
    list,
    totalCount: filteredList.length,
    totalPage: Math.ceil(filteredList.length / pageSize),
    pageNum: parseInt(pageNum),
    pageSize: parseInt(pageSize)
  })
})

// 合伙人推广订单列表API
Mock.mock('/api/admin/partner/orders', 'get', (options) => {
  const { userId, type, pageNum = 1, pageSize = 10 } = options.url.includes('?')
    ? Object.fromEntries(new URLSearchParams(options.url.split('?')[1]))
    : {}

  let filteredList = [...partnerOrdersList]

  if (userId) {
    filteredList = filteredList.filter(item => item.nickName.includes(userId) || item.phone.includes(userId))
  }
  if (type && type !== '0') {
    // 这里可以根据实际业务逻辑过滤订单类型
    // 暂时保持所有数据
  }

  const start = (pageNum - 1) * pageSize
  const end = start + parseInt(pageSize)
  const list = filteredList.slice(start, end)

  return successResponse({
    list,
    totalCount: filteredList.length,
    totalPage: Math.ceil(filteredList.length / pageSize),
    pageNum: parseInt(pageNum),
    pageSize: parseInt(pageSize)
  })
})

console.log('营销管理模块Mock数据已加载')

export { couponList, noticeList, inviteActivityList, partnerList, partnerInviteList, partnerCommissionList, partnerOrdersList }
