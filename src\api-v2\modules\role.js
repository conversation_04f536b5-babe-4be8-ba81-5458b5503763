/**
 * 角色管理API模块
 * 提供角色相关的所有接口调用
 */

import { get, post } from '../index'

export default {
  /**
   * 获取角色列表（分页）
   * @param {Object} params 查询参数
   * @param {string} params.roleName 角色名称（可选）
   * @param {number} params.pageNum 页码（可选，默认1）
   * @param {number} params.pageSize 每页数量（可选，默认10）
   * @returns {Promise} 返回角色列表数据
   */
  getRoleList(params = {}) {
    console.log('📋 获取角色列表API-V2请求:', params)
    
    return get('/api/admin/role/list', params)
      .then(response => {
        console.log('📋 角色列表API响应:', response)
        return response
      })
      .catch(error => {
        console.error('📋 获取角色列表失败:', error)
        throw error
      })
  },

  /**
   * 获取菜单权限列表
   * @returns {Promise} 返回菜单权限列表
   */
  getMenuList() {
    console.log('🌲 获取菜单权限列表API-V2请求')

    return get('/api/admin/role/menus')
      .then(response => {
        console.log('🌲 菜单权限列表API响应:', response)
        return response
      })
      .catch(error => {
        console.error('🌲 获取菜单权限列表失败:', error)
        throw error
      })
  },

  /**
   * 获取用户权限菜单（包含子菜单）
   * 这是一个模拟接口，实际应该从后端获取用户的完整菜单权限
   * @returns {Promise} 返回用户权限菜单树
   */
  getUserMenuTree() {
    console.log('🌳 获取用户权限菜单树API-V2请求')

    return get('/api/admin/role/menus')
      .then(response => {
        console.log('🌳 用户权限菜单树API响应:', response)

        // 如果后端支持层级菜单，直接返回
        // 否则需要在前端构建菜单树结构
        if (response.code === '200') {
          // 模拟构建层级菜单结构
          const menuTree = this.buildMenuTree(response.data)
          return {
            ...response,
            data: menuTree
          }
        }

        return response
      })
      .catch(error => {
        console.error('🌳 获取用户权限菜单树失败:', error)
        throw error
      })
  },

  /**
   * 构建菜单树结构（前端处理）
   * 将平级菜单数据构建成树形结构
   */
  buildMenuTree(menuData) {
    // 这里可以根据实际的菜单数据结构来构建树形菜单
    // 目前先返回原数据，后续可以扩展
    return menuData
  },

  /**
   * 获取所有角色（不分页）
   * @returns {Promise} 返回所有角色数据
   */
  getAllRoles() {
    console.log('📋 获取所有角色API-V2请求')
    
    return get('/api/admin/role/all')
      .then(response => {
        console.log('📋 所有角色API响应:', response)
        return response
      })
      .catch(error => {
        console.error('📋 获取所有角色失败:', error)
        throw error
      })
  },

  /**
   * 获取角色详情
   * @param {number} id 角色ID
   * @returns {Promise} 返回角色详情数据
   */
  getRoleDetail(id) {
    console.log('🔍 获取角色详情API-V2请求:', { id })
    
    return get(`/api/admin/role/detail/${id}`)
      .then(response => {
        console.log('🔍 角色详情API响应:', response)
        return response
      })
      .catch(error => {
        console.error('🔍 获取角色详情失败:', error)
        throw error
      })
  },

  /**
   * 新增角色
   * @param {Object} data 角色数据
   * @param {string} data.roleName 角色名称
   * @param {Array} data.ruleIds 权限ID数组
   * @returns {Promise} 返回新增结果
   */
  addRole(data) {
    console.log('➕ 新增角色API-V2请求:', data)
    
    return post('/api/admin/role/add', data)
      .then(response => {
        console.log('➕ 新增角色API响应:', response)
        return response
      })
      .catch(error => {
        console.error('➕ 新增角色失败:', error)
        throw error
      })
  },

  /**
   * 编辑角色
   * @param {Object} data 角色数据
   * @param {number} data.id 角色ID
   * @param {string} data.roleName 角色名称
   * @param {Array} data.ruleIds 权限ID数组
   * @returns {Promise} 返回编辑结果
   */
  editRole(data) {
    console.log('✏️ 编辑角色API-V2请求:', data)
    
    return post('/api/admin/role/edit', data)
      .then(response => {
        console.log('✏️ 编辑角色API响应:', response)
        return response
      })
      .catch(error => {
        console.error('✏️ 编辑角色失败:', error)
        throw error
      })
  },

  /**
   * 删除角色
   * @param {number} id 角色ID
   * @returns {Promise} 返回删除结果
   */
  deleteRole(id) {
    console.log('🗑️ 删除角色API-V2请求:', { id })
    
    return post(`/api/admin/role/delete/${id}`)
      .then(response => {
        console.log('🗑️ 删除角色API响应:', response)
        return response
      })
      .catch(error => {
        console.error('🗑️ 删除角色失败:', error)
        throw error
      })
  }
}
