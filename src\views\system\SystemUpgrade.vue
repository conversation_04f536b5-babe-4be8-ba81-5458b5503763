<!--
  系统升级页面
 系统升级页面布局重构
-->

<template>
  <div class="lb-system-upgrade">
    <TopNav />
    <div class="page-main">
      <!-- 当前版本信息 -->
      <el-card class="current-version-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>当前版本信息</span>
            <LbButton type="primary" @click="checkUpdate">检查更新</LbButton>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="系统版本">{{ currentVersion.version }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ currentVersion.release_time }}</el-descriptions-item>
          <el-descriptions-item label="版本类型">
            <el-tag :type="getVersionTypeColor(currentVersion.type)" size="small">
              {{ getVersionTypeText(currentVersion.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="更新状态">
            <el-tag :type="currentVersion.has_update ? 'warning' : 'success'" size="small">
              {{ currentVersion.has_update ? '有新版本' : '已是最新' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="版本描述" span="2">{{ currentVersion.description }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 版本升级 -->
      <el-card class="upgrade-card" shadow="never" v-if="currentVersion.has_update">
        <template #header>
          <div class="card-header">
            <span>版本升级</span>
          </div>
        </template>
        
        <el-alert
          title="发现新版本"
          type="warning"
          :description="`新版本 ${latestVersion.version} 已发布，建议及时升级以获得最新功能和安全修复。`"
          show-icon
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-descriptions :column="2" border style="margin-bottom: 20px;">
          <el-descriptions-item label="最新版本">{{ latestVersion.version }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ latestVersion.release_time }}</el-descriptions-item>
          <el-descriptions-item label="版本类型">
            <el-tag :type="getVersionTypeColor(latestVersion.type)" size="small">
              {{ getVersionTypeText(latestVersion.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="升级大小">{{ latestVersion.size }}</el-descriptions-item>
          <el-descriptions-item label="更新内容" span="2">{{ latestVersion.description }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="upgrade-actions">
          <LbButton type="primary" @click="startUpgrade" :loading="upgradeLoading">
            立即升级
          </LbButton>
          <LbButton @click="downloadUpdate">
            下载升级包
          </LbButton>
        </div>
      </el-card>
      
      <!-- 升级说明 -->
      <el-card class="help-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>升级说明</span>
          </div>
        </template>
        
        <div class="help-content">
          <h4>升级前准备：</h4>
          <ol>
            <li>备份重要数据和配置文件</li>
            <li>确保服务器磁盘空间充足</li>
            <li>建议在业务低峰期进行升级</li>
            <li>通知相关用户系统维护时间</li>
          </ol>
          
          <h4>升级注意事项：</h4>
          <ul>
            <li>升级过程中请勿关闭浏览器或断开网络</li>
            <li>升级失败时系统会自动回滚到原版本</li>
            <li>升级完成后请清除浏览器缓存</li>
            <li>如遇问题请联系技术支持</li>
          </ul>
        </div>
      </el-card>
    </div>
    
    <!-- 升级进度对话框 -->
    <el-dialog v-model="upgradeVisible" title="系统升级" width="50%" :close-on-click-modal="false">
      <div class="upgrade-progress">
        <el-steps :active="upgradeStep" finish-status="success">
          <el-step title="下载升级包" />
          <el-step title="备份数据" />
          <el-step title="安装更新" />
          <el-step title="重启系统" />
        </el-steps>
        
        <div class="progress-content">
          <el-progress 
            :percentage="upgradeProgress" 
            :status="upgradeStatus"
            :stroke-width="20"
          />
          <p class="progress-text">{{ upgradeText }}</p>
        </div>
      </div>
      
      <template #footer>
        <LbButton @click="cancelUpgrade" :disabled="upgradeStep > 0">取消升级</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 响应式数据
const upgradeLoading = ref(false)
const upgradeVisible = ref(false)
const upgradeStep = ref(0)
const upgradeProgress = ref(0)
const upgradeStatus = ref('')
const upgradeText = ref('')

// 当前版本信息
const currentVersion = reactive({
  version: 'v3.0.0',
  release_time: '2024-01-01',
  type: 'stable',
  description: '系统初始版本，包含基础功能模块',
  has_update: false
})

// 最新版本信息
const latestVersion = reactive({
  version: 'v3.1.0',
  release_time: '2024-01-15',
  type: 'stable',
  size: '25.6MB',
  description: '新增用户管理功能，修复已知问题，优化系统性能'
})

// 方法
const checkUpdate = async () => {
  try {
    const response = await fetch('/api/system/upgrade/check-update')
    const result = await response.json()
    
    if (result.code === 200) {
      if (result.data.has_update) {
        currentVersion.has_update = true
        Object.assign(latestVersion, result.data.latest_version)
        ElMessage.success('发现新版本')
      } else {
        ElMessage.info('当前已是最新版本')
      }
    } else {
      ElMessage.error(result.meg || '检查更新失败')
    }
  } catch (error) {
    console.error('检查更新失败:', error)
    ElMessage.error('检查更新失败')
  }
}

const startUpgrade = async () => {
  try {
    await ElMessageBox.confirm(
      '升级过程中系统将暂时不可用，确定要开始升级吗？',
      '升级确认',
      {
        confirmButtonText: '开始升级',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    upgradeVisible.value = true
    upgradeStep.value = 0
    upgradeProgress.value = 0
    upgradeStatus.value = ''
    upgradeText.value = '准备下载升级包...'
    
    // 模拟升级过程
    await simulateUpgrade()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('升级失败:', error)
      ElMessage.error('升级失败')
    }
  }
}

const simulateUpgrade = async () => {
  // 步骤1：下载升级包
  upgradeStep.value = 1
  upgradeText.value = '正在下载升级包...'
  for (let i = 0; i <= 100; i += 10) {
    upgradeProgress.value = i
    await new Promise(resolve => setTimeout(resolve, 200))
  }
  
  // 步骤2：备份数据
  upgradeStep.value = 2
  upgradeText.value = '正在备份数据...'
  upgradeProgress.value = 0
  for (let i = 0; i <= 100; i += 20) {
    upgradeProgress.value = i
    await new Promise(resolve => setTimeout(resolve, 300))
  }
  
  // 步骤3：安装更新
  upgradeStep.value = 3
  upgradeText.value = '正在安装更新...'
  upgradeProgress.value = 0
  for (let i = 0; i <= 100; i += 15) {
    upgradeProgress.value = i
    await new Promise(resolve => setTimeout(resolve, 250))
  }
  
  // 步骤4：重启系统
  upgradeStep.value = 4
  upgradeText.value = '升级完成，正在重启系统...'
  upgradeProgress.value = 100
  upgradeStatus.value = 'success'
  
  setTimeout(() => {
    upgradeVisible.value = false
    ElMessage.success('系统升级成功')
    currentVersion.version = latestVersion.version
    currentVersion.has_update = false
  }, 2000)
}

const downloadUpdate = () => {
  ElMessage.info('开始下载升级包...')
  // 这里可以添加下载逻辑
}

const cancelUpgrade = () => {
  upgradeVisible.value = false
}

const getVersionTypeColor = (type) => {
  const typeMap = {
    'stable': 'success',
    'beta': 'warning',
    'alpha': 'danger'
  }
  return typeMap[type] || 'info'
}

const getVersionTypeText = (type) => {
  const typeMap = {
    'stable': '稳定版',
    'beta': '测试版',
    'alpha': '内测版'
  }
  return typeMap[type] || '未知'
}

// 生命周期
onMounted(() => {
  checkUpdate()
})
</script>

<style scoped>
.lb-system-upgrade {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.current-version-card,
.upgrade-card,
.help-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upgrade-actions {
  display: flex;
  gap: 10px;
}

.upgrade-progress {
  padding: 20px 0;
}

.progress-content {
  margin-top: 30px;
  text-align: center;
}

.progress-text {
  margin-top: 15px;
  color: #606266;
  font-size: 14px;
}

.help-content {
  color: #606266;
  line-height: 1.6;
}

.help-content h4 {
  color: #303133;
  margin: 20px 0 10px 0;
}

.help-content ol,
.help-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.help-content li {
  margin: 8px 0;
}

@media (max-width: 768px) {
  .lb-system-upgrade {
    padding: 10px;
  }
  
  .upgrade-actions {
    flex-direction: column;
  }
}
</style>
