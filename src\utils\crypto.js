/**
 * 加密工具函数
 * 提供各种加密和解密功能
 */

import CryptoJS from 'crypto-js'

/**
 * MD5加密
 * @param {string} str 要加密的字符串
 * @returns {string} MD5加密后的字符串（大写）
 */
export function md5(str) {
  if (!str) return ''
  return CryptoJS.MD5(str).toString().toUpperCase()
}

/**
 * SHA256加密
 * @param {string} str 要加密的字符串
 * @returns {string} SHA256加密后的字符串
 */
export function sha256(str) {
  if (!str) return ''
  return CryptoJS.SHA256(str).toString()
}

/**
 * Base64编码
 * @param {string} str 要编码的字符串
 * @returns {string} Base64编码后的字符串
 */
export function base64Encode(str) {
  if (!str) return ''
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(str))
}

/**
 * Base64解码
 * @param {string} str 要解码的字符串
 * @returns {string} Base64解码后的字符串
 */
export function base64Decode(str) {
  if (!str) return ''
  return CryptoJS.enc.Base64.parse(str).toString(CryptoJS.enc.Utf8)
}

/**
 * AES加密
 * @param {string} str 要加密的字符串
 * @param {string} key 加密密钥
 * @returns {string} AES加密后的字符串
 */
export function aesEncrypt(str, key) {
  if (!str || !key) return ''
  return CryptoJS.AES.encrypt(str, key).toString()
}

/**
 * AES解密
 * @param {string} str 要解密的字符串
 * @param {string} key 解密密钥
 * @returns {string} AES解密后的字符串
 */
export function aesDecrypt(str, key) {
  if (!str || !key) return ''
  try {
    const bytes = CryptoJS.AES.decrypt(str, key)
    return bytes.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    console.error('AES解密失败:', error)
    return ''
  }
}

/**
 * 生成随机字符串
 * @param {number} length 字符串长度
 * @returns {string} 随机字符串
 */
export function generateRandomString(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 验证密码强度
 * @param {string} password 密码
 * @returns {Object} 验证结果 { level: number, message: string }
 */
export function validatePasswordStrength(password) {
  if (!password) {
    return { level: 0, message: '密码不能为空' }
  }

  let level = 0
  let message = '密码强度：'

  // 长度检查
  if (password.length >= 8) level++
  if (password.length >= 12) level++

  // 包含数字
  if (/\d/.test(password)) level++

  // 包含小写字母
  if (/[a-z]/.test(password)) level++

  // 包含大写字母
  if (/[A-Z]/.test(password)) level++

  // 包含特殊字符
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) level++

  switch (level) {
    case 0:
    case 1:
      message += '弱'
      break
    case 2:
    case 3:
      message += '中等'
      break
    case 4:
    case 5:
      message += '强'
      break
    case 6:
      message += '很强'
      break
    default:
      message += '未知'
  }

  return { level, message }
}
