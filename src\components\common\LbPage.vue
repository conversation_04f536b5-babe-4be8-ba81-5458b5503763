<!--
  LbPage 通用分页组件

  支持分页、批量操作等功能
-->

<template>
  <div class="lb-page">
    <!-- 批量操作区域 -->
    <div v-if="batch && selectedCount > 0" class="batch-operations">
      <span class="batch-info">已选择 {{ selectedCount }} 项</span>
      <slot name="batch-operations">
        <lb-button size="small" type="danger" @click="handleBatchDelete">
          批量删除
        </lb-button>
      </slot>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="currentPageSize"
        :page-sizes="pageSizes"
        :small="small"
        :disabled="disabled"
        :background="background"
        :total="total"
        :layout="layout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import LbButton from './LbButton.vue'

// Props定义
const props = defineProps({
  page: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  total: {
    type: Number,
    default: 0
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  background: {
    type: Boolean,
    default: true
  },
  small: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  batch: {
    type: Boolean,
    default: false
  },
  selectedCount: {
    type: Number,
    default: 0
  }
})

// Emits定义
const emit = defineEmits([
  'handleSizeChange',
  'handleCurrentChange',
  'batchDelete',
  'update:page',
  'update:pageSize'
])

// 计算属性
const currentPage = computed({
  get: () => props.page,
  set: (value) => emit('update:page', value)
})

const currentPageSize = computed({
  get: () => props.pageSize,
  set: (value) => emit('update:pageSize', value)
})

// 方法
const handleSizeChange = (size) => {
  emit('handleSizeChange', size)
}

const handleCurrentChange = (page) => {
  emit('handleCurrentChange', page)
}

const handleBatchDelete = () => {
  emit('batchDelete')
}
</script>

<style scoped>
.lb-page {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* 批量操作区域 */
.batch-operations {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px 16px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  width: 100%;
}

.batch-info {
  color: #409eff;
  font-size: 14px;
  margin-right: 16px;
}

/* 分页组件包装器 */
.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

/* 分页组件样式调整 */
.pagination-wrapper :deep(.el-pagination) {
  padding: 2px 5px;
}

.pagination-wrapper :deep(.el-pagination .btn-prev),
.pagination-wrapper :deep(.el-pagination .btn-next) {
  padding: 0 4px;
}

.pagination-wrapper :deep(.el-pagination .el-pager li) {
  min-width: 30px;
  height: 28px;
  line-height: 28px;
}

/* 小尺寸分页样式 */
.pagination-wrapper :deep(.el-pagination.is-small .el-pager li) {
  min-width: 24px;
  height: 24px;
  line-height: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lb-page {
    align-items: center;
  }
  
  .pagination-wrapper {
    justify-content: center;
  }
  
  .pagination-wrapper :deep(.el-pagination) {
    flex-wrap: wrap;
  }
  
  .batch-operations {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .batch-info {
    margin-bottom: 8px;
    margin-right: 0;
  }
}

/*  的间距 */
.lb-page {
  margin-top: 15px;
}

/* 总数显示样式 */
.pagination-wrapper :deep(.el-pagination__total) {
  color: #606266;
  font-weight: normal;
}

/* 页码选择器样式 */
.pagination-wrapper :deep(.el-pagination__sizes) {
  margin-right: 10px;
}

/* 跳转输入框样式 */
.pagination-wrapper :deep(.el-pagination__jump) {
  margin-left: 10px;
}
</style>
