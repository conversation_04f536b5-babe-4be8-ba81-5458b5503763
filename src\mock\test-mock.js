/**
 * Mock拦截测试
 * 验证Mock.js是否能正确拦截请求
 */

import Mock from 'mockjs'

console.log('🧪 开始Mock拦截测试...')
console.log('📦 Mock.js版本:', Mock.version || 'unknown')

// 设置Mock
Mock.setup({
  timeout: '200-500'
})

// 测试简单的API拦截
Mock.mock('/api/test', 'get', () => {
  console.log('🎯 测试API被调用了！')
  return {
    code: 200,
    message: '测试成功',
    data: {
      timestamp: Date.now(),
      test: 'Mock拦截正常工作'
    }
  }
})

// 测试服务项目列表API
Mock.mock('/api/service/list', 'get', () => {
  console.log('📋 服务项目列表API被Mock拦截！')
  return {
    code: 200,
    message: '操作成功',
    data: {
      list: [
        {
          id: 1,
          title: 'Mock测试服务项目',
          price: 199.99,
          status: 1,
          create_time: '2024-01-01 10:00:00'
        }
      ],
      total: 1,
      page: 1,
      pageSize: 10
    }
  }
})

// 测试统计API
Mock.mock('/api/service/statistics', 'get', () => {
  console.log('📊 统计API被Mock拦截！')
  return {
    code: 200,
    message: '操作成功',
    data: {
      totalServices: 100,
      activeServices: 80,
      monthlyRevenue: 50000
    }
  }
})

console.log('✅ Mock拦截测试配置完成')
console.log('🔧 已注册的Mock API:')
console.log('   - GET /api/test')
console.log('   - GET /api/service/list')
console.log('   - GET /api/service/statistics')

// 导出测试函数
export const testMockInterception = async () => {
  console.log('🧪 开始测试Mock拦截...')
  
  try {
    // 测试基础API
    const testResponse = await fetch('/api/test')
    const testData = await testResponse.json()
    console.log('✅ 基础测试API成功:', testData)
    
    // 测试服务项目API
    const serviceResponse = await fetch('/api/service/list')
    const serviceData = await serviceResponse.json()
    console.log('✅ 服务项目API成功:', serviceData)
    
    return true
  } catch (error) {
    console.error('❌ Mock拦截测试失败:', error)
    return false
  }
}

// 自动运行测试
setTimeout(() => {
  testMockInterception()
}, 2000)
