/**
 * 工具样式类
 * 提供常用的原子级CSS类，用于快速布局和样式调整
 * 
 * 包含：
 * - 布局工具类
 * - 间距工具类
 * - 文本工具类
 * - 显示工具类
 * - 位置工具类
 * - 边框工具类
 * - 颜色工具类
 */

/* ===== 布局工具类 ===== */

/* Flexbox布局 */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-evenly {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-grow-0 {
  flex-grow: 0;
}

.flex-grow-1 {
  flex-grow: 1;
}

/* 对齐方式 */
.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.items-baseline {
  align-items: baseline;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

/* Grid布局 */
.grid {
  display: grid;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
.gap-5 { gap: var(--spacing-xl); }
.gap-6 { gap: var(--spacing-xxl); }

/* ===== 间距工具类 ===== */

/* 外边距 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }
.m-6 { margin: var(--spacing-xxl); }
.m-auto { margin: auto; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
.mt-6 { margin-top: var(--spacing-xxl); }
.mt-auto { margin-top: auto; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }
.mr-6 { margin-right: var(--spacing-xxl); }
.mr-auto { margin-right: auto; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
.mb-6 { margin-bottom: var(--spacing-xxl); }
.mb-auto { margin-bottom: auto; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }
.ml-6 { margin-left: var(--spacing-xxl); }
.ml-auto { margin-left: auto; }

.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-2 { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-3 { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-4 { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-5 { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }
.mx-6 { margin-left: var(--spacing-xxl); margin-right: var(--spacing-xxl); }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-2 { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-3 { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-4 { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-5 { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }
.my-6 { margin-top: var(--spacing-xxl); margin-bottom: var(--spacing-xxl); }
.my-auto { margin-top: auto; margin-bottom: auto; }

/* 内边距 */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }
.p-6 { padding: var(--spacing-xxl); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-xs); }
.pt-2 { padding-top: var(--spacing-sm); }
.pt-3 { padding-top: var(--spacing-md); }
.pt-4 { padding-top: var(--spacing-lg); }
.pt-5 { padding-top: var(--spacing-xl); }
.pt-6 { padding-top: var(--spacing-xxl); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--spacing-xs); }
.pr-2 { padding-right: var(--spacing-sm); }
.pr-3 { padding-right: var(--spacing-md); }
.pr-4 { padding-right: var(--spacing-lg); }
.pr-5 { padding-right: var(--spacing-xl); }
.pr-6 { padding-right: var(--spacing-xxl); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pb-3 { padding-bottom: var(--spacing-md); }
.pb-4 { padding-bottom: var(--spacing-lg); }
.pb-5 { padding-bottom: var(--spacing-xl); }
.pb-6 { padding-bottom: var(--spacing-xxl); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--spacing-xs); }
.pl-2 { padding-left: var(--spacing-sm); }
.pl-3 { padding-left: var(--spacing-md); }
.pl-4 { padding-left: var(--spacing-lg); }
.pl-5 { padding-left: var(--spacing-xl); }
.pl-6 { padding-left: var(--spacing-xxl); }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-2 { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-3 { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-4 { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-5 { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }
.px-6 { padding-left: var(--spacing-xxl); padding-right: var(--spacing-xxl); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-2 { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-3 { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-4 { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-5 { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }
.py-6 { padding-top: var(--spacing-xxl); padding-bottom: var(--spacing-xxl); }

/* ===== 文本工具类 ===== */

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 文本颜色 */
.text-primary { color: var(--color-primary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-danger { color: var(--color-danger); }
.text-info { color: var(--color-info); }
.text-muted { color: var(--color-text-secondary); }
.text-placeholder { color: var(--color-text-placeholder); }
.text-disabled { color: var(--color-text-disabled); }

/* 字体大小 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-xxl); }

/* 字体粗细 */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 文本装饰 */
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

/* 文本转换 */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* 文本省略 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 换行控制 */
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

/* ===== 显示工具类 ===== */

.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }
.invisible { visibility: hidden; }
.visible { visibility: visible; }

/* ===== 位置工具类 ===== */

.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* ===== 边框工具类 ===== */

.border { border: 1px solid var(--border-color-base); }
.border-0 { border: 0; }
.border-t { border-top: 1px solid var(--border-color-base); }
.border-r { border-right: 1px solid var(--border-color-base); }
.border-b { border-bottom: 1px solid var(--border-color-base); }
.border-l { border-left: 1px solid var(--border-color-base); }

.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-full { border-radius: 50%; }

/* ===== 阴影工具类 ===== */

.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--box-shadow-light); }
.shadow { box-shadow: var(--box-shadow-base); }
.shadow-lg { box-shadow: var(--box-shadow-dark); }

/* ===== 背景工具类 ===== */

.bg-transparent { background-color: transparent; }
.bg-white { background-color: #ffffff; }
.bg-primary { background-color: var(--color-primary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-danger { background-color: var(--color-danger); }
.bg-info { background-color: var(--color-info); }

/* ===== 宽高工具类 ===== */

.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-screen { width: 100vw; }

.h-full { height: 100%; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }
.h-screen { height: 100vh; }

.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }

.max-w-none { max-width: none; }
.max-w-full { max-width: 100%; }
.max-h-none { max-height: none; }
.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }

/* ===== 溢出工具类 ===== */

.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

/* ===== 光标工具类 ===== */

.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

/* ===== 用户选择工具类 ===== */

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* ===== 指针事件工具类 ===== */

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* ===== 过渡动画工具类 ===== */

.transition-none { transition: none; }
.transition-all { transition: var(--transition-base); }
.transition-colors { transition: var(--transition-color); }
.transition-opacity { transition: var(--transition-fade); }
.transition-shadow { transition: var(--transition-box-shadow); }
.transition-transform { transition: transform var(--transition-duration-base) var(--transition-function-ease-in-out); }

/* ===== 变换工具类 ===== */

.transform { transform: translateZ(0); }
.scale-0 { transform: scale(0); }
.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.scale-125 { transform: scale(1.25); }
.scale-150 { transform: scale(1.5); }

.rotate-0 { transform: rotate(0deg); }
.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.rotate-270 { transform: rotate(270deg); }

/* ===== 透明度工具类 ===== */

.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }
