/**
 * Service模块统一设计系统
 * 基于service目录页面UI/UX分析结果创建的统一样式规范
 * 
 * 包含：
 * - 设计令牌（Design Tokens）
 * - 组件样式规范
 * - 布局系统
 * - 交互状态
 * 
 * 使用方式：
 * import '@/styles/service-design-system.css'
 */

/* ===== 设计令牌 (Design Tokens) ===== */

:root {
  /* === 服务模块专用颜色系统 === */
  
  /* 主色调 - 基于#609beb */
  --service-primary: #609beb;
  --service-primary-hover: #5689d3;
  --service-primary-active: #4c77bb;
  --service-primary-light: #73a9ee;
  --service-primary-lighter: #e8f1fb;
  
  /* 功能色 */
  --service-success: #67c23a;
  --service-success-hover: #529b2e;
  --service-warning: #e6a23c;
  --service-warning-hover: #b88230;
  --service-danger: #fe6c6f;
  --service-danger-hover: #cb5659;
  --service-info: #909399;
  --service-info-hover: #73767a;
  
  /* 背景色系统 */
  --service-bg-page: #f5f7fa;
  --service-bg-container: #ffffff;
  --service-bg-search: #f8f9fa;
  --service-bg-table-header: #f5f7fa;
  --service-bg-hover: #f8f9fa;
  
  /* 文字颜色 */
  --service-text-primary: #424753;
  --service-text-regular: #606266;
  --service-text-secondary: #909399;
  --service-text-placeholder: #aaafbb;
  --service-text-disabled: #c0c4cc;
  
  /* 边框颜色 */
  --service-border-base: #dcdfe6;
  --service-border-light: #e4e7ed;
  --service-border-lighter: #ebeef5;
  
  /* === 尺寸系统 === */
  
  /* 圆角 */
  --service-radius-container: 8px;
  --service-radius-form: 6px;
  --service-radius-input: 4px;
  --service-radius-button: 4px;
  
  /* 间距 */
  --service-spacing-xs: 4px;
  --service-spacing-sm: 8px;
  --service-spacing-md: 12px;
  --service-spacing-lg: 16px;
  --service-spacing-xl: 20px;
  --service-spacing-xxl: 24px;
  
  /* 阴影 */
  --service-shadow-container: 0 2px 10px rgba(0, 0, 0, 0.1);
  --service-shadow-table: 0 2px 8px rgba(0, 0, 0, 0.1);
  --service-shadow-form: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --service-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* 字体 */
  --service-font-size-xs: 12px;
  --service-font-size-sm: 13px;
  --service-font-size-base: 14px;
  --service-font-size-md: 16px;
  --service-font-size-lg: 18px;
  --service-font-weight-normal: 400;
  --service-font-weight-medium: 500;
  --service-font-weight-semibold: 600;
  
  /* 组件高度 */
  --service-height-input: 32px;
  --service-height-button: 32px;
  --service-height-table-header: 48px;
  --service-height-table-row: 40px;
  
  /* 过渡动画 */
  --service-transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --service-transition-fast: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* ===== 基础布局系统 ===== */

/* 页面容器 */
.service-page {
  padding: 0;
  min-height: calc(100vh - 60px);
  background-color: var(--service-bg-page);
}

/* 内容容器 */
.service-content {
  background: var(--service-bg-container);
  border-radius: var(--service-radius-container);
  padding: var(--service-spacing-xl);
  box-shadow: var(--service-shadow-container);
  margin-bottom: var(--service-spacing-xl);
}

/* 搜索表单容器 */
.service-search {
  padding: var(--service-spacing-xl);
  background: var(--service-bg-search);
  border-radius: var(--service-radius-form);
  margin-bottom: var(--service-spacing-xl);
}

/* 表格容器 */
.service-table {
  background: var(--service-bg-container);
  border-radius: var(--service-radius-container);
  overflow: hidden;
  box-shadow: var(--service-shadow-table);
  margin-bottom: var(--service-spacing-xl);
}

/* ===== 按钮组件系统 ===== */

/* 基础按钮 */
.service-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--service-spacing-xs);
  padding: var(--service-spacing-sm) var(--service-spacing-lg);
  font-size: var(--service-font-size-base);
  font-weight: var(--service-font-weight-medium);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--service-radius-button);
  transition: var(--service-transition-base);
  height: var(--service-height-button);
  min-width: 64px;
}

/* 主要按钮 */
.service-btn--primary {
  color: #ffffff;
  background-color: var(--service-primary);
  border-color: var(--service-primary);
}

.service-btn--primary:hover {
  background-color: var(--service-primary-hover);
  border-color: var(--service-primary-hover);
}

/* 成功按钮 */
.service-btn--success {
  color: #ffffff;
  background-color: var(--service-success);
  border-color: var(--service-success);
}

.service-btn--success:hover {
  background-color: var(--service-success-hover);
  border-color: var(--service-success-hover);
}

/* 危险按钮 */
.service-btn--danger {
  color: #ffffff;
  background-color: var(--service-danger);
  border-color: var(--service-danger);
}

.service-btn--danger:hover {
  background-color: var(--service-danger-hover);
  border-color: var(--service-danger-hover);
}

/* 默认按钮 */
.service-btn--default {
  color: var(--service-text-regular);
  background-color: var(--service-bg-container);
  border-color: var(--service-border-base);
}

.service-btn--default:hover {
  color: var(--service-primary);
  border-color: var(--service-primary);
}

/* 按钮尺寸 */
.service-btn--small {
  padding: 4px var(--service-spacing-sm);
  font-size: var(--service-font-size-sm);
  height: 24px;
  min-width: 48px;
}

.service-btn--large {
  padding: var(--service-spacing-md) var(--service-spacing-xl);
  font-size: var(--service-font-size-md);
  height: 40px;
  min-width: 80px;
}

/* 按钮组 */
.service-btn-group {
  display: flex;
  gap: var(--service-spacing-sm);
  align-items: center;
}

/* ===== 表单组件系统 ===== */

/* 表单组 */
.service-form-group {
  margin-bottom: var(--service-spacing-lg);
}

/* 表单标签 */
.service-form-label {
  display: block;
  margin-bottom: var(--service-spacing-xs);
  font-size: var(--service-font-size-base);
  font-weight: var(--service-font-weight-medium);
  color: var(--service-text-regular);
  line-height: 1.5;
}

.service-form-label--required::after {
  content: " *";
  color: var(--service-danger);
}

/* 表单控件 */
.service-form-control {
  display: block;
  width: 100%;
  padding: var(--service-spacing-sm) var(--service-spacing-md);
  font-size: var(--service-font-size-base);
  line-height: 1.5;
  color: var(--service-text-primary);
  background-color: var(--service-bg-container);
  border: 1px solid var(--service-border-base);
  border-radius: var(--service-radius-input);
  transition: var(--service-transition-base);
  height: var(--service-height-input);
}

.service-form-control:focus {
  outline: none;
  border-color: var(--service-primary);
  box-shadow: 0 0 0 2px rgba(96, 155, 235, 0.2);
}

.service-form-control::placeholder {
  color: var(--service-text-placeholder);
}

/* 表单提示文字 */
.service-form-text {
  margin-top: var(--service-spacing-xs);
  font-size: var(--service-font-size-sm);
  color: var(--service-text-secondary);
}

/* 表单错误提示 */
.service-form-error {
  margin-top: var(--service-spacing-xs);
  font-size: var(--service-font-size-sm);
  color: var(--service-danger);
}

/* 内联表单 */
.service-form--inline {
  display: flex;
  align-items: center;
  gap: var(--service-spacing-lg);
  flex-wrap: wrap;
}

.service-form--inline .service-form-group {
  margin-bottom: 0;
  flex: 0 0 auto;
}

/* ===== 表格组件系统 ===== */

/* 表格样式重写 */
.service-table .el-table {
  border: 1px solid var(--service-border-lighter);
  border-radius: var(--service-radius-container);
}

.service-table .el-table__header-wrapper th {
  background-color: var(--service-bg-table-header) !important;
  color: var(--service-text-regular) !important;
  font-size: var(--service-font-size-md) !important;
  font-weight: var(--service-font-weight-semibold) !important;
  padding: 15px 8px !important;
  border-bottom: 1px solid var(--service-border-lighter) !important;
}

.service-table .el-table__body-wrapper td {
  font-size: var(--service-font-size-base) !important;
  padding: var(--service-spacing-md) 8px !important;
  color: var(--service-text-primary) !important;
  border-bottom: 1px solid var(--service-border-lighter) !important;
}

.service-table .el-table__row:hover > td {
  background-color: var(--service-bg-hover) !important;
}

/* 表格操作按钮组 */
.service-table-actions {
  display: flex;
  gap: var(--service-spacing-xs);
  align-items: center;
  flex-wrap: wrap;
}

/* ===== 状态标签系统 ===== */

.service-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px var(--service-spacing-sm);
  font-size: var(--service-font-size-xs);
  font-weight: var(--service-font-weight-medium);
  line-height: 1;
  border-radius: var(--service-radius-input);
  border: 1px solid transparent;
}

.service-tag--success {
  color: var(--service-success);
  background-color: rgba(103, 194, 58, 0.1);
  border-color: rgba(103, 194, 58, 0.2);
}

.service-tag--warning {
  color: var(--service-warning);
  background-color: rgba(230, 162, 60, 0.1);
  border-color: rgba(230, 162, 60, 0.2);
}

.service-tag--danger {
  color: var(--service-danger);
  background-color: rgba(254, 108, 111, 0.1);
  border-color: rgba(254, 108, 111, 0.2);
}

.service-tag--info {
  color: var(--service-info);
  background-color: rgba(144, 147, 153, 0.1);
  border-color: rgba(144, 147, 153, 0.2);
}

/* ===== 对话框系统 ===== */

.service-dialog .el-dialog {
  border-radius: var(--service-radius-container);
  box-shadow: var(--service-shadow-form);
}

.service-dialog .el-dialog__header {
  padding: var(--service-spacing-xl) var(--service-spacing-xxl) var(--service-spacing-lg);
  border-bottom: 1px solid var(--service-border-lighter);
}

.service-dialog .el-dialog__title {
  font-size: var(--service-font-size-lg);
  font-weight: var(--service-font-weight-semibold);
  color: var(--service-text-primary);
}

.service-dialog .el-dialog__body {
  padding: var(--service-spacing-xl) var(--service-spacing-xxl);
}

.service-dialog .el-dialog__footer {
  padding: var(--service-spacing-lg) var(--service-spacing-xxl) var(--service-spacing-xl);
  border-top: 1px solid var(--service-border-lighter);
  text-align: right;
}

/* ===== 上传组件系统 ===== */

.service-upload .el-upload--picture-card {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--service-border-base);
  border-radius: var(--service-radius-form);
  cursor: pointer;
  transition: var(--service-transition-base);
}

.service-upload .el-upload--picture-card:hover {
  border-color: var(--service-primary);
}

.service-upload .el-upload-list--picture-card .el-upload-list__item {
  width: 120px;
  height: 120px;
  border-radius: var(--service-radius-form);
}

/* 上传进度 */
.service-upload-progress {
  margin-top: var(--service-spacing-sm);
  padding: var(--service-spacing-sm);
  background-color: var(--service-bg-search);
  border-radius: var(--service-radius-input);
  border: 1px solid var(--service-border-light);
}

.service-upload-progress p {
  margin: var(--service-spacing-xs) 0 0 0;
  font-size: var(--service-font-size-sm);
  color: var(--service-text-regular);
  text-align: center;
}

/* ===== 时间显示组件 ===== */

.service-time-column {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.service-time-column__date {
  font-size: var(--service-font-size-base);
  color: var(--service-text-primary);
  line-height: 1.4;
  margin: 0;
}

.service-time-column__time {
  font-size: var(--service-font-size-sm);
  color: var(--service-text-secondary);
  line-height: 1.4;
  margin: 0;
}

/* ===== 价格显示组件 ===== */

.service-price {
  font-size: var(--service-font-size-base);
  font-weight: var(--service-font-weight-medium);
  color: var(--service-danger);
}

.service-price--large {
  font-size: var(--service-font-size-lg);
}

.service-price::before {
  content: "¥";
}

/* ===== 树形结构组件 ===== */

.service-tree-node {
  display: flex;
  align-items: center;
}

.service-tree-arrow {
  margin-right: var(--service-spacing-xs);
  transition: transform 0.2s ease-in-out;
  cursor: pointer;
  color: var(--service-text-secondary);
}

.service-tree-arrow--expanded {
  transform: rotate(90deg);
}

.service-tree-indent {
  display: inline-block;
  width: 19px;
  height: 1px;
}

/* 父级行样式 */
.service-tree-parent {
  font-weight: var(--service-font-weight-medium);
}

/* 子级行样式 */
.service-tree-child {
  background-color: rgba(248, 249, 250, 0.5);
}

/* ===== 工具提示组件 ===== */

.service-tooltip {
  font-size: var(--service-font-size-sm);
  color: var(--service-text-secondary);
  margin-top: var(--service-spacing-xs);
  line-height: 1.4;
}

.service-tooltip--info {
  color: var(--service-info);
}

.service-tooltip--warning {
  color: var(--service-warning);
}

/* ===== 响应式设计 ===== */

/* 平板设备 */
@media (max-width: 768px) {
  .service-content {
    padding: var(--service-spacing-md);
  }

  .service-search {
    padding: var(--service-spacing-md);
  }

  .service-form--inline {
    flex-direction: column;
    align-items: stretch;
  }

  .service-btn-group {
    flex-wrap: wrap;
  }

  .service-table-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .service-table-actions .service-btn {
    width: 100%;
    justify-content: center;
  }

  .service-dialog .el-dialog__header,
  .service-dialog .el-dialog__body,
  .service-dialog .el-dialog__footer {
    padding-left: var(--service-spacing-lg);
    padding-right: var(--service-spacing-lg);
  }
}

/* 手机设备 */
@media (max-width: 480px) {
  .service-page {
    padding: var(--service-spacing-sm);
  }

  .service-content {
    padding: var(--service-spacing-sm);
  }

  .service-search {
    padding: var(--service-spacing-sm);
  }

  .service-btn {
    width: 100%;
    justify-content: center;
  }

  .service-btn-group {
    flex-direction: column;
  }

  .service-dialog .el-dialog {
    width: 95% !important;
    margin: var(--service-spacing-sm);
  }

  .service-upload .el-upload--picture-card {
    width: 100px;
    height: 100px;
  }
}
